<?php
// Test script for Google2FA

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include Composer autoloader
require_once __DIR__ . '/vendor/autoload.php';

// Try to create a Google2FA instance
try {
    echo "Testing Google2FA class...<br>";
    
    // Check if the class file exists
    $classFile = __DIR__ . '/vendor/pragmarx/google2fa/src/Google2FA.php';
    echo "Class file exists: " . (file_exists($classFile) ? 'Yes' : 'No') . "<br>";
    
    // Try to instantiate the class
    $google2fa = new \PragmaRX\Google2FA\Google2FA();
    echo "Successfully created Google2FA instance!<br>";
    
    // Generate a test secret key
    $secretKey = $google2fa->generateSecretKey();
    echo "Generated secret key: " . $secretKey . "<br>";
    
    echo "<br>Test completed successfully!";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
}
?> 