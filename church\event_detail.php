<?php
require_once __DIR__ . '/config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Get event ID
$event_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$event_id) {
    header("Location: events.php");
    exit();
}

// Get event details
$stmt = $conn->prepare("
    SELECT e.*,
           (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'attending') as attending_count,
           (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'maybe') as maybe_count,
           (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'not_attending') as not_attending_count
    FROM events e
    WHERE e.id = ? AND e.is_active = 1
");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header("Location: events.php");
    exit();
}

// Check if event is in the past
$is_past = strtotime($event['event_date']) < time();
$is_full = $event['max_attendees'] && $event['attending_count'] >= $event['max_attendees'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($event['title']) ?> - <?php echo get_organization_name(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .event-hero {
            background: linear-gradient(135deg, #6f42c1, #5a32a3);
            color: white;
            padding: 80px 0;
        }
        .event-date-large {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .event-date-large .day {
            font-size: 3rem;
            font-weight: bold;
            line-height: 1;
        }
        .event-date-large .month {
            font-size: 1.2rem;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        .event-date-large .time {
            font-size: 1rem;
            opacity: 0.9;
            margin-top: 10px;
        }
        .info-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 15px;
        }
        .stat-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 50px;
            backdrop-filter: blur(10px);
        }
        .rsvp-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <!-- Event Hero Section -->
    <div class="event-hero position-relative">
        <button class="back-btn" onclick="history.back()">
            <i class="bi bi-arrow-left"></i> Back
        </button>
        
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-4 mb-3"><?= htmlspecialchars($event['title']) ?></h1>
                    
                    <?php if ($event['location']): ?>
                        <p class="lead mb-3">
                            <i class="bi bi-geo-alt"></i> <?= htmlspecialchars($event['location']) ?>
                        </p>
                    <?php endif; ?>
                    
                    <div class="d-flex flex-wrap gap-2">
                        <?php if ($is_past): ?>
                            <span class="badge bg-secondary fs-6 px-3 py-2">Past Event</span>
                        <?php elseif ($is_full): ?>
                            <span class="badge bg-warning fs-6 px-3 py-2">Event Full</span>
                        <?php else: ?>
                            <span class="badge bg-success fs-6 px-3 py-2">Registration Open</span>
                        <?php endif; ?>
                        
                        <?php if ($event['max_attendees']): ?>
                            <span class="badge bg-info fs-6 px-3 py-2">
                                Capacity: <?= $event['max_attendees'] ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="event-date-large">
                        <div class="day"><?= date('j', strtotime($event['event_date'])) ?></div>
                        <div class="month"><?= date('F Y', strtotime($event['event_date'])) ?></div>
                        <div class="time"><?= date('g:i A', strtotime($event['event_date'])) ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Details Section -->
    <div class="container my-5">
        <div class="row">
            <!-- Main Content -->
            <div class="col-md-8">
                <div class="info-card card mb-4">
                    <div class="card-body">
                        <h3 class="card-title">About This Event</h3>
                        <p class="card-text"><?= nl2br(htmlspecialchars($event['description'])) ?></p>
                    </div>
                </div>
                
                <!-- Event Information -->
                <div class="info-card card">
                    <div class="card-body">
                        <h3 class="card-title">Event Information</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="bi bi-calendar-event text-primary"></i> Date & Time</h6>
                                <p><?= date('l, F j, Y \a\t g:i A', strtotime($event['event_date'])) ?></p>
                            </div>
                            
                            <?php if ($event['location']): ?>
                            <div class="col-md-6">
                                <h6><i class="bi bi-geo-alt text-primary"></i> Location</h6>
                                <p><?= htmlspecialchars($event['location']) ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($event['max_attendees']): ?>
                            <div class="col-md-6">
                                <h6><i class="bi bi-people text-primary"></i> Capacity</h6>
                                <p><?= $event['max_attendees'] ?> people</p>
                            </div>
                            <?php endif; ?>
                            
                            <div class="col-md-6">
                                <h6><i class="bi bi-person-check text-primary"></i> Organizer</h6>
                                <p><?php echo get_organization_name(); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- RSVP Stats -->
                <div class="stat-card mb-4">
                    <div class="stat-number"><?= $event['attending_count'] ?></div>
                    <div>People Attending</div>
                    
                    <?php if ($event['maybe_count'] > 0): ?>
                        <hr style="border-color: rgba(255,255,255,0.3);">
                        <div class="stat-number"><?= $event['maybe_count'] ?></div>
                        <div>Maybe Attending</div>
                    <?php endif; ?>
                </div>
                
                <!-- RSVP Section -->
                <?php if (!$is_past): ?>
                    <div class="rsvp-section">
                        <h4 class="mb-3">Join This Event</h4>
                        
                        <?php if ($is_full): ?>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                This event is currently full. You can still register to be added to the waitlist.
                            </div>
                        <?php endif; ?>
                        
                        <button class="btn btn-primary btn-lg w-100 mb-3" onclick="rsvpEvent(<?= $event['id'] ?>)">
                            <i class="bi bi-check-circle"></i> 
                            <?= $is_full ? 'Join Waitlist' : 'RSVP Now' ?>
                        </button>
                        
                        <div class="text-center">
                            <small class="text-muted">
                                Registration is free and helps us plan better for the event.
                            </small>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        This event has already taken place. Check out our <a href="events.php">upcoming events</a>.
                    </div>
                <?php endif; ?>
                
                <!-- Share Section -->
                <div class="card mt-4">
                    <div class="card-body text-center">
                        <h5 class="card-title">Share This Event</h5>
                        <div class="d-flex justify-content-center gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="shareEvent('facebook')">
                                <i class="bi bi-facebook"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="shareEvent('twitter')">
                                <i class="bi bi-twitter"></i>
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="shareEvent('whatsapp')">
                                <i class="bi bi-whatsapp"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="copyLink()">
                                <i class="bi bi-link-45deg"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- RSVP Modal -->
    <div class="modal fade" id="rsvpModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">RSVP for <?= htmlspecialchars($event['title']) ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="rsvpForm">
                        <input type="hidden" id="rsvp_event_id" name="event_id" value="<?= $event['id'] ?>">
                        
                        <div class="mb-3">
                            <label for="guest_name" class="form-label">Your Name *</label>
                            <input type="text" class="form-control" id="guest_name" name="guest_name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="guest_email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="guest_email" name="guest_email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="guest_phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="guest_phone" name="guest_phone">
                        </div>
                        
                        <div class="mb-3">
                            <label for="party_size" class="form-label">Party Size</label>
                            <select class="form-select" id="party_size" name="party_size">
                                <option value="1">1 Person</option>
                                <option value="2">2 People</option>
                                <option value="3">3 People</option>
                                <option value="4">4 People</option>
                                <option value="5">5+ People</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="special_requirements" class="form-label">Special Requirements</label>
                            <textarea class="form-control" id="special_requirements" name="special_requirements" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitRSVP()">Submit RSVP</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function rsvpEvent(eventId) {
            new bootstrap.Modal(document.getElementById('rsvpModal')).show();
        }
        
        function submitRSVP() {
            const form = document.getElementById('rsvpForm');
            const formData = new FormData(form);
            formData.append('action', 'rsvp');
            
            fetch('rsvp_handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('RSVP submitted successfully!');
                    bootstrap.Modal.getInstance(document.getElementById('rsvpModal')).hide();
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('An error occurred. Please try again.');
            });
        }
        
        function shareEvent(platform) {
            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent('<?= addslashes($event['title']) ?>');
            const text = encodeURIComponent('Join me at this event: <?= addslashes($event['title']) ?>');
            
            let shareUrl = '';
            
            switch(platform) {
                case 'facebook':
                    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
                    break;
                case 'twitter':
                    shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${text}`;
                    break;
                case 'whatsapp':
                    shareUrl = `https://wa.me/?text=${text}%20${url}`;
                    break;
            }
            
            if (shareUrl) {
                window.open(shareUrl, '_blank', 'width=600,height=400');
            }
        }
        
        function copyLink() {
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('Link copied to clipboard!');
            });
        }
    </script>
</body>
</html>
