<?php
/**
 * Create Missing Database Tables for Production Readiness
 */

require_once 'config.php';

echo "<h1>Creating Missing Database Tables</h1>";

// SQL statements to create missing tables
$tables = [
    'email_queue' => "CREATE TABLE IF NOT EXISTS email_queue (
        id INT AUTO_INCREMENT PRIMARY KEY,
        recipient_email VARCHAR(255) NOT NULL,
        recipient_name VARCHAR(255),
        subject VARCHAR(500) NOT NULL,
        body TEXT NOT NULL,
        sender_email VARCHAR(255),
        sender_name VARCHA<PERSON>(255),
        priority INT DEFAULT 5,
        status ENUM('pending', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
        attempts INT DEFAULT 0,
        max_attempts INT DEFAULT 3,
        scheduled_at DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sent_at DATETIME NULL,
        error_message TEXT NULL,
        email_type VARCHAR(50) DEFAULT 'general',
        template_id INT NULL,
        member_id INT NULL,
        INDEX idx_status (status),
        INDEX idx_scheduled (scheduled_at),
        INDEX idx_priority (priority),
        INDEX idx_created (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
    
    'scheduled_emails' => "CREATE TABLE IF NOT EXISTS scheduled_emails (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        template_id INT,
        custom_subject VARCHAR(255),
        custom_content TEXT,
        recipient_type ENUM('all_members', 'group', 'custom') DEFAULT 'all_members',
        recipient_group_id INT NULL,
        custom_recipients TEXT NULL,
        schedule_type ENUM('immediate', 'scheduled', 'recurring') DEFAULT 'immediate',
        scheduled_datetime DATETIME NULL,
        recurring_pattern VARCHAR(50) NULL,
        status ENUM('pending', 'active', 'paused', 'completed', 'cancelled') DEFAULT 'pending',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_run_at DATETIME NULL,
        next_run_at DATETIME NULL,
        total_recipients INT DEFAULT 0,
        sent_count INT DEFAULT 0,
        failed_count INT DEFAULT 0,
        INDEX idx_status (status),
        INDEX idx_schedule_type (schedule_type),
        INDEX idx_next_run (next_run_at),
        INDEX idx_created_by (created_by),
        FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
    
    'email_schedule_recipients' => "CREATE TABLE IF NOT EXISTS email_schedule_recipients (
        id INT AUTO_INCREMENT PRIMARY KEY,
        schedule_id INT NOT NULL,
        recipient_type ENUM('member', 'contact', 'custom') NOT NULL,
        recipient_id INT NULL,
        recipient_email VARCHAR(255) NOT NULL,
        recipient_name VARCHAR(255),
        status ENUM('pending', 'sent', 'failed', 'skipped') DEFAULT 'pending',
        sent_at DATETIME NULL,
        error_message TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_schedule_id (schedule_id),
        INDEX idx_status (status),
        INDEX idx_recipient_type (recipient_type),
        FOREIGN KEY (schedule_id) REFERENCES scheduled_emails(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
    
    'email_schedule_logs' => "CREATE TABLE IF NOT EXISTS email_schedule_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        schedule_id INT NOT NULL,
        log_type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
        message TEXT NOT NULL,
        details TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_schedule_id (schedule_id),
        INDEX idx_log_type (log_type),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (schedule_id) REFERENCES scheduled_emails(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci"
];

$success = 0;
$errors = [];

foreach ($tables as $tableName => $sql) {
    try {
        echo "<h3>Creating table: $tableName</h3>";
        $pdo->exec($sql);
        echo "<p style='color: green;'>✅ Table '$tableName' created successfully!</p>";
        $success++;
    } catch (PDOException $e) {
        $error = "❌ Error creating table '$tableName': " . $e->getMessage();
        echo "<p style='color: red;'>$error</p>";
        $errors[] = $error;
    }
}

echo "<h2>Summary</h2>";
echo "<p><strong>Successfully created:</strong> $success tables</p>";
echo "<p><strong>Errors:</strong> " . count($errors) . "</p>";

if (!empty($errors)) {
    echo "<h3>Error Details:</h3>";
    foreach ($errors as $error) {
        echo "<p style='color: red;'>$error</p>";
    }
}

// Verify tables exist
echo "<h2>Verification</h2>";
foreach (array_keys($tables) as $tableName) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ Table '$tableName' exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Table '$tableName' does not exist</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Error checking table '$tableName': " . $e->getMessage() . "</p>";
    }
}

echo "<p><strong>Database table creation completed!</strong></p>";
?>
