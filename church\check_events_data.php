<?php
require_once __DIR__ . '/config.php';

echo "<h2>Events Data Check</h2>";

try {
    // Check current events with correct column names
    $stmt = $pdo->query("SELECT id, title, event_date, location, is_active FROM events ORDER BY event_date");
    $events = $stmt->fetchAll();
    
    if ($events) {
        echo "<h3>Current Events:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Event Date (DateTime)</th><th>Location</th><th>Active</th><th>Is Future?</th></tr>";
        foreach ($events as $event) {
            $isFuture = strtotime($event['event_date']) > time() ? 'Yes' : 'No';
            echo "<tr>";
            echo "<td>{$event['id']}</td>";
            echo "<td>{$event['title']}</td>";
            echo "<td>{$event['event_date']}</td>";
            echo "<td>{$event['location']}</td>";
            echo "<td>{$event['is_active']}</td>";
            echo "<td>$isFuture</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p>Current time: " . date('Y-m-d H:i:s') . " (timestamp: " . time() . ")</p>";
    } else {
        echo "No events found<br>";
    }
    
    // Check RSVP table structure
    echo "<h3>Event RSVPs Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE event_rsvps");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "<td>{$col['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check what user table actually exists
    echo "<h3>Available Tables:</h3>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    foreach ($tables as $table) {
        echo "- $table<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>
