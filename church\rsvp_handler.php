<?php
require_once __DIR__ . '/config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

if (!isset($_POST['action']) || $_POST['action'] !== 'rsvp') {
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
    exit();
}

// Validate required fields
$required_fields = ['event_id', 'guest_name', 'guest_email'];
foreach ($required_fields as $field) {
    if (empty($_POST[$field])) {
        echo json_encode(['success' => false, 'message' => 'Please fill in all required fields']);
        exit();
    }
}

$event_id = (int)$_POST['event_id'];
$guest_name = trim($_POST['guest_name']);
$guest_email = trim($_POST['guest_email']);
$guest_phone = trim($_POST['guest_phone'] ?? '');
$party_size = (int)($_POST['party_size'] ?? 1);
$special_requirements = trim($_POST['special_requirements'] ?? '');

// Validate email
if (!filter_var($guest_email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'message' => 'Please enter a valid email address']);
    exit();
}

try {
    // Check if event exists and is active
    $stmt = $conn->prepare("SELECT * FROM events WHERE id = ? AND is_active = 1");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        echo json_encode(['success' => false, 'message' => 'Event not found or not active']);
        exit();
    }
    
    // Check if event is in the past
    if (strtotime($event['event_date']) < time()) {
        echo json_encode(['success' => false, 'message' => 'Cannot RSVP for past events']);
        exit();
    }
    
    // Check if user already has an RSVP for this event
    $stmt = $conn->prepare("SELECT id FROM event_rsvps WHERE event_id = ? AND guest_email = ?");
    $stmt->execute([$event_id, $guest_email]);
    $existing_rsvp = $stmt->fetch();
    
    if ($existing_rsvp) {
        echo json_encode(['success' => false, 'message' => 'You have already RSVP\'d for this event']);
        exit();
    }
    
    // Check capacity
    $stmt = $conn->prepare("SELECT COUNT(*) as attending_count FROM event_rsvps WHERE event_id = ? AND status = 'attending'");
    $stmt->execute([$event_id]);
    $attending_count = $stmt->fetch(PDO::FETCH_ASSOC)['attending_count'];
    
    $status = 'attending';
    if ($event['max_attendees'] && $attending_count >= $event['max_attendees']) {
        $status = 'waitlist'; // Add to waitlist if event is full
    }
    
    // Insert RSVP
    $stmt = $conn->prepare("
        INSERT INTO event_rsvps (event_id, guest_name, guest_email, guest_phone, status, party_size, special_requirements, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $event_id,
        $guest_name,
        $guest_email,
        $guest_phone,
        $status,
        $party_size,
        $special_requirements
    ]);
    
    // Send confirmation email (optional)
    sendRSVPConfirmationEmail($guest_email, $guest_name, $event, $status);
    
    $message = $status === 'waitlist' 
        ? 'You have been added to the waitlist for this event. We will notify you if a spot becomes available.'
        : 'Your RSVP has been confirmed! We look forward to seeing you at the event.';
    
    echo json_encode([
        'success' => true, 
        'message' => $message,
        'status' => $status
    ]);
    
} catch (PDOException $e) {
    error_log("RSVP Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while processing your RSVP. Please try again.']);
}

function sendRSVPConfirmationEmail($email, $name, $event, $status) {
    // This is a basic email function - you can enhance it based on your email system
    $subject = "RSVP Confirmation - " . $event['title'];
    
    $message = "Dear " . $name . ",\n\n";
    
    if ($status === 'waitlist') {
        $message .= "Thank you for your interest in " . $event['title'] . ". You have been added to the waitlist.\n\n";
        $message .= "We will notify you if a spot becomes available.\n\n";
    } else {
        $message .= "Thank you for your RSVP to " . $event['title'] . "!\n\n";
        $message .= "Event Details:\n";
        $message .= "Date: " . date('l, F j, Y \a\t g:i A', strtotime($event['event_date'])) . "\n";
        if ($event['location']) {
            $message .= "Location: " . $event['location'] . "\n";
        }
        $message .= "\nWe look forward to seeing you there!\n\n";
    }
    
    $message .= "Best regards,\n";
    $message .= get_organization_name();
    
    $headers = "From: " . get_organization_name() . " <noreply@" . $_SERVER['HTTP_HOST'] . ">\r\n";
    $headers .= "Reply-To: noreply@" . $_SERVER['HTTP_HOST'] . "\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    // Send email (you might want to use your existing email system)
    @mail($email, $subject, $message, $headers);
}
?>
