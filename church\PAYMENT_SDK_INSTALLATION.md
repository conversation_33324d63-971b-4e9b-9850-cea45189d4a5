# Payment SDK Installation Guide

This guide provides instructions on how to install the required SDKs for the payment processing functionality in the Church Management System.

## Prerequisites

- PHP 7.4 or higher
- Composer (https://getcomposer.org/)

## Installation Steps

1. Navigate to the church directory in your terminal:
   ```
   cd /path/to/church
   ```

2. Update the composer.json file to include the required dependencies:
   ```json
   {
       "name": "church/email-management-system",
       "description": "Church Email Management System",
       "type": "project",
       "require": {
           "php": ">=7.4",
           "phpmailer/phpmailer": "^6.5",
           "pragmarx/google2fa": "*",
           "paypal/paypal-checkout-sdk": "^1.0",
           "stripe/stripe-php": "^10.0"
       },
       "require-dev": {
           "phpunit/phpunit": "^9.5",
           "mockery/mockery": "^1.4"
       },
       "autoload": {
           "files": [
               "config.php"
           ]
       },
       "scripts": {
           "test": "phpunit"
       }
   }
   ```

3. Run Composer to install the dependencies:
   ```
   composer update
   ```

4. Verify the installation:
   - Check that the `vendor/paypal` and `vendor/stripe` directories exist
   - Ensure that the `vendor/autoload.php` file is present

## Troubleshooting

If you encounter any issues during installation:

1. Make sure you have the correct PHP version installed:
   ```
   php -v
   ```

2. Verify that Composer is installed correctly:
   ```
   composer --version
   ```

3. If you get errors about missing extensions, install the required PHP extensions:
   ```
   # For Ubuntu/Debian
   sudo apt-get install php-curl php-json php-mbstring

   # For CentOS/RHEL
   sudo yum install php-curl php-json php-mbstring

   # For Windows with XAMPP
   # These extensions are usually already enabled in php.ini
   ```

4. If you're behind a proxy, configure Composer to use it:
   ```
   composer config -g http-basic.proxy.example.org username password
   ```

5. If you're still having issues, try clearing Composer's cache:
   ```
   composer clear-cache
   ```

## Manual Installation (Alternative)

If you're unable to use Composer, you can manually download and include the SDKs:

1. PayPal SDK: https://github.com/paypal/Checkout-PHP-SDK
2. Stripe SDK: https://github.com/stripe/stripe-php

Extract the files to the `vendor` directory and update the autoload.php file accordingly.

## Support

If you continue to experience issues with the payment SDKs, please contact the system administrator or refer to the official documentation:

- PayPal Checkout SDK: https://github.com/paypal/Checkout-PHP-SDK
- Stripe PHP SDK: https://github.com/stripe/stripe-php 