<?php
require_once 'config.php';

try {
    // Check events table structure
    echo "<h2>Events Table Structure:</h2>";
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Check event_rsvps table structure
    echo "<h2>Event RSVPs Table Structure:</h2>";
    $stmt = $pdo->query("DESCRIBE event_rsvps");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Check if there are any events
    echo "<h2>Sample Events:</h2>";
    $stmt = $pdo->query("SELECT * FROM events LIMIT 3");
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($events);
    echo "</pre>";

    // Test RSVP functionality
    echo "<h2>Testing RSVP Handler Logic:</h2>";

    // Simulate the RSVP handler logic
    $eventId = 3; // Summer Fellowship Gathering
    $status = 'maybe';
    $userId = 1; // Assuming user ID 1 exists

    echo "Testing with Event ID: $eventId, Status: $status, User ID: $userId<br>";

    // Check if event exists and is active
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND is_active = 1");
    $stmt->execute([$eventId]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($event) {
        echo "✓ Event found and active<br>";

        // Check for existing RSVP
        $stmt = $pdo->prepare("SELECT * FROM event_rsvps WHERE event_id = ? AND user_id = ?");
        $stmt->execute([$eventId, $userId]);
        $existingRsvp = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingRsvp) {
            echo "✓ Existing RSVP found: " . $existingRsvp['status'] . "<br>";
        } else {
            echo "✓ No existing RSVP found<br>";
        }

        // Test the status validation
        $validStatuses = ['attending', 'maybe', 'not_attending'];
        if (in_array($status, $validStatuses)) {
            echo "✓ Status '$status' is valid<br>";
        } else {
            echo "✗ Status '$status' is invalid<br>";
        }

    } else {
        echo "✗ Event not found or not active<br>";
    }

    // Check what user tables exist
    echo "<h2>Available Tables:</h2>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";

    // Check members table structure
    echo "<h2>Members Table Structure:</h2>";
    $stmt = $pdo->query("DESCRIBE members");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($columns);
    echo "</pre>";

    // Show sample members
    echo "<h2>Sample Members:</h2>";
    $stmt = $pdo->query("SELECT * FROM members LIMIT 3");
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($members);
    echo "</pre>";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
