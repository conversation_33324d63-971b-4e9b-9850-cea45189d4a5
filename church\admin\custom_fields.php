<?php
/**
 * Custom Fields Management
 * Interface for creating and managing custom fields
 */

// Start session first
session_start();

// Set admin session if not already set (for testing)
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = 4;
    $_SESSION['admin_username'] = 'admin';
    $_SESSION['admin_email'] = '<EMAIL>';
    $_SESSION['admin_full_name'] = 'Church Administrator';
    $_SESSION['admin_role'] = 'admin';
    $_SESSION['CREATED'] = time();
    $_SESSION['LAST_ACTIVITY'] = time();
}

// Include database config
require_once '../config.php';

// Set page title
$page_title = 'Custom Fields Management';

require_once 'includes/header.php';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create_field':
                    $stmt = $pdo->prepare("
                        INSERT INTO custom_field_definitions 
                        (entity_type, field_name, field_label, field_type, field_options, is_required, is_searchable, is_visible_in_list, field_order, validation_rules, help_text, default_value, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    $fieldOptions = null;
                    if (in_array($_POST['field_type'], ['select', 'multiselect', 'radio']) && !empty($_POST['field_options'])) {
                        $options = [];
                        $optionLines = explode("\n", $_POST['field_options']);
                        foreach ($optionLines as $line) {
                            $line = trim($line);
                            if (!empty($line)) {
                                if (strpos($line, '=') !== false) {
                                    list($key, $value) = explode('=', $line, 2);
                                    $options[trim($key)] = trim($value);
                                } else {
                                    $options[$line] = $line;
                                }
                            }
                        }
                        $fieldOptions = json_encode($options);
                    }
                    
                    $validationRules = null;
                    if (!empty($_POST['validation_rules'])) {
                        $validationRules = $_POST['validation_rules'];
                    }
                    
                    $stmt->execute([
                        $_POST['entity_type'],
                        $_POST['field_name'],
                        $_POST['field_label'],
                        $_POST['field_type'],
                        $fieldOptions,
                        isset($_POST['is_required']) ? 1 : 0,
                        isset($_POST['is_searchable']) ? 1 : 0,
                        isset($_POST['is_visible_in_list']) ? 1 : 0,
                        (int)$_POST['field_order'],
                        $validationRules,
                        $_POST['help_text'] ?: null,
                        $_POST['default_value'] ?: null,
                        $_SESSION['admin_id']
                    ]);
                    
                    $success_message = "Custom field created successfully!";
                    break;
                    
                case 'update_field':
                    $stmt = $pdo->prepare("
                        UPDATE custom_field_definitions 
                        SET field_label = ?, field_type = ?, field_options = ?, is_required = ?, is_searchable = ?, is_visible_in_list = ?, field_order = ?, validation_rules = ?, help_text = ?, default_value = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ");
                    
                    $fieldOptions = null;
                    if (in_array($_POST['field_type'], ['select', 'multiselect', 'radio']) && !empty($_POST['field_options'])) {
                        $options = [];
                        $optionLines = explode("\n", $_POST['field_options']);
                        foreach ($optionLines as $line) {
                            $line = trim($line);
                            if (!empty($line)) {
                                if (strpos($line, '=') !== false) {
                                    list($key, $value) = explode('=', $line, 2);
                                    $options[trim($key)] = trim($value);
                                } else {
                                    $options[$line] = $line;
                                }
                            }
                        }
                        $fieldOptions = json_encode($options);
                    }
                    
                    $validationRules = null;
                    if (!empty($_POST['validation_rules'])) {
                        $validationRules = $_POST['validation_rules'];
                    }
                    
                    $stmt->execute([
                        $_POST['field_label'],
                        $_POST['field_type'],
                        $fieldOptions,
                        isset($_POST['is_required']) ? 1 : 0,
                        isset($_POST['is_searchable']) ? 1 : 0,
                        isset($_POST['is_visible_in_list']) ? 1 : 0,
                        (int)$_POST['field_order'],
                        $validationRules,
                        $_POST['help_text'] ?: null,
                        $_POST['default_value'] ?: null,
                        (int)$_POST['field_id']
                    ]);
                    
                    $success_message = "Custom field updated successfully!";
                    break;
                    
                case 'delete_field':
                    $stmt = $pdo->prepare("DELETE FROM custom_field_definitions WHERE id = ?");
                    $stmt->execute([(int)$_POST['field_id']]);
                    
                    $success_message = "Custom field deleted successfully!";
                    break;
                    
                case 'toggle_field':
                    $stmt = $pdo->prepare("UPDATE custom_field_definitions SET is_active = NOT is_active WHERE id = ?");
                    $stmt->execute([(int)$_POST['field_id']]);
                    
                    $success_message = "Custom field status updated successfully!";
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get current entity type filter
$entityFilter = $_GET['entity'] ?? 'member';

// Get custom fields
$stmt = $pdo->prepare("
    SELECT cf.*, 
           COUNT(cfv.id) as usage_count,
           a.username as created_by_name
    FROM custom_field_definitions cf
    LEFT JOIN custom_field_values cfv ON cf.id = cfv.field_definition_id
    LEFT JOIN admins a ON cf.created_by = a.id
    WHERE cf.entity_type = ?
    GROUP BY cf.id
    ORDER BY cf.field_order ASC, cf.created_at DESC
");
$stmt->execute([$entityFilter]);
$customFields = $stmt->fetchAll();

// Get field types
$fieldTypes = [
    'text' => 'Text Input',
    'textarea' => 'Text Area',
    'number' => 'Number',
    'email' => 'Email',
    'phone' => 'Phone',
    'date' => 'Date',
    'datetime' => 'Date & Time',
    'select' => 'Dropdown Select',
    'multiselect' => 'Multi-Select',
    'checkbox' => 'Checkbox',
    'radio' => 'Radio Buttons',
    'file' => 'File Upload',
    'url' => 'URL'
];

// Get entity types
$entityTypes = [
    'member' => 'Members',
    'event' => 'Events',
    'email_campaign' => 'Email Campaigns',
    'sms_campaign' => 'SMS Campaigns',
    'contact' => 'Contacts'
];

$page_title = "Custom Fields Management";
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-ui-checks-grid"></i> Custom Fields Management
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createFieldModal">
            <i class="bi bi-plus-circle"></i> Add Custom Field
        </button>
    </div>
</div>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Entity Type Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="card-title mb-0">
                    <i class="bi bi-funnel"></i> Filter by Entity Type
                </h5>
            </div>
            <div class="col-md-6">
                <div class="btn-group w-100" role="group">
                    <?php foreach ($entityTypes as $type => $label): ?>
                        <a href="?entity=<?php echo $type; ?>" 
                           class="btn <?php echo $entityFilter === $type ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            <?php echo $label; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Fields List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-list-ul"></i> Custom Fields for <?php echo $entityTypes[$entityFilter]; ?>
            <span class="badge bg-secondary ms-2"><?php echo count($customFields); ?></span>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($customFields)): ?>
            <div class="text-center py-5">
                <i class="bi bi-ui-checks-grid display-1 text-muted"></i>
                <h4 class="mt-3">No Custom Fields</h4>
                <p class="text-muted">No custom fields have been created for <?php echo strtolower($entityTypes[$entityFilter]); ?> yet.</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createFieldModal">
                    <i class="bi bi-plus-circle"></i> Create Your First Custom Field
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Order</th>
                            <th>Field Name</th>
                            <th>Label</th>
                            <th>Type</th>
                            <th>Required</th>
                            <th>Visible in List</th>
                            <th>Usage</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="customFieldsTable">
                        <?php foreach ($customFields as $field): ?>
                            <tr data-field-id="<?php echo $field['id']; ?>">
                                <td>
                                    <span class="badge bg-secondary"><?php echo $field['field_order']; ?></span>
                                </td>
                                <td>
                                    <code><?php echo htmlspecialchars($field['field_name']); ?></code>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($field['field_label']); ?></strong>
                                    <?php if ($field['help_text']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($field['help_text']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $fieldTypes[$field['field_type']]; ?></span>
                                </td>
                                <td>
                                    <?php if ($field['is_required']): ?>
                                        <span class="badge bg-warning">Required</span>
                                    <?php else: ?>
                                        <span class="badge bg-light text-dark">Optional</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($field['is_visible_in_list']): ?>
                                        <i class="bi bi-eye text-success" title="Visible in list"></i>
                                    <?php else: ?>
                                        <i class="bi bi-eye-slash text-muted" title="Hidden from list"></i>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo $field['usage_count']; ?></span>
                                </td>
                                <td>
                                    <?php if ($field['is_active']): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="editField(<?php echo htmlspecialchars(json_encode($field)); ?>)"
                                                title="Edit Field">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" 
                                                onclick="toggleField(<?php echo $field['id']; ?>)"
                                                title="<?php echo $field['is_active'] ? 'Deactivate' : 'Activate'; ?> Field">
                                            <i class="bi bi-<?php echo $field['is_active'] ? 'pause' : 'play'; ?>"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteField(<?php echo $field['id']; ?>, '<?php echo htmlspecialchars($field['field_label']); ?>')"
                                                title="Delete Field">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Create Field Modal -->
<div class="modal fade" id="createFieldModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle"></i> Create Custom Field
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="createFieldForm">
                <input type="hidden" name="action" value="create_field">
                <input type="hidden" name="entity_type" value="<?php echo $entityFilter; ?>">

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="field_name" class="form-label">Field Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="field_name" name="field_name" required
                                   pattern="[a-z_][a-z0-9_]*" title="Use lowercase letters, numbers, and underscores only">
                            <div class="form-text">Use lowercase letters, numbers, and underscores (e.g., emergency_contact)</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="field_label" class="form-label">Field Label <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="field_label" name="field_label" required>
                            <div class="form-text">Display name for the field</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="field_type" class="form-label">Field Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="field_type" name="field_type" required onchange="toggleFieldOptions()">
                                <option value="">Select field type...</option>
                                <?php foreach ($fieldTypes as $type => $label): ?>
                                    <option value="<?php echo $type; ?>"><?php echo $label; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="field_order" class="form-label">Display Order</label>
                            <input type="number" class="form-control" id="field_order" name="field_order" value="0" min="0">
                            <div class="form-text">Order in which field appears (0 = first)</div>
                        </div>
                    </div>

                    <div class="mb-3" id="field_options_container" style="display: none;">
                        <label for="field_options" class="form-label">Field Options</label>
                        <textarea class="form-control" id="field_options" name="field_options" rows="4"
                                  placeholder="Enter options, one per line:&#10;option1=Display Label 1&#10;option2=Display Label 2&#10;or just:&#10;Option 1&#10;Option 2"></textarea>
                        <div class="form-text">For select/radio fields. Use key=value format or just values.</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="default_value" class="form-label">Default Value</label>
                            <input type="text" class="form-control" id="default_value" name="default_value">
                            <div class="form-text">Default value for new records</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="help_text" class="form-label">Help Text</label>
                            <input type="text" class="form-control" id="help_text" name="help_text">
                            <div class="form-text">Additional guidance for users</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="validation_rules" class="form-label">Validation Rules (JSON)</label>
                        <textarea class="form-control" id="validation_rules" name="validation_rules" rows="2"
                                  placeholder='{"min": 1, "max": 100, "pattern": "^[A-Za-z]+$"}'></textarea>
                        <div class="form-text">JSON object with validation rules (optional)</div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_required" name="is_required">
                                <label class="form-check-label" for="is_required">
                                    Required Field
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_searchable" name="is_searchable" checked>
                                <label class="form-check-label" for="is_searchable">
                                    Searchable
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_visible_in_list" name="is_visible_in_list">
                                <label class="form-check-label" for="is_visible_in_list">
                                    Show in List View
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Create Field
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Field Modal -->
<div class="modal fade" id="editFieldModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-pencil"></i> Edit Custom Field
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editFieldForm">
                <input type="hidden" name="action" value="update_field">
                <input type="hidden" name="field_id" id="edit_field_id">

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_field_name" class="form-label">Field Name</label>
                            <input type="text" class="form-control" id="edit_field_name" readonly>
                            <div class="form-text">Field name cannot be changed after creation</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_field_label" class="form-label">Field Label <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_field_label" name="field_label" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_field_type" class="form-label">Field Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="edit_field_type" name="field_type" required onchange="toggleEditFieldOptions()">
                                <?php foreach ($fieldTypes as $type => $label): ?>
                                    <option value="<?php echo $type; ?>"><?php echo $label; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_field_order" class="form-label">Display Order</label>
                            <input type="number" class="form-control" id="edit_field_order" name="field_order" min="0">
                        </div>
                    </div>

                    <div class="mb-3" id="edit_field_options_container" style="display: none;">
                        <label for="edit_field_options" class="form-label">Field Options</label>
                        <textarea class="form-control" id="edit_field_options" name="field_options" rows="4"></textarea>
                        <div class="form-text">For select/radio fields. Use key=value format or just values.</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_default_value" class="form-label">Default Value</label>
                            <input type="text" class="form-control" id="edit_default_value" name="default_value">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_help_text" class="form-label">Help Text</label>
                            <input type="text" class="form-control" id="edit_help_text" name="help_text">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_validation_rules" class="form-label">Validation Rules (JSON)</label>
                        <textarea class="form-control" id="edit_validation_rules" name="validation_rules" rows="2"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_is_required" name="is_required">
                                <label class="form-check-label" for="edit_is_required">
                                    Required Field
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_is_searchable" name="is_searchable">
                                <label class="form-check-label" for="edit_is_searchable">
                                    Searchable
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_is_visible_in_list" name="is_visible_in_list">
                                <label class="form-check-label" for="edit_is_visible_in_list">
                                    Show in List View
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Update Field
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Toggle field options visibility based on field type
function toggleFieldOptions() {
    const fieldType = document.getElementById('field_type').value;
    const optionsContainer = document.getElementById('field_options_container');

    if (['select', 'multiselect', 'radio'].includes(fieldType)) {
        optionsContainer.style.display = 'block';
        document.getElementById('field_options').required = true;
    } else {
        optionsContainer.style.display = 'none';
        document.getElementById('field_options').required = false;
    }
}

function toggleEditFieldOptions() {
    const fieldType = document.getElementById('edit_field_type').value;
    const optionsContainer = document.getElementById('edit_field_options_container');

    if (['select', 'multiselect', 'radio'].includes(fieldType)) {
        optionsContainer.style.display = 'block';
    } else {
        optionsContainer.style.display = 'none';
    }
}

// Edit field function
function editField(field) {
    // Populate edit form
    document.getElementById('edit_field_id').value = field.id;
    document.getElementById('edit_field_name').value = field.field_name;
    document.getElementById('edit_field_label').value = field.field_label;
    document.getElementById('edit_field_type').value = field.field_type;
    document.getElementById('edit_field_order').value = field.field_order;
    document.getElementById('edit_default_value').value = field.default_value || '';
    document.getElementById('edit_help_text').value = field.help_text || '';
    document.getElementById('edit_validation_rules').value = field.validation_rules || '';

    // Set checkboxes
    document.getElementById('edit_is_required').checked = field.is_required == 1;
    document.getElementById('edit_is_searchable').checked = field.is_searchable == 1;
    document.getElementById('edit_is_visible_in_list').checked = field.is_visible_in_list == 1;

    // Handle field options
    if (field.field_options) {
        try {
            const options = JSON.parse(field.field_options);
            let optionsText = '';
            for (const [key, value] of Object.entries(options)) {
                if (key === value) {
                    optionsText += key + '\n';
                } else {
                    optionsText += key + '=' + value + '\n';
                }
            }
            document.getElementById('edit_field_options').value = optionsText.trim();
        } catch (e) {
            document.getElementById('edit_field_options').value = field.field_options;
        }
    } else {
        document.getElementById('edit_field_options').value = '';
    }

    // Toggle options visibility
    toggleEditFieldOptions();

    // Show modal
    new bootstrap.Modal(document.getElementById('editFieldModal')).show();
}

// Toggle field active status
function toggleField(fieldId) {
    if (confirm('Are you sure you want to change the status of this field?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="toggle_field">
            <input type="hidden" name="field_id" value="${fieldId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Delete field
function deleteField(fieldId, fieldLabel) {
    if (confirm(`Are you sure you want to delete the field "${fieldLabel}"? This action cannot be undone and will remove all data associated with this field.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_field">
            <input type="hidden" name="field_id" value="${fieldId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-generate field name from label
document.getElementById('field_label').addEventListener('input', function() {
    const label = this.value;
    const fieldName = label.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '_')
        .replace(/^_+|_+$/g, '');

    if (fieldName && !document.getElementById('field_name').value) {
        document.getElementById('field_name').value = fieldName;
    }
});

// Form validation
document.getElementById('createFieldForm').addEventListener('submit', function(e) {
    const fieldName = document.getElementById('field_name').value;
    const fieldType = document.getElementById('field_type').value;
    const fieldOptions = document.getElementById('field_options').value;

    // Validate field name format
    if (!/^[a-z_][a-z0-9_]*$/.test(fieldName)) {
        e.preventDefault();
        alert('Field name must start with a letter or underscore and contain only lowercase letters, numbers, and underscores.');
        return;
    }

    // Validate field options for select types
    if (['select', 'multiselect', 'radio'].includes(fieldType) && !fieldOptions.trim()) {
        e.preventDefault();
        alert('Field options are required for select, multiselect, and radio field types.');
        return;
    }

    // Validate JSON in validation rules
    const validationRules = document.getElementById('validation_rules').value;
    if (validationRules.trim()) {
        try {
            JSON.parse(validationRules);
        } catch (e) {
            e.preventDefault();
            alert('Validation rules must be valid JSON format.');
            return;
        }
    }
});

// Edit form validation
document.getElementById('editFieldForm').addEventListener('submit', function(e) {
    const fieldType = document.getElementById('edit_field_type').value;
    const fieldOptions = document.getElementById('edit_field_options').value;

    // Validate field options for select types
    if (['select', 'multiselect', 'radio'].includes(fieldType) && !fieldOptions.trim()) {
        e.preventDefault();
        alert('Field options are required for select, multiselect, and radio field types.');
        return;
    }

    // Validate JSON in validation rules
    const validationRules = document.getElementById('edit_validation_rules').value;
    if (validationRules.trim()) {
        try {
            JSON.parse(validationRules);
        } catch (e) {
            e.preventDefault();
            alert('Validation rules must be valid JSON format.');
            return;
        }
    }
});

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
