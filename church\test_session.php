<?php
session_start();

// Include configuration and classes
require_once 'config.php';
require_once 'classes/SecurityManager.php';
require_once 'classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

header('Content-Type: application/json');

// Check if this is a POST request (simulating the RSVP request)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = [
        'method' => 'POST',
        'session_id' => session_id(),
        'session_data' => $_SESSION,
        'is_authenticated' => $userAuth->isAuthenticated(),
        'post_data' => $_POST,
        'cookies' => $_COOKIE
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}

// For GET requests, show a test page
?>
<!DOCTYPE html>
<html>
<head>
    <title>Session Test</title>
</head>
<body>
    <h2>Session Test</h2>
    
    <div id="session-info">
        <h3>Current Session Info:</h3>
        <p>Session ID: <?php echo session_id(); ?></p>
        <p>Authenticated: <?php echo $userAuth->isAuthenticated() ? 'Yes' : 'No'; ?></p>
        <?php if ($userAuth->isAuthenticated()): ?>
            <p>User ID: <?php echo $_SESSION['user_id'] ?? 'Not set'; ?></p>
            <p>Username: <?php echo $_SESSION['username'] ?? 'Not set'; ?></p>
        <?php endif; ?>
    </div>
    
    <button onclick="testSession()">Test Session via Fetch</button>
    
    <div id="result"></div>
    
    <script>
        function testSession() {
            const formData = new FormData();
            formData.append('test', 'session_test');
            
            fetch('test_session.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('result').innerHTML = '<h3>Fetch Result:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.getElementById('result').innerHTML = '<h3>Error:</h3><pre>' + error + '</pre>';
            });
        }
    </script>
</body>
</html>
