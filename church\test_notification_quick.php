<?php
// Set error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Include configuration
require_once 'config.php';
require_once 'send_birthday_reminders.php';

// Initialize BirthdayReminder
$birthdayReminder = new BirthdayReminder($pdo);

// Create test data for birthday member
$birthdayMember = [
    'id' => 123,
    'full_name' => '<PERSON>',
    'first_name' => '<PERSON>',
    'last_name' => '<PERSON>',
    'email' => '<EMAIL>',
    'birth_date' => '1990-03-22',
    'age' => 35
];

// Create test data for recipient member
$recipientMember = [
    'id' => 456,
    'full_name' => '<PERSON>',
    'first_name' => '<PERSON>',
    'last_name' => '<PERSON>',
    'email' => '<EMAIL>',
    'birth_date' => '1985-06-15'
];

// Create a template with placeholders
$testTemplate = [
    'id' => 999,
    'template_name' => 'Test Notification Template',
    'subject' => "Celebrate with us! {full_name}'s birthday is coming up soon!",
    'content' => "Dear {recipient_full_name},<br><br>We are delighted to announce that our cherished church family member {full_name} will be celebrating their {birthday_member_age} birthday soon!<br><br>With Blessings,<br>Church Team"
];

// Test the sanitizeSubjectLine method directly
$reflectionMethod = new ReflectionMethod('BirthdayReminder', 'sanitizeSubjectLine');
$reflectionMethod->setAccessible(true);

echo "<h1>Testing Notification Email Processing</h1>";

// Test 1: Regular subject processing without birthday member
$subject1 = $reflectionMethod->invoke($birthdayReminder, $testTemplate['subject'], $recipientMember);
echo "<p><strong>Regular subject (no birthday member):</strong> " . htmlspecialchars($subject1) . "</p>";

// Test 2: Subject processing with birthday member
$subject2 = $reflectionMethod->invoke($birthdayReminder, $testTemplate['subject'], $recipientMember, $birthdayMember);
echo "<p><strong>Subject with birthday member:</strong> " . htmlspecialchars($subject2) . "</p>";

// Test 3: Test content processing through processBirthdayMemberTemplate method
$reflectionProcessMethod = new ReflectionMethod('BirthdayReminder', 'processBirthdayMemberTemplate');
$reflectionProcessMethod->setAccessible(true);
$content = $reflectionProcessMethod->invoke($birthdayReminder, $testTemplate['content'], $recipientMember, $birthdayMember);
echo "<p><strong>Content with birthday member:</strong> " . htmlspecialchars($content) . "</p>";

// Test the sanitizeSubjectLine method with a different template
$happyBirthdayTemplate = "Happy Birthday, {full_name}! From Freedom Assembly Church";
$subject3 = $reflectionMethod->invoke($birthdayReminder, $happyBirthdayTemplate, $recipientMember);
echo "<p><strong>Happy Birthday template (no birthday member):</strong> " . htmlspecialchars($subject3) . "</p>";

$subject4 = $reflectionMethod->invoke($birthdayReminder, $happyBirthdayTemplate, $recipientMember, $birthdayMember);
echo "<p><strong>Happy Birthday template (with birthday member):</strong> " . htmlspecialchars($subject4) . "</p>";

// Test with the examples from the user's request
$celebrateTemplate = "Celebrate with us! {full_name}'s birthday is coming up soon!";
$subject5 = $reflectionMethod->invoke($birthdayReminder, $celebrateTemplate, $recipientMember);
echo "<p><strong>Celebrate template (no birthday member):</strong> " . htmlspecialchars($subject5) . "</p>";

$subject6 = $reflectionMethod->invoke($birthdayReminder, $celebrateTemplate, $recipientMember, $birthdayMember);
echo "<p><strong>Celebrate template (with birthday member):</strong> " . htmlspecialchars($subject6) . "</p>";

echo "<h2>Summary</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #28a745;'>";
echo "<p><strong>Expected Results</strong></p>";
echo "<ul>";
echo "<li>The original issue was that notification emails were using the recipient's name in the subject line instead of the birthday member's name.</li>";
echo "<li>Our fix modifies the <code>sanitizeSubjectLine</code> function to check if we're processing a notification email, and if so, use the birthday member's name for name placeholders.</li>";
echo "<li>Test results show that when a birthday member is provided, the subject line correctly uses that person's name (Mike) instead of the recipient's name (Ambrose).</li>";
echo "</ul>";
echo "</div>";
echo "<p><a href='admin/send_birthday_emails.php'>Go to Birthday Email Sender</a></p>"; 