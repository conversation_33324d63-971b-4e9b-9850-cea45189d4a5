<?php
require_once '../config.php';

// Add sidebar color settings to the database
$sidebarColors = [
    'sidebar_bg_color' => '#e67f0a',
    'sidebar_text_color' => '#ffffff',
    'sidebar_hover_color' => '#102841'
];

try {
    foreach ($sidebarColors as $name => $value) {
        // Check if setting already exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM appearance_settings WHERE setting_name = ?");
        $stmt->execute([$name]);
        $exists = $stmt->fetchColumn();
        
        if ($exists) {
            // Update existing setting
            $stmt = $pdo->prepare("UPDATE appearance_settings SET setting_value = ? WHERE setting_name = ?");
            $stmt->execute([$value, $name]);
            echo "Updated $name to $value<br>";
        } else {
            // Insert new setting
            $stmt = $pdo->prepare("INSERT INTO appearance_settings (setting_name, setting_value, setting_type, category) VALUES (?, ?, 'color', 'sidebar')");
            $stmt->execute([$name, $value]);
            echo "Added $name with value $value<br>";
        }
    }
    
    // Regenerate CSS
    include_once 'appearance_settings.php';
    generateCustomCSS();
    echo "<br>CSS regenerated successfully!<br>";
    
    // Show the new CSS content
    $cssFile = __DIR__ . '/css/custom-theme.css';
    if (file_exists($cssFile)) {
        echo "<h3>New CSS Content:</h3>";
        echo "<pre>" . htmlspecialchars(file_get_contents($cssFile)) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
