<?php
/**
 * Test Script for Scheduled Emails
 * 
 * This script tests the scheduled email functionality with the fix for preserving inline styles.
 */

// Include necessary files
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/email_functions.php';

// Set up logging
$logFile = __DIR__ . '/logs/test_scheduled_email.log';
file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test scheduled email script started.' . PHP_EOL);

// Create a test email with inline styles
$testEmailContent = '
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Test Scheduled Email</title>
</head>
<body>
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h1 style="color: #3366cc; text-align: center; border-bottom: 2px solid #eee; padding-bottom: 10px;">Test Email With Inline Styles</h1>
        
        <p style="font-size: 16px; line-height: 1.5; color: #333; margin: 15px 0;">
            This is a test email to verify that inline styles are preserved in scheduled emails.
        </p>
        
        <div style="background-color: #f8f8f8; padding: 15px; border-left: 4px solid #3366cc; margin: 20px 0;">
            <p style="margin: 0; font-style: italic;">This block should have a light gray background with a blue left border.</p>
        </div>
        
        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <tr style="background-color: #3366cc; color: white;">
                <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Column 1</th>
                <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Column 2</th>
            </tr>
            <tr style="background-color: #f2f2f2;">
                <td style="padding: 10px; border: 1px solid #ddd;">Data 1</td>
                <td style="padding: 10px; border: 1px solid #ddd;">Data 2</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Data 3</td>
                <td style="padding: 10px; border: 1px solid #ddd;">Data 4</td>
            </tr>
        </table>
        
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <a href="https://freedomassemblydb.online" style="display: inline-block; background-color: #3366cc; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Visit Our Website</a>
        </div>
        
        <p style="font-size: 12px; color: #666; text-align: center; margin-top: 20px;">
            This is a test email. Please ignore.
        </p>
    </div>
</body>
</html>
';

// Get a test recipient
try {
    $stmt = $pdo->prepare("SELECT id, email, full_name FROM members WHERE email IS NOT NULL AND email != '' LIMIT 1");
    $stmt->execute();
    $recipient = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$recipient) {
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] No recipients found with valid email addresses.' . PHP_EOL, FILE_APPEND);
        echo "No recipients found with valid email addresses.\n";
        exit;
    }
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test recipient: ' . $recipient['email'] . PHP_EOL, FILE_APPEND);
    
    // Test sending with the scheduled email function
    $result = sendScheduledEmail(
        $recipient['email'],
        $recipient['full_name'],
        'Test Scheduled Email - Inline Styles',
        $testEmailContent,
        999 // Dummy schedule ID
    );
    
    if ($result['success']) {
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test scheduled email sent successfully to ' . $recipient['email'] . PHP_EOL, FILE_APPEND);
        echo "Test scheduled email sent successfully to " . $recipient['email'] . "\n";
        
        // Also send using the regular email function for comparison
        $regularResult = sendEmail(
            $recipient['email'],
            $recipient['full_name'],
            'Test Regular Email - Inline Styles',
            $testEmailContent
        );
        
        if ($regularResult) {
            file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test regular email sent successfully to ' . $recipient['email'] . PHP_EOL, FILE_APPEND);
            echo "Test regular email sent successfully to " . $recipient['email'] . "\n";
        } else {
            file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Failed to send test regular email: ' . $GLOBALS['last_email_error'] . PHP_EOL, FILE_APPEND);
            echo "Failed to send test regular email: " . $GLOBALS['last_email_error'] . "\n";
        }
    } else {
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Failed to send test scheduled email: ' . $result['message'] . PHP_EOL, FILE_APPEND);
        echo "Failed to send test scheduled email: " . $result['message'] . "\n";
    }
    
} catch (Exception $e) {
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
    echo "Error: " . $e->getMessage() . "\n";
}

file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test scheduled email script completed.' . PHP_EOL, FILE_APPEND);
echo "Test completed. Check logs for details.\n";
