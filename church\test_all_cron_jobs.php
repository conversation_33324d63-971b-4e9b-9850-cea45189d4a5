<?php
/**
 * Test All Cron Jobs - Comprehensive Testing Script
 * 
 * This script tests all cron job functionality to ensure they work correctly
 * after the fixes have been applied.
 * 
 * Access: http://localhost/campaign/church/test_all_cron_jobs.php
 */

require_once 'config.php';

// Set up error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test results array
$TEST_RESULTS = [];
$CRON_KEY = 'fac_2024_secure_cron_8x9q2p5m';

/**
 * Log test results
 */
function logTest($testName, $status, $message, $details = '') {
    global $TEST_RESULTS;
    $TEST_RESULTS[] = [
        'test' => $testName,
        'status' => $status,
        'message' => $message,
        'details' => $details,
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

/**
 * Test cURL request to cron job
 */
function testCronJob($scriptName, $description) {
    global $CRON_KEY;
    
    $url = "http://localhost/campaign/church/cron/{$scriptName}?cron_key={$CRON_KEY}";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        logTest($description, 'FAILED', "cURL Error: $error", $url);
        return false;
    }
    
    if ($httpCode === 200) {
        logTest($description, 'PASSED', "HTTP 200 - Script executed successfully", "Response: " . substr($response, 0, 200));
        return true;
    } else {
        logTest($description, 'FAILED', "HTTP $httpCode - Script failed", "Response: " . substr($response, 0, 200));
        return false;
    }
}

/**
 * Test database connectivity
 */
function testDatabaseConnection() {
    global $pdo;
    
    try {
        $stmt = $pdo->query("SELECT 1");
        $result = $stmt->fetch();
        
        if ($result) {
            logTest('Database Connection', 'PASSED', 'Database connection successful');
            return true;
        } else {
            logTest('Database Connection', 'FAILED', 'Database query failed');
            return false;
        }
    } catch (Exception $e) {
        logTest('Database Connection', 'FAILED', 'Database connection failed: ' . $e->getMessage());
        return false;
    }
}

/**
 * Test email queue table exists
 */
function testEmailQueueTable() {
    global $pdo;
    
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'email_queue'");
        $result = $stmt->fetch();
        
        if ($result) {
            logTest('Email Queue Table', 'PASSED', 'email_queue table exists');
            
            // Test table structure
            $stmt = $pdo->query("DESCRIBE email_queue");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $requiredColumns = ['id', 'recipient_email', 'subject', 'body', 'status', 'created_at'];
            $missingColumns = array_diff($requiredColumns, $columns);
            
            if (empty($missingColumns)) {
                logTest('Email Queue Structure', 'PASSED', 'All required columns present');
                return true;
            } else {
                logTest('Email Queue Structure', 'FAILED', 'Missing columns: ' . implode(', ', $missingColumns));
                return false;
            }
        } else {
            logTest('Email Queue Table', 'FAILED', 'email_queue table does not exist');
            return false;
        }
    } catch (Exception $e) {
        logTest('Email Queue Table', 'FAILED', 'Error checking email_queue table: ' . $e->getMessage());
        return false;
    }
}

/**
 * Test birthday reminder functionality
 */
function testBirthdayReminders() {
    global $pdo;
    
    try {
        // Check if there are members with birthdays
        $stmt = $pdo->query("SELECT COUNT(*) FROM members WHERE birth_date IS NOT NULL OR date_of_birth IS NOT NULL");
        $memberCount = $stmt->fetchColumn();
        
        if ($memberCount > 0) {
            logTest('Birthday Data', 'PASSED', "Found $memberCount members with birthday data");
            
            // Test birthday query
            $stmt = $pdo->query("SELECT COUNT(*) FROM members WHERE 
                (MONTH(CURDATE()) = MONTH(birth_date) AND DAY(CURDATE()) = DAY(birth_date))
                OR
                (MONTH(CURDATE()) = MONTH(date_of_birth) AND DAY(CURDATE()) = DAY(date_of_birth))");
            $todayBirthdays = $stmt->fetchColumn();
            
            logTest('Today\'s Birthdays', 'INFO', "Found $todayBirthdays members with birthdays today");
            return true;
        } else {
            logTest('Birthday Data', 'WARNING', 'No members have birthday data');
            return false;
        }
    } catch (Exception $e) {
        logTest('Birthday Data', 'FAILED', 'Error checking birthday data: ' . $e->getMessage());
        return false;
    }
}

/**
 * Test email templates
 */
function testEmailTemplates() {
    global $pdo;
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM email_templates");
        $templateCount = $stmt->fetchColumn();
        
        if ($templateCount > 0) {
            logTest('Email Templates', 'PASSED', "Found $templateCount email templates");
            
            // Check for birthday templates
            $stmt = $pdo->query("SELECT COUNT(*) FROM email_templates WHERE is_birthday_template = 1");
            $birthdayTemplates = $stmt->fetchColumn();
            
            if ($birthdayTemplates > 0) {
                logTest('Birthday Templates', 'PASSED', "Found $birthdayTemplates birthday templates");
            } else {
                logTest('Birthday Templates', 'WARNING', 'No birthday templates found');
            }
            
            return true;
        } else {
            logTest('Email Templates', 'FAILED', 'No email templates found');
            return false;
        }
    } catch (Exception $e) {
        logTest('Email Templates', 'FAILED', 'Error checking email templates: ' . $e->getMessage());
        return false;
    }
}

/**
 * Test log directory permissions
 */
function testLogDirectory() {
    $logDir = __DIR__ . '/logs';
    
    if (!is_dir($logDir)) {
        if (mkdir($logDir, 0755, true)) {
            logTest('Log Directory', 'PASSED', 'Log directory created successfully');
        } else {
            logTest('Log Directory', 'FAILED', 'Could not create log directory');
            return false;
        }
    } else {
        logTest('Log Directory', 'PASSED', 'Log directory exists');
    }
    
    if (is_writable($logDir)) {
        logTest('Log Directory Permissions', 'PASSED', 'Log directory is writable');
        return true;
    } else {
        logTest('Log Directory Permissions', 'FAILED', 'Log directory is not writable');
        return false;
    }
}

/**
 * Test SMTP settings
 */
function testSMTPSettings() {
    global $pdo;
    
    try {
        $stmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($settings) {
            $requiredFields = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password'];
            $missingFields = [];
            
            foreach ($requiredFields as $field) {
                if (empty($settings[$field])) {
                    $missingFields[] = $field;
                }
            }
            
            if (empty($missingFields)) {
                logTest('SMTP Settings', 'PASSED', 'All SMTP settings configured');
                return true;
            } else {
                logTest('SMTP Settings', 'WARNING', 'Missing SMTP settings: ' . implode(', ', $missingFields));
                return false;
            }
        } else {
            logTest('SMTP Settings', 'FAILED', 'No SMTP settings found');
            return false;
        }
    } catch (Exception $e) {
        logTest('SMTP Settings', 'FAILED', 'Error checking SMTP settings: ' . $e->getMessage());
        return false;
    }
}

// Start testing
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cron Jobs Test Results - Church Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-passed { color: #28a745; }
        .test-failed { color: #dc3545; }
        .test-warning { color: #ffc107; }
        .test-info { color: #17a2b8; }
        .status-badge {
            font-size: 0.8em;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-cogs"></i> Cron Jobs Test Results
                    <small class="text-muted">Church Management System</small>
                </h1>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Testing all cron job functionality after fixes</strong><br>
                    This comprehensive test verifies that all cron jobs are working correctly with the fixed syntax and new files.
                </div>

                <?php
                // Run all tests
                echo "<h3>Running Tests...</h3>";
                
                // Basic system tests
                testDatabaseConnection();
                testLogDirectory();
                testEmailQueueTable();
                testEmailTemplates();
                testBirthdayReminders();
                testSMTPSettings();
                
                // Test each cron job
                testCronJob('birthday_reminders.php', 'Birthday Reminders Cron Job');
                testCronJob('process_email_queue.php', 'Email Queue Processing Cron Job');
                testCronJob('process_scheduled_emails.php', 'Scheduled Emails Cron Job');
                testCronJob('system_cleanup.php', 'System Cleanup Cron Job');
                
                // Display results
                $totalTests = count($TEST_RESULTS);
                $passedTests = count(array_filter($TEST_RESULTS, function($test) { return $test['status'] === 'PASSED'; }));
                $failedTests = count(array_filter($TEST_RESULTS, function($test) { return $test['status'] === 'FAILED'; }));
                $warningTests = count(array_filter($TEST_RESULTS, function($test) { return $test['status'] === 'WARNING'; }));
                ?>

                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $passedTests; ?></h4>
                                <p>Tests Passed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $failedTests; ?></h4>
                                <p>Tests Failed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $warningTests; ?></h4>
                                <p>Warnings</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $totalTests; ?></h4>
                                <p>Total Tests</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Detailed Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Test Name</th>
                                        <th>Status</th>
                                        <th>Message</th>
                                        <th>Details</th>
                                        <th>Timestamp</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($TEST_RESULTS as $result): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($result['test']); ?></strong></td>
                                        <td>
                                            <span class="badge status-badge 
                                                <?php 
                                                switch($result['status']) {
                                                    case 'PASSED': echo 'bg-success'; break;
                                                    case 'FAILED': echo 'bg-danger'; break;
                                                    case 'WARNING': echo 'bg-warning text-dark'; break;
                                                    case 'INFO': echo 'bg-info'; break;
                                                    default: echo 'bg-secondary';
                                                }
                                                ?>">
                                                <?php echo $result['status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($result['message']); ?></td>
                                        <td>
                                            <?php if (!empty($result['details'])): ?>
                                                <small class="text-muted"><?php echo htmlspecialchars($result['details']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><small><?php echo $result['timestamp']; ?></small></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="alert alert-success mt-4">
                    <h5><i class="fas fa-check-circle"></i> Next Steps</h5>
                    <ol>
                        <li><strong>Update Cron Jobs on demosender.online</strong> - Use the corrected syntax from CRON_JOBS_FIXED.md</li>
                        <li><strong>Test Email Delivery</strong> - Send test <NAME_EMAIL></li>
                        <li><strong>Monitor Logs</strong> - Check log files for successful execution</li>
                        <li><strong>Verify Birthday Notifications</strong> - Ensure birthday emails are being sent</li>
                    </ol>
                </div>

                <div class="mt-4">
                    <a href="admin/cron_jobs.php" class="btn btn-primary">
                        <i class="fas fa-cogs"></i> Manage Cron Jobs
                    </a>
                    <a href="comprehensive_test.php" class="btn btn-secondary">
                        <i class="fas fa-vial"></i> Run Full System Test
                    </a>
                    <a href="test_email_functionality.php" class="btn btn-info">
                        <i class="fas fa-envelope"></i> Test Email System
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
