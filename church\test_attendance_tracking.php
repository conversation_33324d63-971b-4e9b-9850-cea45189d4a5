<?php
// Test script for attendance tracking functionality
require_once 'config.php';

echo "<h2>Testing Attendance Tracking System</h2>";

try {
    // Check if the actually_attended column exists
    $stmt = $pdo->query("DESCRIBE event_rsvps");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('actually_attended', $columns)) {
        echo "✓ actually_attended column exists in event_rsvps table<br>";
    } else {
        echo "❌ actually_attended column missing from event_rsvps table<br>";
        exit;
    }
    
    // Get sample event with RSVPs
    $stmt = $pdo->query("
        SELECT 
            e.id,
            e.title,
            e.event_date,
            COUNT(CASE WHEN er.status = 'attending' THEN 1 END) as attending_count,
            COUNT(CASE WHEN er.actually_attended = 1 THEN 1 END) as actually_attended_count
        FROM events e
        LEFT JOIN event_rsvps er ON e.id = er.event_id
        GROUP BY e.id, e.title, e.event_date
        HAVING attending_count > 0
        ORDER BY e.event_date DESC
        LIMIT 1
    ");
    
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($event) {
        echo "<h3>Sample Event: " . htmlspecialchars($event['title']) . "</h3>";
        echo "Event ID: " . $event['id'] . "<br>";
        echo "Event Date: " . $event['event_date'] . "<br>";
        echo "Users RSVP'd as Attending: " . $event['attending_count'] . "<br>";
        echo "Users Actually Attended: " . $event['actually_attended_count'] . "<br><br>";
        
        // Get attendees for this event
        $stmt = $pdo->prepare("
            SELECT 
                er.id as rsvp_id,
                er.user_id,
                er.status,
                er.actually_attended,
                m.full_name,
                m.email
            FROM event_rsvps er
            JOIN members m ON er.user_id = m.id
            WHERE er.event_id = ? AND er.status = 'attending'
            ORDER BY m.full_name ASC
        ");
        $stmt->execute([$event['id']]);
        $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>Attendees who RSVP'd as 'attending':</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Name</th><th>Email</th><th>RSVP Status</th><th>Actually Attended</th></tr>";
        
        foreach ($attendees as $attendee) {
            $attendance_status = '';
            if ($attendee['actually_attended'] === null) {
                $attendance_status = 'Not marked';
            } elseif ($attendee['actually_attended'] == 1) {
                $attendance_status = 'Yes';
            } else {
                $attendance_status = 'No';
            }
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($attendee['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($attendee['email']) . "</td>";
            echo "<td>" . htmlspecialchars($attendee['status']) . "</td>";
            echo "<td>" . $attendance_status . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
        
        // Test updating attendance for first attendee
        if (!empty($attendees)) {
            $first_attendee = $attendees[0];
            echo "<h4>Testing Attendance Update</h4>";
            echo "Marking " . htmlspecialchars($first_attendee['full_name']) . " as attended...<br>";
            
            $stmt = $pdo->prepare("
                UPDATE event_rsvps 
                SET actually_attended = 1 
                WHERE id = ?
            ");
            $stmt->execute([$first_attendee['rsvp_id']]);
            
            echo "✓ Attendance updated successfully<br>";
            
            // Verify the update
            $stmt = $pdo->prepare("
                SELECT actually_attended 
                FROM event_rsvps 
                WHERE id = ?
            ");
            $stmt->execute([$first_attendee['rsvp_id']]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['actually_attended'] == 1) {
                echo "✓ Verification: Attendance correctly marked as attended<br>";
            } else {
                echo "❌ Verification failed<br>";
            }
        }
        
        // Test user stats calculation
        echo "<h4>Testing User Stats Calculation</h4>";
        if (!empty($attendees)) {
            $test_user_id = $attendees[0]['user_id'];
            
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as attended_count
                FROM event_rsvps er
                JOIN events e ON er.event_id = e.id
                WHERE er.user_id = ? AND er.actually_attended = 1
            ");
            $stmt->execute([$test_user_id]);
            $user_attended_count = $stmt->fetch(PDO::FETCH_ASSOC)['attended_count'];
            
            echo "User ID " . $test_user_id . " has attended " . $user_attended_count . " events<br>";
        }
        
    } else {
        echo "No events with RSVPs found for testing<br>";
    }
    
    echo "<br><h3>Database Schema Check</h3>";
    $stmt = $pdo->query("SHOW CREATE TABLE event_rsvps");
    $schema = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (strpos($schema['Create Table'], 'actually_attended') !== false) {
        echo "✓ actually_attended column properly defined in schema<br>";
    } else {
        echo "❌ actually_attended column not found in schema<br>";
    }
    
    echo "<br><h3>Test Complete!</h3>";
    echo "<a href='admin/event_attendance.php'>Go to Admin Attendance Tracking</a> (requires admin login)<br>";
    echo "<a href='user/events.php'>Go to User Events Page</a> (requires user login)<br>";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
}
