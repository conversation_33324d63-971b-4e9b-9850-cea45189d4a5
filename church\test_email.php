<?php
/**
 * Test Email Functions
 * 
 * This script tests the email functions to ensure they're working correctly.
 */

// Include necessary files
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/email_functions.php';

// Create logs directory if it doesn't exist
if (!is_dir(__DIR__ . '/logs')) {
    mkdir(__DIR__ . '/logs', 0755, true);
}

// Set up test parameters
$to = '<EMAIL>';
$toName = 'Test User';
$subject = 'Test Email from Church Management System';
$body = '<h1>Test Email</h1><p>This is a test email to verify that the email functions are working correctly.</p>';

// Debug function
function debug_var($var, $label = null) {
    echo ($label ? "$label: " : "") . (is_array($var) || is_object($var) ? print_r($var, true) : var_export($var, true)) . "\n";
}

// Check if email settings are configured
echo "Checking email settings...\n";
global $emailSettings;
debug_var($emailSettings, "Email Settings");
echo "\n";

// Test the original sendEmail function from config.php
echo "Testing sendEmail function from config.php...\n";
try {
    $result = sendEmail($to, $toName, $subject, $body, true);
    debug_var($result, "Result");
    
    global $last_email_error;
    debug_var($last_email_error, "Last Email Error");
    echo "\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n\n";
}

// Test the sendScheduledEmail function
echo "Testing sendScheduledEmail function...\n";
try {
    // Set a real test email address here
    $testEmail = '<EMAIL>'; // Change this to a real email for testing
    
    // Create a simple HTML test email with tracking
    $testBody = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>Test Email</title>
    </head>
    <body>
        <h1>Test Email</h1>
        <p>This is a test email sent at ' . date('Y-m-d H:i:s') . '</p>
        <p>Testing improved email functions with better error handling.</p>
        <p>Click <a href="' . SITE_URL . '/track_click.php?id=test123">here</a> to test click tracking.</p>
    </body>
    </html>';
    
    $result = sendScheduledEmail($testEmail, 'Test Recipient', 'Test Email - ' . date('Y-m-d H:i:s'), $testBody);
    debug_var($result, "Result");
    echo "\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n\n";
}

// Test the sendEmailWithPHPMailer function
echo "Testing sendEmailWithPHPMailer function...\n";
try {
    $result = sendEmailWithPHPMailer($to, $subject, $body, 'Church', '<EMAIL>', true);
    debug_var($result, "Result");
    echo "\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n\n";
}

// Check log files
echo "Checking log files...\n";
$debug_log_file = __DIR__ . '/logs/email_debug.log';
if (file_exists($debug_log_file)) {
    echo "Email debug log content:\n";
    echo file_get_contents($debug_log_file) . "\n";
} else {
    echo "Email debug log file does not exist.\n";
}

echo "All tests completed.\n";
