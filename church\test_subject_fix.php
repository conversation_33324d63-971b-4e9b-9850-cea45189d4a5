<?php
// Set error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Include configuration
require_once 'config.php';
require_once 'send_birthday_reminders.php';

// Initialize BirthdayReminder
$birthdayReminder = new BirthdayReminder($pdo);

// Get template with the problematic subject
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE subject LIKE '%Happy Birthday, {full_name}!%'");
$stmt->execute();
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    die("Could not find template with 'Happy Birthday, {full_name}!' subject");
}

// Create test data for member
$testMember = [
    'id' => 123,
    'full_name' => '<PERSON>',
    'first_name' => '<PERSON>',
    'last_name' => '<PERSON>',
    'email' => '<EMAIL>',
    'birth_date' => '1990-03-22',
];

echo "<h1>Testing Subject Line Replacement</h1>";
echo "<p><strong>Original Template Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";

// Test using the standard placeholder replacement
$subject1 = replaceTemplatePlaceholders($template['subject'], $testMember);
echo "<p><strong>After replaceTemplatePlaceholders:</strong> " . htmlspecialchars($subject1) . "</p>";

// Test using the sanitizeSubjectLine method (which we modified)
$reflectionMethod = new ReflectionMethod('BirthdayReminder', 'sanitizeSubjectLine');
$reflectionMethod->setAccessible(true);
$subject2 = $reflectionMethod->invoke($birthdayReminder, $template['subject'], $testMember);
echo "<p><strong>After sanitizeSubjectLine:</strong> " . htmlspecialchars($subject2) . "</p>";

// In the real system, both are called in sequence
$subject3 = replaceTemplatePlaceholders($template['subject'], $testMember);
$subject3 = $reflectionMethod->invoke($birthdayReminder, $subject3, $testMember);
echo "<p><strong>After both processing steps:</strong> " . htmlspecialchars($subject3) . "</p>";

// Test the original object using processTemplate
$reflectionProcess = new ReflectionMethod('BirthdayReminder', 'processTemplate');
$reflectionProcess->setAccessible(true);
$processed = $reflectionProcess->invoke($birthdayReminder, $template['subject'], $testMember);
echo "<p><strong>Using processTemplate method:</strong> " . htmlspecialchars($processed) . "</p>";

echo "<p><a href='admin/send_birthday_emails.php'>Go to Birthday Email Sender</a></p>"; 