<?php
/**
 * Comprehensive Production Readiness Test
 * Tests all critical systems for deployment readiness
 */

require_once 'config.php';

// Start output buffering for clean HTML output
ob_start();

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Production Readiness Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .test-item { margin: 10px 0; padding: 8px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .test-pass { border-left-color: #28a745; }
        .test-fail { border-left-color: #dc3545; }
        .test-warn { border-left-color: #ffc107; }
        h1, h2, h3 { color: #333; }
        .cron-command { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 5px 0; }
        .url-list { background: #f8f9fa; padding: 10px; border-radius: 4px; }
        .url-list a { display: block; margin: 5px 0; color: #007bff; text-decoration: none; }
        .url-list a:hover { text-decoration: underline; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
        th { background: #f8f9fa; }
        .status-icon { font-weight: bold; }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        .warn { color: #ffc107; }
    </style>
</head>
<body>
<div class='container'>
<h1>🚀 Production Readiness Test Report</h1>
<p><strong>Generated:</strong> " . date('Y-m-d H:i:s') . "</p>
<p><strong>Environment:</strong> " . (isset($environment) ? $environment : 'Unknown') . "</p>
<p><strong>Site URL:</strong> " . (defined('SITE_URL') ? SITE_URL : 'Not defined') . "</p>";

// Initialize test results
$testResults = [
    'total' => 0,
    'passed' => 0,
    'failed' => 0,
    'warnings' => 0
];

function runTest($name, $test, $critical = true) {
    global $testResults;
    $testResults['total']++;
    
    try {
        $result = $test();
        if ($result['status'] === 'pass') {
            $testResults['passed']++;
            $class = 'test-pass';
            $icon = '✅';
        } elseif ($result['status'] === 'warn') {
            $testResults['warnings']++;
            $class = 'test-warn';
            $icon = '⚠️';
        } else {
            $testResults['failed']++;
            $class = 'test-fail';
            $icon = '❌';
        }
        
        echo "<div class='test-item $class'>";
        echo "<strong>$icon $name</strong><br>";
        echo $result['message'];
        if (isset($result['details'])) {
            echo "<br><small>" . $result['details'] . "</small>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        $testResults['failed']++;
        echo "<div class='test-item test-fail'>";
        echo "<strong>❌ $name</strong><br>";
        echo "Test failed with exception: " . $e->getMessage();
        echo "</div>";
    }
}

// Test 1: Database Connection
echo "<div class='test-section'>";
echo "<h2>🗄️ Database Connection Tests</h2>";

runTest("Database Connection", function() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT 1");
        return ['status' => 'pass', 'message' => 'Database connection successful'];
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'Database connection failed: ' . $e->getMessage()];
    }
});

runTest("Required Tables Check", function() {
    global $pdo;
    $requiredTables = [
        'members', 'email_templates', 'email_logs', 'events', 'event_categories',
        'email_tracking', 'scheduled_emails', 'email_queue', 'settings'
    ];
    
    $missingTables = [];
    foreach ($requiredTables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() === 0) {
                $missingTables[] = $table;
            }
        } catch (Exception $e) {
            $missingTables[] = $table . " (error: " . $e->getMessage() . ")";
        }
    }
    
    if (empty($missingTables)) {
        return ['status' => 'pass', 'message' => 'All required tables exist'];
    } else {
        return ['status' => 'fail', 'message' => 'Missing tables: ' . implode(', ', $missingTables)];
    }
});

echo "</div>";

// Test 2: Email System
echo "<div class='test-section'>";
echo "<h2>📧 Email System Tests</h2>";

runTest("Email Settings Configuration", function() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($settings && !empty($settings['smtp_host'])) {
            return ['status' => 'pass', 'message' => 'Email settings configured', 
                   'details' => 'SMTP Host: ' . $settings['smtp_host']];
        } else {
            return ['status' => 'warn', 'message' => 'Email settings not fully configured'];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'Cannot check email settings: ' . $e->getMessage()];
    }
});

runTest("Email Templates Check", function() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM email_templates");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($count > 0) {
            return ['status' => 'pass', 'message' => "Found $count email templates"];
        } else {
            return ['status' => 'warn', 'message' => 'No email templates found'];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'Cannot check email templates: ' . $e->getMessage()];
    }
});

echo "</div>";

// Test 3: File System
echo "<div class='test-section'>";
echo "<h2>📁 File System Tests</h2>";

runTest("Upload Directory Writable", function() {
    $uploadDir = __DIR__ . '/uploads';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    if (is_writable($uploadDir)) {
        return ['status' => 'pass', 'message' => 'Upload directory is writable'];
    } else {
        return ['status' => 'fail', 'message' => 'Upload directory is not writable'];
    }
});

runTest("Logs Directory Writable", function() {
    $logsDir = __DIR__ . '/logs';
    if (!is_dir($logsDir)) {
        mkdir($logsDir, 0755, true);
    }
    
    if (is_writable($logsDir)) {
        return ['status' => 'pass', 'message' => 'Logs directory is writable'];
    } else {
        return ['status' => 'fail', 'message' => 'Logs directory is not writable'];
    }
});

runTest("Required PHP Extensions", function() {
    $requiredExtensions = ['pdo', 'pdo_mysql', 'curl', 'gd', 'mbstring', 'openssl'];
    $missingExtensions = [];
    
    foreach ($requiredExtensions as $ext) {
        if (!extension_loaded($ext)) {
            $missingExtensions[] = $ext;
        }
    }
    
    if (empty($missingExtensions)) {
        return ['status' => 'pass', 'message' => 'All required PHP extensions are loaded'];
    } else {
        return ['status' => 'fail', 'message' => 'Missing PHP extensions: ' . implode(', ', $missingExtensions)];
    }
});

echo "</div>";

// Test 4: Cron Jobs
echo "<div class='test-section'>";
echo "<h2>⏰ Cron Job Tests</h2>";

$cronJobs = [
    'birthday_reminders.php' => 'Birthday Reminders',
    'process_email_queue.php' => 'Email Queue Processing',
    'process_scheduled_emails.php' => 'Scheduled Emails',
    'event_reminders.php' => 'Event Reminders',
    'system_cleanup.php' => 'System Cleanup'
];

foreach ($cronJobs as $file => $name) {
    runTest("$name Cron Script", function() use ($file) {
        $cronFile = __DIR__ . '/cron/' . $file;
        if (file_exists($cronFile)) {
            return ['status' => 'pass', 'message' => 'Cron script exists and is readable'];
        } else {
            return ['status' => 'fail', 'message' => 'Cron script not found'];
        }
    });
}

echo "</div>";

// Test 5: Shortcode System
echo "<div class='test-section'>";
echo "<h2>🔧 Shortcode System Tests</h2>";

runTest("Shortcode Functions Available", function() {
    $shortcodeFunctions = ['process_shortcodes', 'get_member_shortcodes'];
    $missingFunctions = [];

    foreach ($shortcodeFunctions as $func) {
        if (!function_exists($func)) {
            $missingFunctions[] = $func;
        }
    }

    if (empty($missingFunctions)) {
        return ['status' => 'pass', 'message' => 'All shortcode functions are available'];
    } else {
        return ['status' => 'warn', 'message' => 'Missing shortcode functions: ' . implode(', ', $missingFunctions)];
    }
});

echo "</div>";

// Test 6: Security
echo "<div class='test-section'>";
echo "<h2>🔒 Security Tests</h2>";

runTest("Cron Security Key", function() {
    if (defined('SECURE_CRON_KEY') && !empty(SECURE_CRON_KEY)) {
        return ['status' => 'pass', 'message' => 'Cron security key is defined'];
    } else {
        return ['status' => 'fail', 'message' => 'Cron security key is not defined'];
    }
});

runTest("Admin Authentication", function() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM admins");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        if ($count > 0) {
            return ['status' => 'pass', 'message' => "Found $count admin accounts"];
        } else {
            return ['status' => 'warn', 'message' => 'No admin accounts found'];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'Cannot check admin accounts: ' . $e->getMessage()];
    }
});

echo "</div>";

// Summary and Cron Job URLs
echo "<div class='test-section info'>";
echo "<h2>📊 Test Summary</h2>";
echo "<table>";
echo "<tr><th>Status</th><th>Count</th><th>Percentage</th></tr>";
echo "<tr><td class='pass'>✅ Passed</td><td>{$testResults['passed']}</td><td>" . round(($testResults['passed'] / $testResults['total']) * 100, 1) . "%</td></tr>";
echo "<tr><td class='warn'>⚠️ Warnings</td><td>{$testResults['warnings']}</td><td>" . round(($testResults['warnings'] / $testResults['total']) * 100, 1) . "%</td></tr>";
echo "<tr><td class='fail'>❌ Failed</td><td>{$testResults['failed']}</td><td>" . round(($testResults['failed'] / $testResults['total']) * 100, 1) . "%</td></tr>";
echo "<tr><th>Total Tests</th><th>{$testResults['total']}</th><th>100%</th></tr>";
echo "</table>";

$readinessScore = round((($testResults['passed'] + ($testResults['warnings'] * 0.5)) / $testResults['total']) * 100, 1);
echo "<h3>Production Readiness Score: $readinessScore%</h3>";

if ($readinessScore >= 90) {
    echo "<div class='success'><strong>✅ READY FOR PRODUCTION</strong><br>System is ready for deployment with minimal issues.</div>";
} elseif ($readinessScore >= 75) {
    echo "<div class='warning'><strong>⚠️ MOSTLY READY</strong><br>System is mostly ready but has some warnings that should be addressed.</div>";
} else {
    echo "<div class='error'><strong>❌ NOT READY</strong><br>System has critical issues that must be fixed before deployment.</div>";
}

echo "</div>";

// Cron Job URLs for Production
echo "<div class='test-section info'>";
echo "<h2>🔗 Production Cron Job URLs</h2>";
echo "<p><strong>Copy these URLs for your hosting provider's cron job setup:</strong></p>";

$baseUrl = defined('SITE_URL') ? SITE_URL : 'https://YOUR_DOMAIN.COM/YOUR_PATH';
$cronKey = defined('SECURE_CRON_KEY') ? SECURE_CRON_KEY : 'fac_2024_secure_cron_8x9q2p5m';

$cronUrls = [
    'Birthday Reminders (*/15 1 * * *)' => "$baseUrl/cron/birthday_reminders.php?cron_key=$cronKey",
    'Email Queue Processing (*/5 * * * *)' => "$baseUrl/cron/process_email_queue.php?cron_key=$cronKey",
    'Scheduled Emails (*/5 * * * *)' => "$baseUrl/cron/process_scheduled_emails.php?cron_key=$cronKey",
    'Event Reminders (0 */2 * * *)' => "$baseUrl/cron/event_reminders.php?cron_key=$cronKey",
    'System Cleanup (0 2 * * 0)' => "$baseUrl/cron/system_cleanup.php?cron_key=$cronKey"
];

echo "<div class='url-list'>";
foreach ($cronUrls as $name => $url) {
    echo "<strong>$name:</strong><br>";
    echo "<a href='$url' target='_blank'>$url</a><br><br>";
}
echo "</div>";

echo "<h3>Cron Command Examples:</h3>";
echo "<div class='cron-command'>";
echo "# Birthday Reminders (Every 15 minutes at 1 AM)<br>";
echo "*/15 1 * * * wget -q -O /dev/null \"$baseUrl/cron/birthday_reminders.php?cron_key=$cronKey\"<br><br>";
echo "# Email Queue Processing (Every 5 minutes)<br>";
echo "*/5 * * * * wget -q -O /dev/null \"$baseUrl/cron/process_email_queue.php?cron_key=$cronKey\"<br><br>";
echo "# Scheduled Emails (Every 5 minutes)<br>";
echo "*/5 * * * * wget -q -O /dev/null \"$baseUrl/cron/process_scheduled_emails.php?cron_key=$cronKey\"<br><br>";
echo "# Event Reminders (Every 2 hours)<br>";
echo "0 */2 * * * wget -q -O /dev/null \"$baseUrl/cron/event_reminders.php?cron_key=$cronKey\"<br><br>";
echo "# System Cleanup (Weekly - Sundays at 2 AM)<br>";
echo "0 2 * * 0 wget -q -O /dev/null \"$baseUrl/cron/system_cleanup.php?cron_key=$cronKey\"";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body></html>";

// Get the output and clean it
$output = ob_get_clean();
echo $output;
?>
