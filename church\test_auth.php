<?php
session_start();

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/classes/SecurityManager.php';
require_once __DIR__ . '/classes/UserAuthManager.php';

echo "<h1>Authentication Test</h1>";

echo "<h2>Session Data:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

try {
    $security = new SecurityManager($pdo);
    $userAuth = new UserAuthManager($pdo, $security);
    
    echo "<h2>Authentication Check:</h2>";
    $isAuth = $userAuth->isAuthenticated();
    echo "Is Authenticated: " . ($isAuth ? 'YES' : 'NO') . "<br>";
    
    if (isset($_SESSION['user_id'])) {
        echo "Session User ID: " . $_SESSION['user_id'] . "<br>";
        
        // Check if user exists in members table
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "✅ User found in members table: {$user['first_name']} {$user['last_name']}<br>";
        } else {
            echo "❌ User not found in members table<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>
