<?php
require_once 'config.php';

// Get the current month
$current_month = date('m');
$current_month_name = date('F');

// Get members with birthdays this month
echo "<h1>Raw SQL Query Results</h1>";
$stmt = $pdo->prepare("SELECT id, full_name, birth_date, email, phone_number FROM members WHERE MONTH(birth_date) = ? ORDER BY DAY(birth_date)");
$stmt->execute([$current_month]);
$birthdays = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<pre>";
print_r($birthdays);
echo "</pre>";

// Add day of the month to each member - IMPORTANT: Create a copy to avoid reference issues
$processed_birthdays = [];
foreach ($birthdays as $member) {
    $member_copy = $member;
    $member_copy['day'] = date('j', strtotime($member['birth_date']));
    $processed_birthdays[] = $member_copy;
}

echo "<h1>After Adding Day Property</h1>";
echo "<pre>";
print_r($processed_birthdays);
echo "</pre>";

echo "<h1>Birthday Celebrants for {$current_month_name}</h1>";

if (count($processed_birthdays) > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Name</th><th>Birth Date</th><th>Email</th></tr>";
    
    // Loop through all birthday members with index for debugging
    foreach ($processed_birthdays as $index => $member) {
        echo "<tr>";
        echo "<td>" . $member['id'] . " (Index: $index)</td>";
        echo "<td>" . htmlspecialchars($member['full_name']) . "</td>";
        echo "<td>" . date('d M', strtotime($member['birth_date'])) . "</td>";
        echo "<td>" . htmlspecialchars($member['email']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No birthday celebrants for {$current_month_name}.</p>";
}
?> 