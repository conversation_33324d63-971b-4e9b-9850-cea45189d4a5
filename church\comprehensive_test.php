<?php
/**
 * Comprehensive Test Suite for Freedom Assembly Church Management System
 * Tests: Database, Email, Registration, Admin Panel, Security, Links, Functions
 * 
 * Usage: Navigate to http://localhost/campaign/church/comprehensive_test.php
 */

// Include configuration
require_once 'config.php';

// Test configuration
$TEST_EMAIL = '<EMAIL>';
$TEST_RESULTS = [];

// HTML Header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Church Management System - Comprehensive Test Results</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .test-warning { color: #ffc107; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result-summary { background: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🔍 Church Management System - Comprehensive Test Suite</h1>
        <div class="alert alert-info">
            <strong>Test Email:</strong> <?php echo $TEST_EMAIL; ?><br>
            <strong>Test Time:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
            <strong>Server:</strong> <?php echo $_SERVER['HTTP_HOST']; ?>
        </div>

<?php

// Function to log test results
function logTest($category, $test_name, $status, $message, $details = '') {
    global $TEST_RESULTS;
    $TEST_RESULTS[] = [
        'category' => $category,
        'test' => $test_name,
        'status' => $status,
        'message' => $message,
        'details' => $details
    ];
    
    $class = $status === 'PASS' ? 'test-pass' : ($status === 'FAIL' ? 'test-fail' : 'test-warning');
    echo "<div class='$class'><strong>[$status]</strong> $test_name: $message";
    if ($details) echo "<br><small>$details</small>";
    echo "</div>";
}

// Test 1: Database Connectivity and Structure
echo "<div class='test-section'>";
echo "<h3>📊 Test 1: Database Connectivity and Structure</h3>";

try {
    // Test database connection
    $stmt = $pdo->query("SELECT 1");
    logTest('Database', 'Connection Test', 'PASS', 'Database connection successful');
    
    // Test critical tables
    $critical_tables = ['members', 'admins', 'email_templates', 'email_logs', 'settings'];
    foreach ($critical_tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            logTest('Database', "Table: $table", 'PASS', "Table exists with $count records");
        } catch (Exception $e) {
            logTest('Database', "Table: $table", 'FAIL', 'Table missing or inaccessible', $e->getMessage());
        }
    }
    
    // Test member with test email
    $stmt = $pdo->prepare("SELECT * FROM members WHERE email = ?");
    $stmt->execute([$TEST_EMAIL]);
    $test_member = $stmt->fetch();
    
    if ($test_member) {
        logTest('Database', 'Test Member', 'PASS', "Test member found: {$test_member['full_name']}");
    } else {
        logTest('Database', 'Test Member', 'WARNING', 'Test member not found in database');
    }
    
} catch (Exception $e) {
    logTest('Database', 'Connection Test', 'FAIL', 'Database connection failed', $e->getMessage());
}

echo "</div>";

// Test 2: Email System Configuration
echo "<div class='test-section'>";
echo "<h3>📧 Test 2: Email System Configuration</h3>";

try {
    // Check email settings
    $stmt = $pdo->query("SELECT * FROM settings WHERE setting_key LIKE 'smtp_%' OR setting_key LIKE 'email_%'");
    $email_settings = $stmt->fetchAll();
    
    $required_settings = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password'];
    $settings_found = [];
    
    foreach ($email_settings as $setting) {
        $settings_found[] = $setting['setting_key'];
    }
    
    foreach ($required_settings as $required) {
        if (in_array($required, $settings_found)) {
            logTest('Email', "Setting: $required", 'PASS', 'Email setting configured');
        } else {
            logTest('Email', "Setting: $required", 'WARNING', 'Email setting not found');
        }
    }
    
    // Check email templates
    $stmt = $pdo->query("SELECT * FROM email_templates");
    $templates = $stmt->fetchAll();
    
    if (count($templates) > 0) {
        logTest('Email', 'Templates', 'PASS', count($templates) . ' email templates found');
        foreach ($templates as $template) {
            logTest('Email', "Template: {$template['template_name']}", 'PASS', 'Template available');
        }
    } else {
        logTest('Email', 'Templates', 'WARNING', 'No email templates found');
    }
    
} catch (Exception $e) {
    logTest('Email', 'Configuration Check', 'FAIL', 'Email configuration check failed', $e->getMessage());
}

echo "</div>";

// Test 3: Email Functionality Test
echo "<div class='test-section'>";
echo "<h3>✉️ Test 3: Email Functionality Test</h3>";

if (function_exists('sendEmail')) {
    try {
        // Test email sending (if function exists)
        $test_subject = "Church System Test - " . date('Y-m-d H:i:s');
        $test_message = "This is a test email from the Church Management System comprehensive test suite.";
        
        // Note: We'll simulate the email test without actually sending
        logTest('Email', 'Send Function', 'PASS', 'sendEmail function is available');
        logTest('Email', 'Test Email Prepared', 'PASS', "Test email prepared for $TEST_EMAIL");
        
        // Check if PHPMailer is available
        if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
            logTest('Email', 'PHPMailer', 'PASS', 'PHPMailer library is available');
        } else {
            logTest('Email', 'PHPMailer', 'WARNING', 'PHPMailer library not found');
        }
        
    } catch (Exception $e) {
        logTest('Email', 'Functionality Test', 'FAIL', 'Email functionality test failed', $e->getMessage());
    }
} else {
    logTest('Email', 'Send Function', 'WARNING', 'sendEmail function not found');
}

echo "</div>";

// Test 4: Birthday Email System
echo "<div class='test-section'>";
echo "<h3>🎂 Test 4: Birthday Email System</h3>";

try {
    // Check for birthday-related files
    $birthday_files = [
        'birthday_reminders.php',
        'send_birthday_reminders.php',
        'test_birthday_email.php',
        'cron_birthday_reminders.php'
    ];
    
    foreach ($birthday_files as $file) {
        if (file_exists($file)) {
            logTest('Birthday', "File: $file", 'PASS', 'Birthday file exists');
        } else {
            logTest('Birthday', "File: $file", 'WARNING', 'Birthday file not found');
        }
    }
    
    // Check for today's birthdays
    $today = date('m-d');
    $stmt = $pdo->prepare("SELECT * FROM members WHERE DATE_FORMAT(birth_date, '%m-%d') = ?");
    $stmt->execute([$today]);
    $today_birthdays = $stmt->fetchAll();
    
    logTest('Birthday', 'Today\'s Birthdays', 'PASS', count($today_birthdays) . ' birthdays today');
    
    // Check if test member has birthday today
    if ($test_member && date('m-d', strtotime($test_member['birth_date'])) === $today) {
        logTest('Birthday', 'Test Member Birthday', 'PASS', 'Test member has birthday today - perfect for testing!');
    } else {
        logTest('Birthday', 'Test Member Birthday', 'WARNING', 'Test member does not have birthday today');
    }
    
} catch (Exception $e) {
    logTest('Birthday', 'System Check', 'FAIL', 'Birthday system check failed', $e->getMessage());
}

echo "</div>";

// Test 5: File and Directory Structure
echo "<div class='test-section'>";
echo "<h3>📁 Test 5: File and Directory Structure</h3>";

$critical_files = [
    'config.php' => 'Configuration file',
    'index.php' => 'Registration page',
    'admin/index.php' => 'Admin dashboard',
    'process_registration.php' => 'Registration processor',
    'composer.json' => 'Dependencies configuration'
];

$critical_dirs = [
    'admin' => 'Admin panel directory',
    'api' => 'API endpoints directory',
    'assets' => 'Assets directory',
    'uploads' => 'Upload directory'
];

foreach ($critical_files as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        logTest('Files', $description, 'PASS', "File exists ($size bytes)");
    } else {
        logTest('Files', $description, 'FAIL', 'Critical file missing');
    }
}

foreach ($critical_dirs as $dir => $description) {
    if (is_dir($dir)) {
        $files = count(scandir($dir)) - 2; // Subtract . and ..
        logTest('Files', $description, 'PASS', "Directory exists ($files items)");
    } else {
        logTest('Files', $description, 'WARNING', 'Directory not found');
    }
}

echo "</div>";

// Test 6: Security Check
echo "<div class='test-section'>";
echo "<h3>🔒 Test 6: Security Check</h3>";

// Check if sensitive files are protected
$sensitive_files = ['.env', 'config.php', 'composer.json'];
foreach ($sensitive_files as $file) {
    if (file_exists($file)) {
        // In a real scenario, these should not be directly accessible via web
        logTest('Security', "Protected File: $file", 'WARNING', 'File exists - ensure web access is blocked');
    }
}

// Check for SQL injection protection (basic check)
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM members WHERE email = ?");
    logTest('Security', 'Prepared Statements', 'PASS', 'Database uses prepared statements');
} catch (Exception $e) {
    logTest('Security', 'Prepared Statements', 'FAIL', 'Prepared statement test failed');
}

// Check session security
if (session_status() === PHP_SESSION_ACTIVE || isset($_SESSION)) {
    logTest('Security', 'Session Management', 'PASS', 'Session management active');
} else {
    logTest('Security', 'Session Management', 'WARNING', 'Session management not detected');
}

echo "</div>";
?>

        <!-- Test Results Summary -->
        <div class="result-summary">
            <h3>📊 Test Results Summary</h3>
            <?php
            $total_tests = count($TEST_RESULTS);
            $passed = count(array_filter($TEST_RESULTS, function($r) { return $r['status'] === 'PASS'; }));
            $failed = count(array_filter($TEST_RESULTS, function($r) { return $r['status'] === 'FAIL'; }));
            $warnings = count(array_filter($TEST_RESULTS, function($r) { return $r['status'] === 'WARNING'; }));
            
            echo "<div class='row'>";
            echo "<div class='col-md-3'><div class='alert alert-success'>✅ Passed: $passed</div></div>";
            echo "<div class='col-md-3'><div class='alert alert-danger'>❌ Failed: $failed</div></div>";
            echo "<div class='col-md-3'><div class='alert alert-warning'>⚠️ Warnings: $warnings</div></div>";
            echo "<div class='col-md-3'><div class='alert alert-info'>📊 Total: $total_tests</div></div>";
            echo "</div>";
            
            $success_rate = round(($passed / $total_tests) * 100, 1);
            echo "<div class='alert alert-primary'>";
            echo "<strong>Overall Success Rate: $success_rate%</strong><br>";
            if ($success_rate >= 80) {
                echo "🎉 Excellent! The system is functioning well.";
            } elseif ($success_rate >= 60) {
                echo "👍 Good! Some areas need attention.";
            } else {
                echo "⚠️ Needs attention! Several issues detected.";
            }
            echo "</div>";
            ?>
        </div>

        <!-- Detailed Test Log -->
        <div class="test-section">
            <h3>📋 Detailed Test Log</h3>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Test</th>
                            <th>Status</th>
                            <th>Message</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($TEST_RESULTS as $result): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($result['category']); ?></td>
                            <td><?php echo htmlspecialchars($result['test']); ?></td>
                            <td>
                                <span class="badge bg-<?php 
                                    echo $result['status'] === 'PASS' ? 'success' : 
                                        ($result['status'] === 'FAIL' ? 'danger' : 'warning'); 
                                ?>">
                                    <?php echo $result['status']; ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($result['message']); ?></td>
                            <td><?php echo htmlspecialchars($result['details']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Action Items -->
        <div class="test-section">
            <h3>🎯 Recommended Actions</h3>
            <div class="alert alert-info">
                <h5>Based on test results, consider these actions:</h5>
                <ul>
                    <?php if ($failed > 0): ?>
                    <li><strong>High Priority:</strong> Address failed tests immediately</li>
                    <?php endif; ?>
                    <?php if ($warnings > 0): ?>
                    <li><strong>Medium Priority:</strong> Review warning items for potential improvements</li>
                    <?php endif; ?>
                    <li><strong>Email Testing:</strong> Send a test email to <?php echo $TEST_EMAIL; ?> to verify email functionality</li>
                    <li><strong>Birthday Testing:</strong> Test birthday email system with today's birthdays</li>
                    <li><strong>Security Review:</strong> Ensure sensitive files are not web-accessible</li>
                    <li><strong>Regular Testing:</strong> Run this test suite regularly to monitor system health</li>
                </ul>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-primary">Return to Registration</a>
            <a href="admin/index.php" class="btn btn-secondary">Admin Panel</a>
            <button onclick="window.location.reload()" class="btn btn-success">Rerun Tests</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
