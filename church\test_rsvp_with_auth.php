<?php
session_start();

// Include configuration and classes
require_once 'config.php';
require_once 'classes/SecurityManager.php';
require_once 'classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

echo "<h2>RSVP Test with Authentication</h2>";

// Check if we need to simulate login
if (!$userAuth->isAuthenticated()) {
    echo "<h3>Simulating User Login</h3>";
    
    // Get a test user from the database
    try {
        $stmt = $pdo->prepare("SELECT id, full_name, email FROM members WHERE status = 'active' AND password_hash IS NOT NULL LIMIT 1");
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            // Manually set session variables to simulate login
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['full_name'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['is_authenticated'] = true;

            echo "✓ Simulated login for user: " . $user['full_name'] . " (ID: " . $user['id'] . ")<br>";
        } else {
            echo "✗ No active users found in database<br>";
            exit;
        }
    } catch (Exception $e) {
        echo "✗ Error finding user: " . $e->getMessage() . "<br>";
        exit;
    }
}

// Now test authentication
echo "<h3>Authentication Check:</h3>";
if ($userAuth->isAuthenticated()) {
    echo "✓ User is authenticated<br>";
    echo "User ID: " . $_SESSION['user_id'] . "<br>";
    echo "Username: " . $_SESSION['username'] . "<br>";
} else {
    echo "✗ User is not authenticated<br>";
    exit;
}

// Test RSVP submission
echo "<h3>Testing RSVP Submission:</h3>";

// Simulate POST data
$_POST['event_id'] = '3';
$_POST['status'] = 'maybe';
$_POST['notes'] = 'Test RSVP from authenticated session';
$_POST['action'] = 'rsvp';

echo "POST data:<br>";
echo "event_id: " . $_POST['event_id'] . "<br>";
echo "status: " . $_POST['status'] . "<br>";
echo "notes: " . $_POST['notes'] . "<br>";

// Include the RSVP handler logic directly
echo "<h3>RSVP Handler Logic Test:</h3>";

// Validate input (using $_POST directly since filter_input doesn't work with manually set values)
$eventId = isset($_POST['event_id']) ? (int)$_POST['event_id'] : 0;
$status = isset($_POST['status']) ? trim($_POST['status']) : '';
$notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';

if (!$eventId || !$status) {
    echo "✗ Missing required fields<br>";
    exit;
}

$validStatuses = ['attending', 'maybe', 'not_attending'];
if (!in_array($status, $validStatuses)) {
    echo "✗ Invalid RSVP status<br>";
    exit;
}

echo "✓ Input validation passed<br>";

// Check if event exists and is active
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND is_active = 1");
    $stmt->execute([$eventId]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        echo "✗ Event not found or not active<br>";
        exit;
    }
    
    echo "✓ Event found: " . $event['title'] . "<br>";
    
    // Check for existing RSVP
    $stmt = $pdo->prepare("SELECT * FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$eventId, $_SESSION['user_id']]);
    $existingRsvp = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existingRsvp) {
        // Update existing RSVP
        $stmt = $pdo->prepare("UPDATE event_rsvps SET status = ?, notes = ?, updated_at = NOW() WHERE event_id = ? AND user_id = ?");
        $result = $stmt->execute([$status, $notes, $eventId, $_SESSION['user_id']]);
        
        if ($result) {
            echo "✓ RSVP updated successfully<br>";
        } else {
            echo "✗ Failed to update RSVP<br>";
        }
    } else {
        // Create new RSVP
        $stmt = $pdo->prepare("INSERT INTO event_rsvps (event_id, user_id, status, notes) VALUES (?, ?, ?, ?)");
        $result = $stmt->execute([$eventId, $_SESSION['user_id'], $status, $notes]);
        
        if ($result) {
            echo "✓ RSVP created successfully<br>";
        } else {
            echo "✗ Failed to create RSVP<br>";
        }
    }
    
} catch (Exception $e) {
    echo "✗ Database error: " . $e->getMessage() . "<br>";
}

echo "<h3>Final RSVP Status:</h3>";
try {
    $stmt = $pdo->prepare("SELECT * FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$eventId, $_SESSION['user_id']]);
    $rsvp = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($rsvp) {
        echo "Event ID: " . $rsvp['event_id'] . "<br>";
        echo "User ID: " . $rsvp['user_id'] . "<br>";
        echo "Status: " . $rsvp['status'] . "<br>";
        echo "Notes: " . $rsvp['notes'] . "<br>";
        echo "Created: " . $rsvp['created_at'] . "<br>";
        echo "Updated: " . $rsvp['updated_at'] . "<br>";
    } else {
        echo "No RSVP found<br>";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}

?>
