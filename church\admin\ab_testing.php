<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_test'])) {
        try {
            $testName = trim($_POST['test_name']);
            $templateA = intval($_POST['template_a']);
            $templateB = intval($_POST['template_b']);
            $splitPercentage = intval($_POST['split_percentage']);
            $description = trim($_POST['description']);
            
            // Validate inputs
            if (empty($testName) || $templateA <= 0 || $templateB <= 0) {
                throw new Exception("Please fill in all required fields.");
            }
            
            if ($templateA === $templateB) {
                throw new Exception("Template A and Template B must be different.");
            }
            
            if ($splitPercentage < 10 || $splitPercentage > 90) {
                throw new Exception("Split percentage must be between 10% and 90%.");
            }
            
            // Create A/B test
            $stmt = $pdo->prepare("INSERT INTO ab_tests (test_name, template_a_id, template_b_id, split_percentage, description, status, created_at) VALUES (?, ?, ?, ?, ?, 'active', NOW())");
            $stmt->execute([$testName, $templateA, $templateB, $splitPercentage, $description]);
            
            $success = "A/B test created successfully!";
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if (isset($_POST['end_test'])) {
        try {
            $testId = intval($_POST['test_id']);
            $stmt = $pdo->prepare("UPDATE ab_tests SET status = 'completed', ended_at = NOW() WHERE id = ?");
            $stmt->execute([$testId]);
            
            $success = "A/B test ended successfully!";
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get available templates
$templates = [];
try {
    $stmt = $pdo->prepare("SELECT id, template_name FROM email_templates WHERE status = 'active' ORDER BY template_name");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching templates: " . $e->getMessage();
}

// Get A/B tests with results
$abTests = [];
try {
    $query = "SELECT 
                at.*,
                ta.template_name as template_a_name,
                tb.template_name as template_b_name,
                COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id THEN el.id END) as emails_sent_a,
                COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id THEN el.id END) as emails_sent_b,
                COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id AND et.opened_at IS NOT NULL THEN et.id END) as opens_a,
                COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id AND et.opened_at IS NOT NULL THEN et.id END) as opens_b,
                ROUND(
                    COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id AND et.opened_at IS NOT NULL THEN et.id END) / 
                    NULLIF(COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id THEN el.id END), 0) * 100, 
                    2
                ) as open_rate_a,
                ROUND(
                    COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id AND et.opened_at IS NOT NULL THEN et.id END) / 
                    NULLIF(COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id THEN el.id END), 0) * 100, 
                    2
                ) as open_rate_b
              FROM 
                ab_tests at
              LEFT JOIN 
                email_templates ta ON at.template_a_id = ta.id
              LEFT JOIN 
                email_templates tb ON at.template_b_id = tb.id
              LEFT JOIN 
                email_logs el ON (el.template_id = at.template_a_id OR el.template_id = at.template_b_id) 
                                AND el.sent_at >= at.created_at 
                                AND (at.ended_at IS NULL OR el.sent_at <= at.ended_at)
              LEFT JOIN 
                email_tracking et ON el.member_id = et.member_id AND DATE(el.sent_at) = DATE(et.sent_at)
              GROUP BY 
                at.id
              ORDER BY 
                at.created_at DESC";
                
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $abTests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error = "Error fetching A/B tests: " . $e->getMessage();
}

// Set page variables
$page_title = 'A/B Testing';
$page_header = 'A/B Testing';
$page_description = 'Create and manage A/B tests for email templates.';

// Include header
include 'includes/header.php';
?>

<style>
.test-card {
    transition: all 0.3s ease;
}
.test-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.winner-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}
.template-comparison {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    position: relative;
}
.template-comparison.winner {
    border-color: #198754;
    background-color: #f8fff9;
}
.vs-divider {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #6c757d;
    z-index: 5;
}
</style>

<!-- Page Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="bi bi-graph-up-arrow me-2"></i><?php echo $page_header; ?>
    </h1>
    <div>
        <a href="email_analytics.php" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left me-2"></i>Back to Analytics
        </a>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTestModal">
            <i class="bi bi-plus-circle me-2"></i>Create A/B Test
        </button>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
<?php endif; ?>

<?php if (isset($success)): ?>
    <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
<?php endif; ?>

<!-- A/B Tests List -->
<div class="row">
    <?php if (empty($abTests)): ?>
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-graph-up-arrow display-4 text-muted"></i>
                    <h4 class="mt-3">No A/B Tests Yet</h4>
                    <p class="text-muted">Create your first A/B test to compare email template performance.</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTestModal">
                        <i class="bi bi-plus-circle me-2"></i>Create A/B Test
                    </button>
                </div>
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($abTests as $test): ?>
            <div class="col-lg-6 mb-4">
                <div class="card test-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><?php echo htmlspecialchars($test['test_name']); ?></h5>
                        <div>
                            <span class="badge bg-<?php echo $test['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                <?php echo ucfirst($test['status']); ?>
                            </span>
                            <?php if ($test['status'] === 'active'): ?>
                                <form method="post" class="d-inline">
                                    <input type="hidden" name="test_id" value="<?php echo $test['id']; ?>">
                                    <button type="submit" name="end_test" class="btn btn-sm btn-outline-danger ms-2" 
                                            onclick="return confirm('Are you sure you want to end this test?')">
                                        End Test
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($test['description'])): ?>
                            <p class="text-muted mb-3"><?php echo htmlspecialchars($test['description']); ?></p>
                        <?php endif; ?>
                        
                        <div class="row position-relative">
                            <!-- Template A -->
                            <div class="col-6">
                                <div class="template-comparison p-3 <?php echo ($test['open_rate_a'] > $test['open_rate_b'] && $test['emails_sent_a'] > 0 && $test['emails_sent_b'] > 0) ? 'winner' : ''; ?>">
                                    <?php if ($test['open_rate_a'] > $test['open_rate_b'] && $test['emails_sent_a'] > 0 && $test['emails_sent_b'] > 0): ?>
                                        <span class="badge bg-success winner-badge">Winner!</span>
                                    <?php endif; ?>
                                    <h6 class="text-primary">Template A (<?php echo $test['split_percentage']; ?>%)</h6>
                                    <p class="mb-2"><strong><?php echo htmlspecialchars($test['template_a_name']); ?></strong></p>
                                    <div class="mb-2">
                                        <small class="text-muted">Emails Sent:</small>
                                        <span class="badge bg-info"><?php echo number_format($test['emails_sent_a']); ?></span>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">Opens:</small>
                                        <span class="badge bg-success"><?php echo number_format($test['opens_a']); ?></span>
                                    </div>
                                    <div>
                                        <small class="text-muted">Open Rate:</small>
                                        <span class="fw-bold text-primary"><?php echo $test['open_rate_a'] ?: '0'; ?>%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- VS Divider -->
                            <div class="vs-divider">VS</div>
                            
                            <!-- Template B -->
                            <div class="col-6">
                                <div class="template-comparison p-3 <?php echo ($test['open_rate_b'] > $test['open_rate_a'] && $test['emails_sent_a'] > 0 && $test['emails_sent_b'] > 0) ? 'winner' : ''; ?>">
                                    <?php if ($test['open_rate_b'] > $test['open_rate_a'] && $test['emails_sent_a'] > 0 && $test['emails_sent_b'] > 0): ?>
                                        <span class="badge bg-success winner-badge">Winner!</span>
                                    <?php endif; ?>
                                    <h6 class="text-warning">Template B (<?php echo 100 - $test['split_percentage']; ?>%)</h6>
                                    <p class="mb-2"><strong><?php echo htmlspecialchars($test['template_b_name']); ?></strong></p>
                                    <div class="mb-2">
                                        <small class="text-muted">Emails Sent:</small>
                                        <span class="badge bg-info"><?php echo number_format($test['emails_sent_b']); ?></span>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">Opens:</small>
                                        <span class="badge bg-success"><?php echo number_format($test['opens_b']); ?></span>
                                    </div>
                                    <div>
                                        <small class="text-muted">Open Rate:</small>
                                        <span class="fw-bold text-warning"><?php echo $test['open_rate_b'] ?: '0'; ?>%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3 pt-3 border-top">
                            <small class="text-muted">
                                <i class="bi bi-calendar me-1"></i>
                                Created: <?php echo date('M j, Y g:i A', strtotime($test['created_at'])); ?>
                                <?php if ($test['ended_at']): ?>
                                    | Ended: <?php echo date('M j, Y g:i A', strtotime($test['ended_at'])); ?>
                                <?php endif; ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- Create A/B Test Modal -->
<div class="modal fade" id="createTestModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New A/B Test</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_name" class="form-label">Test Name *</label>
                                <input type="text" class="form-control" id="test_name" name="test_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="split_percentage" class="form-label">Template A Split % *</label>
                                <input type="number" class="form-control" id="split_percentage" name="split_percentage" 
                                       min="10" max="90" value="50" required>
                                <small class="text-muted">Template B will get the remaining percentage</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template_a" class="form-label">Template A *</label>
                                <select class="form-select" id="template_a" name="template_a" required>
                                    <option value="">Select Template A</option>
                                    <?php foreach ($templates as $template): ?>
                                        <option value="<?php echo $template['id']; ?>">
                                            <?php echo htmlspecialchars($template['template_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template_b" class="form-label">Template B *</label>
                                <select class="form-select" id="template_b" name="template_b" required>
                                    <option value="">Select Template B</option>
                                    <?php foreach ($templates as $template): ?>
                                        <option value="<?php echo $template['id']; ?>">
                                            <?php echo htmlspecialchars($template['template_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="Describe what you're testing (e.g., subject line variations, content changes, etc.)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="create_test" class="btn btn-primary">Create A/B Test</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Prevent selecting the same template for both A and B
document.getElementById('template_a').addEventListener('change', function() {
    const templateB = document.getElementById('template_b');
    const selectedValue = this.value;
    
    // Enable all options in template B
    Array.from(templateB.options).forEach(option => {
        option.disabled = false;
    });
    
    // Disable the selected option in template B
    if (selectedValue) {
        const optionToDisable = templateB.querySelector(`option[value="${selectedValue}"]`);
        if (optionToDisable) {
            optionToDisable.disabled = true;
        }
        
        // If template B currently has the same value, reset it
        if (templateB.value === selectedValue) {
            templateB.value = '';
        }
    }
});

document.getElementById('template_b').addEventListener('change', function() {
    const templateA = document.getElementById('template_a');
    const selectedValue = this.value;
    
    // Enable all options in template A
    Array.from(templateA.options).forEach(option => {
        option.disabled = false;
    });
    
    // Disable the selected option in template A
    if (selectedValue) {
        const optionToDisable = templateA.querySelector(`option[value="${selectedValue}"]`);
        if (optionToDisable) {
            optionToDisable.disabled = true;
        }
        
        // If template A currently has the same value, reset it
        if (templateA.value === selectedValue) {
            templateA.value = '';
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
