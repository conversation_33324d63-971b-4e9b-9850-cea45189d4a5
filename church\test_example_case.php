<?php
// Test the example case
require_once 'config.php';
require_once 'send_birthday_reminders.php';

// Initialize BirthdayReminder
$birthdayReminder = new BirthdayReminder($pdo);

// Get template 14
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = 14");
$stmt->execute();
$template = $stmt->fetch(PDO::FETCH_ASSOC);

// The exact example data from the email
$birthdayMember = [
    'id' => 123,
    'full_name' => 'Oneyeka',  // Just the first name as in the example
    'email' => '<EMAIL>',
    'birth_date' => '1952-03-22',  // To match the 73rd birthday in the example
];

$recipientMember = [
    'id' => 456,
    'full_name' => '<PERSON>',
    'email' => '<EMAIL>',
];

$daysUntil = 4;  // "in 4 days" from the example

echo "<h1>Testing Exact Example Case</h1>";
echo "<p><strong>Original subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";

// Process the subject
$processed_subject = $birthdayReminder->processBirthdayMemberTemplate($template['subject'], $recipientMember, $birthdayMember, $daysUntil);

echo "<p><strong>Processed subject:</strong> " . htmlspecialchars($processed_subject) . "</p>";

// Check if our fix resolves the issue
$expectedSubject = "Celebrate with us! Oneyeka's birthday is coming up in 4 days!";
echo "<p><strong>Expected subject:</strong> " . htmlspecialchars($expectedSubject) . "</p>";
echo "<p><strong>Does it match?</strong> " . ($processed_subject === $expectedSubject ? "YES - FIXED!" : "NO - Still an issue") . "</p>";

// Check for the specific duplication pattern from the example
$exampleDuplication = "OneyeaOneyeka";
$found = strpos($processed_subject, $exampleDuplication) !== false;
echo "<p><strong>Found duplication '" . htmlspecialchars($exampleDuplication) . "'?</strong> " . ($found ? "YES - PROBLEM!" : "NO - Good!") . "</p>";

// Print the duplication patterns handled by our code
echo "<h2>Duplication Patterns We're Handling:</h2>";
$patterns = [
    "birthdayMemberName . birthdayMemberName", 
    "birthdayMemberName . \"'\" . birthdayMemberName",
    "birthdayMemberName . \"'\" . birthdayMemberName . \"'s\"",
    "birthdayMemberName . \" \" . birthdayMemberName",
    "birthdayMemberName . \"'s \" . birthdayMemberName . \"'s\"",
    "\"Let's make \" . birthdayMemberName . birthdayMemberFullName",
    "birthdayMemberName . birthdayMemberFullName",
    "birthdayMemberFullName . \" \" . birthdayMemberFullName",
    "birthdayMemberFullName . birthdayMemberFullName"
];

echo "<ul>";
foreach ($patterns as $pattern) {
    echo "<li>" . htmlspecialchars($pattern) . "</li>";
}
echo "</ul>";

// Test the special case handling
echo "<h2>Special Case Detection:</h2>";
$isSubjectLine = (strlen($template['subject']) < 200 && strpos($template['subject'], "Celebrate with us!") !== false);
echo "<p>Is this a subject line? " . ($isSubjectLine ? "YES" : "NO") . "</p>";

$hasFullNamePlaceholder = strpos($template['subject'], '{birthday_member_full_name}') !== false;
echo "<p>Does it use full_name placeholder? " . ($hasFullNamePlaceholder ? "YES" : "NO") . "</p>";

// If both conditions are true, our fix should be working
if ($isSubjectLine && $hasFullNamePlaceholder) {
    echo "<p><strong>Our fix should handle this case!</strong></p>";
} else {
    echo "<p><strong>Potential issue: The special case detection is not triggering.</strong></p>";
}

// Debug log - what the actual method is doing
echo "<h2>Debug Information:</h2>";
error_log("DEBUG START");
$birthdayReminder->processBirthdayMemberTemplate($template['subject'], $recipientMember, $birthdayMember, $daysUntil);
echo "<p>Check the error log for detailed debug messages.</p>";

// Show regular replacements for clarity
$birthdayMemberName = explode(' ', $birthdayMember['full_name'])[0];
$birthdayMemberFullName = $birthdayMember['full_name'];
$daysText = "in $daysUntil days";

$regularReplacements = [
    '{birthday_member_name}' => $birthdayMemberName,
    '{birthday_member_first_name}' => $birthdayMemberName,
    '{birthday_member_full_name}' => $birthdayMemberFullName,
    '{days_text}' => $daysText,
];

echo "<p><strong>Regular replacements would give:</strong></p>";
$normalReplaced = str_replace(
    array_keys($regularReplacements),
    array_values($regularReplacements),
    $template['subject']
);
echo "<p>" . htmlspecialchars($normalReplaced) . "</p>"; 