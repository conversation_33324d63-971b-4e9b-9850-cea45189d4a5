<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Rendering Test</title>
    
    <!-- Bootstrap Icons CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .icon-test {
            font-size: 24px;
            margin: 10px;
            display: inline-block;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
        }
        
        /* Force font families */
        .bi-force {
            font-family: 'bootstrap-icons' !important;
        }
        
        .fa-force {
            font-family: 'Font Awesome 6 Free' !important;
            font-weight: 900 !important;
        }
        
        /* Direct Unicode test */
        .unicode-test::before {
            content: "\f425"; /* Bootstrap Icons house */
            font-family: 'bootstrap-icons' !important;
        }
        
        .unicode-fa-test::before {
            content: "\f015"; /* Font Awesome house */
            font-family: 'Font Awesome 6 Free' !important;
            font-weight: 900 !important;
        }
    </style>
</head>
<body>
    <h1>Icon Font Rendering Test</h1>
    
    <div class="test-section">
        <h2>1. Bootstrap Icons (Standard Classes)</h2>
        <div class="test-result">
            <span class="icon-test bi bi-house"></span> bi-house
            <span class="icon-test bi bi-people"></span> bi-people
            <span class="icon-test bi bi-calendar-event"></span> bi-calendar-event
            <span class="icon-test bi bi-speedometer2"></span> bi-speedometer2
        </div>
    </div>
    
    <div class="test-section">
        <h2>2. Font Awesome Icons (Standard Classes)</h2>
        <div class="test-result">
            <span class="icon-test fas fa-home"></span> fas fa-home
            <span class="icon-test fas fa-users"></span> fas fa-users
            <span class="icon-test fas fa-calendar-alt"></span> fas fa-calendar-alt
            <span class="icon-test fas fa-tachometer-alt"></span> fas fa-tachometer-alt
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. Bootstrap Icons (Forced Font Family)</h2>
        <div class="test-result">
            <span class="icon-test bi bi-house bi-force"></span> bi-house (forced)
            <span class="icon-test bi bi-people bi-force"></span> bi-people (forced)
        </div>
    </div>
    
    <div class="test-section">
        <h2>4. Font Awesome Icons (Forced Font Family)</h2>
        <div class="test-result">
            <span class="icon-test fas fa-home fa-force"></span> fas fa-home (forced)
            <span class="icon-test fas fa-users fa-force"></span> fas fa-users (forced)
        </div>
    </div>
    
    <div class="test-section">
        <h2>5. Direct Unicode Content Test</h2>
        <div class="test-result">
            <span class="icon-test unicode-test"></span> Bootstrap Icons Unicode (\\f425)
            <span class="icon-test unicode-fa-test"></span> Font Awesome Unicode (\\f015)
        </div>
    </div>
    
    <div class="test-section">
        <h2>6. SVG Icons Test (Alternative)</h2>
        <div class="test-result">
            <svg width="24" height="24" fill="currentColor" class="icon-test">
                <path d="M8 6.982C8 6.44 8.44 6 8.982 6h2.036c.542 0 .982.44.982.982v4.036c0 .542-.44.982-.982.982H8.982A.982.982 0 0 1 8 11.018V6.982zM8 13h3v3H8v-3z"/>
            </svg> SVG House Icon

            <svg width="24" height="24" fill="currentColor" class="icon-test">
                <path d="M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
            </svg> SVG Person Icon
        </div>
    </div>

    <div class="test-section">
        <h2>7. Font Loading Status</h2>
        <div class="test-result" id="font-status">
            Checking font loading status...
        </div>
    </div>

    <div class="test-section">
        <h2>8. Browser & System Info</h2>
        <div class="test-result" id="browser-info">
            Loading browser information...
        </div>
    </div>
    
    <script>
        // Check if fonts are loaded
        document.addEventListener('DOMContentLoaded', function() {
            const statusDiv = document.getElementById('font-status');
            const browserInfoDiv = document.getElementById('browser-info');

            // Browser and system information
            browserInfoDiv.innerHTML = `
                <strong>Browser Information:</strong><br>
                User Agent: ${navigator.userAgent}<br>
                Platform: ${navigator.platform}<br>
                Language: ${navigator.language}<br>
                Cookies Enabled: ${navigator.cookieEnabled}<br>
                Online: ${navigator.onLine}<br>
                Screen Resolution: ${screen.width}x${screen.height}<br>
                Color Depth: ${screen.colorDepth} bits
            `;

            // Check if document.fonts API is available
            if ('fonts' in document) {
                document.fonts.ready.then(function() {
                    const bootstrapIconsLoaded = document.fonts.check('16px bootstrap-icons');
                    const fontAwesomeLoaded = document.fonts.check('900 16px "Font Awesome 6 Free"');

                    // Get all loaded fonts
                    const loadedFonts = [];
                    for (const font of document.fonts) {
                        loadedFonts.push(`${font.family} (${font.style}, ${font.weight})`);
                    }

                    statusDiv.innerHTML = `
                        <strong>Font Loading Status:</strong><br>
                        Bootstrap Icons: ${bootstrapIconsLoaded ? '✅ Loaded' : '❌ Not Loaded'}<br>
                        Font Awesome: ${fontAwesomeLoaded ? '✅ Loaded' : '❌ Not Loaded'}<br>
                        Total fonts loaded: ${document.fonts.size}<br><br>
                        <strong>All Loaded Fonts:</strong><br>
                        ${loadedFonts.join('<br>')}
                    `;
                });
            } else {
                statusDiv.innerHTML = 'Font loading API not supported in this browser.';
            }
        });
    </script>
</body>
</html>
