<?php
session_start();
require_once __DIR__ . '/config.php';

// Simulate a logged-in user
$_SESSION['user_id'] = 1; // Assuming user ID 1 exists

echo "<h2>RSVP Fix Test</h2>";

// Test data
$testEventId = 3; // Summer Fellowship Gathering
$testStatus = 'maybe';
$testNotes = 'Testing the RSVP fix';

echo "<h3>Test Parameters:</h3>";
echo "Event ID: $testEventId<br>";
echo "Status: $testStatus<br>";
echo "Notes: $testNotes<br>";
echo "User ID: " . $_SESSION['user_id'] . "<br>";

// Simulate POST data
$_POST = [
    'event_id' => $testEventId,
    'status' => $testStatus,
    'notes' => $testNotes,
    'action' => 'rsvp'
];

echo "<h3>Simulated POST Data:</h3>";
echo "<pre>";
print_r($_POST);
echo "</pre>";

// Test the RSVP handler logic directly
echo "<h3>Testing RSVP Handler Logic:</h3>";

try {
    // Get and validate input
    $eventId = filter_input(INPUT_POST, 'event_id', FILTER_VALIDATE_INT);
    $status = filter_input(INPUT_POST, 'status', FILTER_SANITIZE_STRING);
    $notes = filter_input(INPUT_POST, 'notes', FILTER_SANITIZE_STRING);
    
    echo "Filtered Event ID: " . ($eventId ?: 'INVALID') . "<br>";
    echo "Filtered Status: " . ($status ?: 'EMPTY') . "<br>";
    echo "Filtered Notes: " . ($notes ?: 'EMPTY') . "<br>";
    
    if (!$eventId || !$status) {
        echo "<p style='color: red;'>❌ Missing required fields</p>";
        exit();
    }
    
    // Validate status
    $validStatuses = ['attending', 'maybe', 'not_attending'];
    if (!in_array($status, $validStatuses)) {
        echo "<p style='color: red;'>❌ Invalid RSVP status</p>";
        exit();
    }
    
    $userId = $_SESSION['user_id'];
    
    // Check if event exists and is active
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND is_active = 1");
    $stmt->execute([$eventId]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        echo "<p style='color: red;'>❌ Event not found or inactive</p>";
        exit();
    }
    
    echo "<h4>Event Details:</h4>";
    echo "Title: {$event['title']}<br>";
    echo "Date: {$event['event_date']}<br>";
    echo "Time: {$event['event_time']}<br>";
    
    // Check if event is in the future (with the fix)
    $eventDateTime = $event['event_date'] . ' ' . $event['event_time'];
    $eventTimestamp = strtotime($eventDateTime);
    $currentTimestamp = time();
    
    echo "Event DateTime: $eventDateTime<br>";
    echo "Event Timestamp: $eventTimestamp (" . date('Y-m-d H:i:s', $eventTimestamp) . ")<br>";
    echo "Current Timestamp: $currentTimestamp (" . date('Y-m-d H:i:s', $currentTimestamp) . ")<br>";
    echo "Is Future Event: " . ($eventTimestamp > $currentTimestamp ? 'Yes' : 'No') . "<br>";
    
    if ($eventTimestamp < $currentTimestamp) {
        echo "<p style='color: red;'>❌ Cannot RSVP to past events</p>";
        exit();
    }
    
    // Check if user already has an RSVP for this event
    $stmt = $pdo->prepare("SELECT id FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$eventId, $userId]);
    $existingRsvp = $stmt->fetch();
    
    if ($existingRsvp) {
        echo "<p style='color: blue;'>ℹ️ Updating existing RSVP</p>";
        
        // Update existing RSVP
        $stmt = $pdo->prepare("
            UPDATE event_rsvps 
            SET status = ?, notes = ?, updated_at = NOW()
            WHERE event_id = ? AND user_id = ?
        ");
        $stmt->execute([$status, $notes, $eventId, $userId]);
        $message = 'RSVP updated successfully!';
    } else {
        echo "<p style='color: blue;'>ℹ️ Creating new RSVP</p>";
        
        // Create new RSVP
        $stmt = $pdo->prepare("
            INSERT INTO event_rsvps (event_id, user_id, status, notes, created_at, updated_at)
            VALUES (?, ?, ?, ?, NOW(), NOW())
        ");
        $stmt->execute([$eventId, $userId, $status, $notes]);
        $message = 'RSVP submitted successfully!';
    }
    
    // Get updated counts
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(CASE WHEN status = 'attending' THEN 1 END) as attending_count,
            COUNT(CASE WHEN status = 'maybe' THEN 1 END) as maybe_count,
            COUNT(CASE WHEN status = 'not_attending' THEN 1 END) as not_attending_count
        FROM event_rsvps 
        WHERE event_id = ?
    ");
    $stmt->execute([$eventId]);
    $counts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✅ $message</p>";
    echo "<h4>Updated Counts:</h4>";
    echo "Attending: {$counts['attending_count']}<br>";
    echo "Maybe: {$counts['maybe_count']}<br>";
    echo "Not Attending: {$counts['not_attending_count']}<br>";
    
    $response = [
        'success' => true,
        'message' => $message,
        'status' => $status,
        'counts' => $counts
    ];
    
    echo "<h4>JSON Response:</h4>";
    echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ General error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='user/events.php'>Go to Events Page</a></p>";
?>
