<?php
// Simple test to check if the system is working
echo "<h1>Church Management System Test</h1>";

// Test 1: Check if config.php loads
echo "<h2>Test 1: Configuration</h2>";
try {
    require_once __DIR__ . '/config.php';
    echo "✅ Config loaded successfully<br>";
    echo "Database connection: " . ($pdo ? "✅ Connected" : "❌ Failed") . "<br>";
} catch (Exception $e) {
    echo "❌ Config error: " . $e->getMessage() . "<br>";
}

// Test 2: Check database tables
echo "<h2>Test 2: Database Tables</h2>";
try {
    $tables = ['users', 'events', 'event_rsvps', 'members'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $result = $stmt->fetch();
        echo "Table '$table': {$result['count']} records<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 3: Check if events exist
echo "<h2>Test 3: Events Data</h2>";
try {
    $stmt = $pdo->query("SELECT id, title, event_date, event_time, is_active FROM events ORDER BY event_date");
    $events = $stmt->fetchAll();
    
    if ($events) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Date</th><th>Time</th><th>Active</th><th>Future?</th></tr>";
        foreach ($events as $event) {
            $eventDateTime = $event['event_date'] . ' ' . $event['event_time'];
            $isFuture = strtotime($eventDateTime) > time() ? 'Yes' : 'No';
            echo "<tr>";
            echo "<td>{$event['id']}</td>";
            echo "<td>{$event['title']}</td>";
            echo "<td>{$event['event_date']}</td>";
            echo "<td>{$event['event_time']}</td>";
            echo "<td>{$event['is_active']}</td>";
            echo "<td>$isFuture</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No events found<br>";
    }
} catch (Exception $e) {
    echo "❌ Events error: " . $e->getMessage() . "<br>";
}

// Test 4: Check user authentication classes
echo "<h2>Test 4: Authentication Classes</h2>";
try {
    require_once __DIR__ . '/classes/SecurityManager.php';
    require_once __DIR__ . '/classes/UserAuthManager.php';
    
    $security = new SecurityManager($pdo);
    $userAuth = new UserAuthManager($pdo, $security);
    
    echo "✅ SecurityManager loaded<br>";
    echo "✅ UserAuthManager loaded<br>";
} catch (Exception $e) {
    echo "❌ Auth classes error: " . $e->getMessage() . "<br>";
}

// Test 5: Check if user pages exist
echo "<h2>Test 5: User Pages</h2>";
$userPages = [
    'user/login.php',
    'user/dashboard.php', 
    'user/events.php',
    'user/event_detail.php',
    'user/rsvp_handler.php'
];

foreach ($userPages as $page) {
    $fullPath = __DIR__ . '/' . $page;
    if (file_exists($fullPath)) {
        echo "✅ $page exists<br>";
    } else {
        echo "❌ $page missing<br>";
    }
}

echo "<h2>Test 6: Current Time</h2>";
echo "Current server time: " . date('Y-m-d H:i:s') . " (timestamp: " . time() . ")<br>";

echo "<h2>Navigation Links</h2>";
echo "<a href='user/login.php'>Login Page</a><br>";
echo "<a href='user/dashboard.php'>Dashboard (requires login)</a><br>";
echo "<a href='user/events.php'>Events (requires login)</a><br>";
echo "<a href='test_rsvp_fix.php'>Test RSVP Fix</a><br>";
?>
