<?php
session_start();
require_once "../config.php";
require_once 'includes/language.php';

// Check if admin is logged in
if (!isset($_SESSION["admin_id"])) {
    header("Location: login.php");
    exit();
}

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST["action"])) {
    try {
        if ($_POST["action"] === "update_setting") {
            $stmt = $pdo->prepare("
                UPDATE appearance_settings
                SET setting_value = ?, updated_at = NOW()
                WHERE setting_name = ?
            ");
            $stmt->execute([$_POST["setting_value"], $_POST["setting_name"]]);
            $success = __('setting_updated_successfully');

            // Regenerate CSS if appearance settings changed
            generateCustomCSS();
        } elseif ($_POST["action"] === "save_all_settings") {
            // Handle bulk save from the interface
            foreach ($_POST as $key => $value) {
                if (strpos($key, 'setting_') === 0) {
                    $settingName = str_replace('setting_', '', $key);
                    $stmt = $pdo->prepare("
                        UPDATE appearance_settings
                        SET setting_value = ?, updated_at = NOW()
                        WHERE setting_name = ?
                    ");
                    $stmt->execute([$value, $settingName]);
                }
            }
            $success = __('all_appearance_settings_saved_successfully');
            generateCustomCSS();
        }
    } catch (Exception $e) {
        $error = __('error') . ": " . $e->getMessage();
    }
}

// Function to generate custom CSS
function generateCustomCSS() {
    global $pdo;

    try {
        // Get ALL appearance settings, not just colors
        $stmt = $pdo->prepare("SELECT setting_name, setting_value FROM appearance_settings");
        $stmt->execute();
        $allSettings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        $css = ":root {\n";

        // Add all settings as CSS variables
        foreach ($allSettings as $name => $value) {
            $cssVar = '--' . str_replace('_', '-', $name);
            // Add 'px' suffix for width and spacing settings
            if (strpos($name, 'width') !== false || strpos($name, 'spacing') !== false) {
                $css .= "  $cssVar: {$value}px;\n";
            } else {
                $css .= "  $cssVar: $value;\n";
            }
        }

        // Add sidebar-specific CSS variables with fallbacks
        $sidebarBg = $allSettings['sidebar_background'] ?? $allSettings['primary_color'] ?? '#e67f0a';
        $sidebarText = $allSettings['sidebar_text_color'] ?? '#ffffff';
        $sidebarHover = $allSettings['sidebar_hover_color'] ?? '#102841';
        $sidebarWidth = $allSettings['sidebar_width'] ?? '280';
        $contentSpacing = $allSettings['content_spacing'] ?? '30';

        // Get primary and secondary colors for navbar theming
        $primaryColor = $allSettings['primary_color'] ?? '#007bff';
        $secondaryColor = $allSettings['secondary_color'] ?? '#6c757d';

        $css .= "\n  /* Sidebar-specific variables */\n";
        $css .= "  --sidebar-bg-color: $sidebarBg;\n";
        $css .= "  --sidebar-text-color: $sidebarText;\n";
        $css .= "  --sidebar-hover-color: $sidebarHover;\n";
        $css .= "  --sidebar-width: {$sidebarWidth}px;\n";
        $css .= "  --content-spacing: {$contentSpacing}px;\n";

        $css .= "\n  /* Bootstrap color overrides */\n";
        $css .= "  --bs-primary: $primaryColor;\n";
        $css .= "  --bs-secondary: $secondaryColor;\n";
        $css .= "}\n";

        // Add sidebar CSS rules
        $css .= "\n/* Sidebar theming */\n";
        $css .= ".sidebar.themed-sidebar {\n";
        $css .= "  background-color: var(--sidebar-bg-color) !important;\n";
        $css .= "  color: var(--sidebar-text-color) !important;\n";
        $css .= "}\n\n";

        $css .= ".sidebar.themed-sidebar .nav-link {\n";
        $css .= "  color: var(--sidebar-text-color) !important;\n";
        $css .= "}\n\n";

        $css .= ".sidebar.themed-sidebar .nav-link:hover {\n";
        $css .= "  background-color: var(--sidebar-hover-color) !important;\n";
        $css .= "  color: var(--sidebar-text-color) !important;\n";
        $css .= "}\n\n";

        $css .= ".sidebar.themed-sidebar .sidebar-heading {\n";
        $css .= "  color: var(--sidebar-text-color) !important;\n";
        $css .= "}\n\n";

        $css .= ".sidebar.themed-sidebar .sidebar-toggle-btn {\n";
        $css .= "  color: var(--sidebar-text-color) !important;\n";
        $css .= "}\n\n";

        // Add navbar theming CSS for user pages
        $css .= "/* Navbar theming for user pages */\n";
        $css .= ".navbar {\n";
        $css .= "  background: linear-gradient(135deg, var(--bs-primary, $primaryColor) 0%, var(--bs-secondary, $secondaryColor) 100%) !important;\n";
        $css .= "}\n\n";

        $css .= ".navbar-brand {\n";
        $css .= "  color: white !important;\n";
        $css .= "}\n\n";

        $css .= ".navbar-nav .nav-link {\n";
        $css .= "  color: rgba(255,255,255,0.9) !important;\n";
        $css .= "}\n\n";

        $css .= ".navbar-nav .nav-link:hover {\n";
        $css .= "  color: white !important;\n";
        $css .= "}\n\n";

        $css .= ".navbar-nav .nav-link.active {\n";
        $css .= "  color: white !important;\n";
        $css .= "  font-weight: 600;\n";
        $css .= "}\n\n";

        // Save CSS to file
        $cssFile = __DIR__ . '/css/custom-theme.css';
        file_put_contents($cssFile, $css);

    } catch (Exception $e) {
        error_log("Error generating CSS: " . $e->getMessage());
    }
}

// Get all appearance settings
try {
    $stmt = $pdo->prepare("SELECT * FROM appearance_settings ORDER BY category, setting_name");
    $stmt->execute();
    $allSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Organize settings by name for easy access
    $settings = [];
    foreach ($allSettings as $setting) {
        $settings[$setting['setting_name']] = $setting['setting_value'];
    }
} catch (Exception $e) {
    $error = "Error loading settings: " . $e->getMessage();
    $settings = [];
}

// Set page variables for header.php
$page_title = __('appearance_settings');
$page_header = __('appearance_settings');
$page_description = __('customize_appearance_description');

include "includes/header.php";
?>

            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <div>
                    <h1 class="h2"><i class="bi bi-palette"></i> <?php _e('appearance_settings'); ?></h1>
                    <p class="text-muted"><?php _e('customize_appearance_description'); ?></p>
                </div>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-outline-secondary" id="previewChanges">
                            <i class="bi bi-eye"></i> Preview Changes
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="resetToDefaults">
                            <i class="bi bi-arrow-clockwise"></i> Reset to Defaults
                        </button>
                    </div>
                </div>
            </div>

            <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="post" id="appearanceForm">
                <input type="hidden" name="action" value="save_all_settings">

                <!-- Advanced Theme Customizer -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-palette me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('advanced_theme_customizer'); ?></h5>
                    </div>
                    <div class="card-body">
                        <!-- Theme Presets -->
                        <div class="mb-4">
                            <h6 class="mb-3">Theme Presets</h6>
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <div class="theme-preset" data-theme="default-blue">
                                        <div class="theme-colors mb-2">
                                            <span class="color-dot" style="background: #007bff;"></span>
                                            <span class="color-dot" style="background: #6c757d;"></span>
                                            <span class="color-dot" style="background: #28a745;"></span>
                                        </div>
                                        <h6>Default Blue</h6>
                                        <small class="text-muted">Classic and professional</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="theme-preset" data-theme="modern-purple">
                                        <div class="theme-colors mb-2">
                                            <span class="color-dot" style="background: #6f42c1;"></span>
                                            <span class="color-dot" style="background: #e83e8c;"></span>
                                            <span class="color-dot" style="background: #fd7e14;"></span>
                                        </div>
                                        <h6>Modern Purple</h6>
                                        <small class="text-muted">Modern and vibrant</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="theme-preset" data-theme="minimal-gray">
                                        <div class="theme-colors mb-2">
                                            <span class="color-dot" style="background: #495057;"></span>
                                            <span class="color-dot" style="background: #6c757d;"></span>
                                            <span class="color-dot" style="background: #adb5bd;"></span>
                                        </div>
                                        <h6>Minimal Gray</h6>
                                        <small class="text-muted">Clean and minimal</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="theme-preset" data-theme="vibrant-orange">
                                        <div class="theme-colors mb-2">
                                            <span class="color-dot" style="background: #fd7e14;"></span>
                                            <span class="color-dot" style="background: #ffc107;"></span>
                                            <span class="color-dot" style="background: #dc3545;"></span>
                                        </div>
                                        <h6>Vibrant Orange</h6>
                                        <small class="text-muted">Energetic and warm</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="theme-preset" data-theme="corporate-blue">
                                        <div class="theme-colors mb-2">
                                            <span class="color-dot" style="background: #0d6efd;"></span>
                                            <span class="color-dot" style="background: #198754;"></span>
                                            <span class="color-dot" style="background: #20c997;"></span>
                                        </div>
                                        <h6>Corporate Blue</h6>
                                        <small class="text-muted">Professional and trustworthy</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Live Preview -->
                        <div class="mb-4">
                            <h6 class="mb-3">Live Preview</h6>
                            <div class="live-preview-container p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="resetPreview">
                                        <i class="bi bi-arrow-clockwise"></i> Reset
                                    </button>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-secondary" id="exportPreview">
                                            <i class="bi bi-download"></i> Export
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="importPreview">
                                            <i class="bi bi-upload"></i> Import
                                        </button>
                                    </div>
                                </div>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-eye display-4"></i>
                                    <p class="mt-2">Live preview will appear here when you make changes</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Color Scheme Section -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-palette me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('color_scheme'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <!-- Primary Color -->
                            <div class="col-md-3">
                                <label class="form-label"><strong>Primary Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_primary_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#fd7e14'); ?>" id="primaryColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#fd7e14'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Secondary Color -->
                            <div class="col-md-3">
                                <label class="form-label"><strong>Secondary Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_secondary_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#6c757d'); ?>" id="secondaryColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#6c757d'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Success Color -->
                            <div class="col-md-3">
                                <label class="form-label"><strong>Success Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_success_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['success_color'] ?? '#28a745'); ?>" id="successColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['success_color'] ?? '#28a745'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Danger Color -->
                            <div class="col-md-3">
                                <label class="form-label"><strong>Danger Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_danger_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['danger_color'] ?? '#dc3545'); ?>" id="dangerColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['danger_color'] ?? '#dc3545'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Background Color -->
                            <div class="col-md-4">
                                <label class="form-label"><strong>Background Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_background_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['background_color'] ?? '#ffffff'); ?>" id="backgroundColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['background_color'] ?? '#ffffff'); ?>" readonly>
                                </div>
                                <small class="text-muted">Use contrast with background color</small>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Text Color -->
                            <div class="col-md-4">
                                <label class="form-label"><strong>Text Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_text_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['text_color'] ?? '#8b4513'); ?>" id="textColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['text_color'] ?? '#8b4513'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Link Color -->
                            <div class="col-md-4">
                                <label class="form-label"><strong>Link Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_link_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['link_color'] ?? '#fd7e14'); ?>" id="linkColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['link_color'] ?? '#fd7e14'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Color Presets -->
                        <div class="mt-4">
                            <h6 class="mb-3">Color Presets</h6>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="default">Default</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="blue">Blue</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="green">Green</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="purple">Purple</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="orange">Orange</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Typography Section -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-fonts me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('typography'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label"><strong>Primary Font</strong></label>
                                <select name="setting_font_family" class="form-select" id="primaryFont">
                                    <option value="Inter" <?php echo ($settings['font_family'] ?? '') === 'Inter' ? 'selected' : ''; ?>>Inter</option>
                                    <option value="Arial, sans-serif" <?php echo ($settings['font_family'] ?? '') === 'Arial, sans-serif' ? 'selected' : ''; ?>>Arial</option>
                                    <option value="Helvetica, sans-serif" <?php echo ($settings['font_family'] ?? '') === 'Helvetica, sans-serif' ? 'selected' : ''; ?>>Helvetica</option>
                                    <option value="Georgia, serif" <?php echo ($settings['font_family'] ?? '') === 'Georgia, serif' ? 'selected' : ''; ?>>Georgia</option>
                                    <option value="Times New Roman, serif" <?php echo ($settings['font_family'] ?? '') === 'Times New Roman, serif' ? 'selected' : ''; ?>>Times New Roman</option>
                                    <option value="Roboto, sans-serif" <?php echo ($settings['font_family'] ?? '') === 'Roboto, sans-serif' ? 'selected' : ''; ?>>Roboto</option>
                                    <option value="Open Sans, sans-serif" <?php echo ($settings['font_family'] ?? '') === 'Open Sans, sans-serif' ? 'selected' : ''; ?>>Open Sans</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Base Font Size (px)</strong></label>
                                <input type="number" name="setting_base_font_size" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['base_font_size'] ?? '16'); ?>" min="12" max="24">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Normal Weight</strong></label>
                                <select name="setting_font_weight_normal" class="form-select">
                                    <option value="300" <?php echo ($settings['font_weight_normal'] ?? '') === '300' ? 'selected' : ''; ?>>Light (300)</option>
                                    <option value="400" <?php echo ($settings['font_weight_normal'] ?? '') === '400' ? 'selected' : ''; ?>>Normal (400)</option>
                                    <option value="500" <?php echo ($settings['font_weight_normal'] ?? '') === '500' ? 'selected' : ''; ?>>Medium (500)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Bold Weight</strong></label>
                                <select name="setting_font_weight_bold" class="form-select">
                                    <option value="600" <?php echo ($settings['font_weight_bold'] ?? '') === '600' ? 'selected' : ''; ?>>Semi Bold (600)</option>
                                    <option value="700" <?php echo ($settings['font_weight_bold'] ?? '') === '700' ? 'selected' : ''; ?>>Bold (700)</option>
                                    <option value="800" <?php echo ($settings['font_weight_bold'] ?? '') === '800' ? 'selected' : ''; ?>>Extra Bold (800)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Line Height</strong></label>
                                <input type="number" name="setting_line_height" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['line_height'] ?? '1.5'); ?>" min="1" max="3" step="0.1">
                            </div>
                        </div>

                        <!-- Typography Preview -->
                        <div class="mt-4">
                            <h6 class="mb-3">Typography Preview</h6>
                            <div class="typography-preview p-3 border rounded">
                                <h1 class="preview-heading">Heading 1</h1>
                                <h2 class="preview-heading">Heading 2</h2>
                                <h3 class="preview-heading">Heading 3</h3>
                                <p class="preview-text">This is a paragraph of text to demonstrate the typography settings. It shows how the font family, size, weight, and line height work together.</p>
                                <p class="preview-text"><strong>Bold text</strong> and <a href="#" class="preview-link">link text</a> examples.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Layout Settings -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-layout-sidebar me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('layout_settings'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label"><strong>Sidebar Style</strong></label>
                                <select name="setting_sidebar_style" class="form-select">
                                    <option value="default" <?php echo ($settings['sidebar_style'] ?? '') === 'default' ? 'selected' : ''; ?>>Default</option>
                                    <option value="compact" <?php echo ($settings['sidebar_style'] ?? '') === 'compact' ? 'selected' : ''; ?>>Compact</option>
                                    <option value="minimal" <?php echo ($settings['sidebar_style'] ?? '') === 'minimal' ? 'selected' : ''; ?>>Minimal</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Navigation Style</strong></label>
                                <select name="setting_navigation_style" class="form-select">
                                    <option value="default" <?php echo ($settings['navigation_style'] ?? '') === 'default' ? 'selected' : ''; ?>>Default</option>
                                    <option value="pills" <?php echo ($settings['navigation_style'] ?? '') === 'pills' ? 'selected' : ''; ?>>Pills</option>
                                    <option value="underline" <?php echo ($settings['navigation_style'] ?? '') === 'underline' ? 'selected' : ''; ?>>Underline</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Card Style</strong></label>
                                <select name="setting_card_style" class="form-select">
                                    <option value="default" <?php echo ($settings['card_style'] ?? '') === 'default' ? 'selected' : ''; ?>>Default</option>
                                    <option value="shadow" <?php echo ($settings['card_style'] ?? '') === 'shadow' ? 'selected' : ''; ?>>Shadow</option>
                                    <option value="border" <?php echo ($settings['card_style'] ?? '') === 'border' ? 'selected' : ''; ?>>Border</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Border Radius (rem)</strong></label>
                                <input type="number" name="setting_border_radius" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['border_radius'] ?? '0.375'); ?>" min="0" max="2" step="0.125">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Container Max Width (px)</strong></label>
                                <input type="number" name="setting_container_max_width" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['container_max_width'] ?? '1200'); ?>" min="800" max="1600" step="100">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Sidebar Width (px)</strong></label>
                                <input type="number" name="setting_sidebar_width" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['sidebar_width'] ?? '300'); ?>" min="200" max="400" step="10">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label"><strong>Content Spacing (px)</strong></label>
                                <input type="number" name="setting_content_spacing" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['content_spacing'] ?? '30'); ?>" min="10" max="60" step="5">
                                <small class="text-muted">Space between sidebar and main content</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Colors -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-palette me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('sidebar_colors'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label"><strong>Sidebar Background</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_sidebar_background" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['sidebar_background'] ?? '#e67f0a'); ?>" id="sidebarBackground">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['sidebar_background'] ?? '#e67f0a'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Sidebar Text Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_sidebar_text_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['sidebar_text_color'] ?? '#ffffff'); ?>" id="sidebarTextColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['sidebar_text_color'] ?? '#ffffff'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Sidebar Hover Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_sidebar_hover_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['sidebar_hover_color'] ?? '#102841'); ?>" id="sidebarHoverColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['sidebar_hover_color'] ?? '#102841'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Logo and Branding -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-image me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('logo_and_branding'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label"><strong>Organization Name</strong></label>
                                <input type="text" name="setting_organization_name" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['organization_name'] ?? 'Church Management System'); ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Tagline</strong></label>
                                <input type="text" name="setting_tagline" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['tagline'] ?? 'Connecting Faith, Building Community'); ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Theme Settings -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-gear me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('theme_settings'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label"><strong>Default Theme Mode</strong></label>
                                <select name="setting_default_theme_mode" class="form-select">
                                    <option value="auto" <?php echo ($settings['default_theme_mode'] ?? '') === 'auto' ? 'selected' : ''; ?>>Auto (System)</option>
                                    <option value="light" <?php echo ($settings['default_theme_mode'] ?? '') === 'light' ? 'selected' : ''; ?>>Light</option>
                                    <option value="dark" <?php echo ($settings['default_theme_mode'] ?? '') === 'dark' ? 'selected' : ''; ?>>Dark</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Admin Theme</strong></label>
                                <select name="setting_admin_theme" class="form-select">
                                    <option value="modern" <?php echo ($settings['admin_theme'] ?? '') === 'modern' ? 'selected' : ''; ?>>Modern</option>
                                    <option value="classic" <?php echo ($settings['admin_theme'] ?? '') === 'classic' ? 'selected' : ''; ?>>Classic</option>
                                    <option value="minimal" <?php echo ($settings['admin_theme'] ?? '') === 'minimal' ? 'selected' : ''; ?>>Minimal</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>User Theme</strong></label>
                                <select name="setting_user_theme" class="form-select">
                                    <option value="minimal" <?php echo ($settings['user_theme'] ?? '') === 'minimal' ? 'selected' : ''; ?>>Minimal</option>
                                    <option value="modern" <?php echo ($settings['user_theme'] ?? '') === 'modern' ? 'selected' : ''; ?>>Modern</option>
                                    <option value="classic" <?php echo ($settings['user_theme'] ?? '') === 'classic' ? 'selected' : ''; ?>>Classic</option>
                                </select>
                            </div>
                        </div>
                        <div class="row g-4 mt-2">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="setting_enable_dark_mode" id="enableDarkMode"
                                           <?php echo ($settings['enable_dark_mode'] ?? '') === '1' ? 'checked' : ''; ?> value="1">
                                    <label class="form-check-label" for="enableDarkMode">
                                        <strong>Enable Dark Mode Support</strong>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="setting_show_theme_switcher" id="showThemeSwitcher"
                                           <?php echo ($settings['show_theme_switcher'] ?? '') === '1' ? 'checked' : ''; ?> value="1">
                                    <label class="form-check-label" for="showThemeSwitcher">
                                        <strong>Show Theme Switcher to Users</strong>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom CSS -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-code-slash me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('custom_css'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label"><strong>Additional CSS</strong></label>
                            <textarea name="setting_custom_css" class="form-control" rows="8"
                                      placeholder="/* Add your custom CSS here */"><?php echo htmlspecialchars($settings['custom_css'] ?? ''); ?></textarea>
                            <small class="text-muted">Add custom CSS to override default styles. Use CSS custom properties (variables) for better theme integration.</small>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="d-flex justify-content-end mb-4">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="bi bi-check-lg"></i> Save Appearance Settings
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-lg ms-2" id="cancelChanges">
                        <i class="bi bi-x-lg"></i> Cancel
                    </button>
                </div>
            </form>

<style>
.theme-preset {
    cursor: pointer;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
}

.theme-preset:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.theme-preset.active {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.theme-colors {
    display: flex;
    justify-content: center;
    gap: 5px;
}

.color-dot {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-block;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.color-input-group {
    position: relative;
}

.color-palette {
    margin-top: 8px;
}

.color-swatches {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.color-swatch {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    transition: transform 0.2s ease;
}

.color-swatch:hover {
    transform: scale(1.1);
}

.live-preview-container {
    background: #f8f9fa;
    min-height: 200px;
}

.typography-preview {
    background: #fff;
}

.preview-heading {
    color: var(--bs-primary, #007bff);
    margin-bottom: 0.5rem;
}

.preview-text {
    color: var(--bs-body-color, #212529);
    margin-bottom: 0.5rem;
}

.preview-link {
    color: var(--bs-link-color, #007bff);
    text-decoration: none;
}

.preview-link:hover {
    text-decoration: underline;
}

.color-preset.active {
    background-color: #007bff;
    color: white;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Color swatch functionality
    document.querySelectorAll('.color-swatch').forEach(swatch => {
        swatch.addEventListener('click', function() {
            const color = this.dataset.color;
            const colorInput = this.closest('.col-md-3, .col-md-4').querySelector('input[type="color"]');
            const textInput = this.closest('.col-md-3, .col-md-4').querySelector('input[type="text"]');

            if (colorInput && textInput) {
                colorInput.value = color;
                textInput.value = color;

                // Trigger change event for live preview
                colorInput.dispatchEvent(new Event('change'));
            }
        });
    });

    // Color input sync
    document.querySelectorAll('input[type="color"]').forEach(colorInput => {
        colorInput.addEventListener('change', function() {
            const textInput = this.parentNode.querySelector('input[type="text"]');
            if (textInput) {
                textInput.value = this.value;
            }

            // Update live preview
            updateLivePreview();
        });
    });

    // Theme preset functionality
    document.querySelectorAll('.theme-preset').forEach(preset => {
        preset.addEventListener('click', function() {
            // Remove active class from all presets
            document.querySelectorAll('.theme-preset').forEach(p => p.classList.remove('active'));

            // Add active class to clicked preset
            this.classList.add('active');

            // Apply theme colors based on preset
            const theme = this.dataset.theme;
            applyThemePreset(theme);
        });
    });

    // Color preset functionality
    document.querySelectorAll('.color-preset').forEach(preset => {
        preset.addEventListener('click', function() {
            // Remove active class from all presets
            document.querySelectorAll('.color-preset').forEach(p => p.classList.remove('active'));

            // Add active class to clicked preset
            this.classList.add('active');

            // Apply color preset
            const presetName = this.dataset.preset;
            applyColorPreset(presetName);
        });
    });

    // Typography preview update
    document.querySelectorAll('select[name^="setting_font"], input[name^="setting_font"], input[name="setting_line_height"]').forEach(input => {
        input.addEventListener('change', updateTypographyPreview);
    });

    // Preview changes button
    document.getElementById('previewChanges')?.addEventListener('click', function() {
        updateLivePreview();
        alert('Preview updated! Changes are visible in the preview area.');
    });

    // Reset to defaults button
    document.getElementById('resetToDefaults')?.addEventListener('click', function() {
        if (confirm('Are you sure you want to reset all appearance settings to defaults? This action cannot be undone.')) {
            resetToDefaults();
        }
    });

    // Cancel changes button
    document.getElementById('cancelChanges')?.addEventListener('click', function() {
        if (confirm('Are you sure you want to cancel all changes? Unsaved changes will be lost.')) {
            location.reload();
        }
    });

    function applyThemePreset(theme) {
        const presets = {
            'default-blue': {
                primary: '#007bff',
                secondary: '#6c757d',
                success: '#28a745',
                danger: '#dc3545'
            },
            'modern-purple': {
                primary: '#6f42c1',
                secondary: '#e83e8c',
                success: '#20c997',
                danger: '#fd7e14'
            },
            'minimal-gray': {
                primary: '#495057',
                secondary: '#6c757d',
                success: '#28a745',
                danger: '#dc3545'
            },
            'vibrant-orange': {
                primary: '#fd7e14',
                secondary: '#ffc107',
                success: '#28a745',
                danger: '#dc3545'
            },
            'corporate-blue': {
                primary: '#0d6efd',
                secondary: '#198754',
                success: '#20c997',
                danger: '#dc3545'
            }
        };

        if (presets[theme]) {
            const colors = presets[theme];

            // Update color inputs
            updateColorInput('setting_primary_color', colors.primary);
            updateColorInput('setting_secondary_color', colors.secondary);
            updateColorInput('setting_success_color', colors.success);
            updateColorInput('setting_danger_color', colors.danger);

            updateLivePreview();
        }
    }

    function applyColorPreset(preset) {
        const presets = {
            'default': { primary: '#007bff', secondary: '#6c757d', success: '#28a745', danger: '#dc3545' },
            'blue': { primary: '#0d6efd', secondary: '#0dcaf0', success: '#198754', danger: '#dc3545' },
            'green': { primary: '#198754', secondary: '#20c997', success: '#28a745', danger: '#dc3545' },
            'purple': { primary: '#6f42c1', secondary: '#d63384', success: '#20c997', danger: '#dc3545' },
            'orange': { primary: '#fd7e14', secondary: '#ffc107', success: '#198754', danger: '#dc3545' }
        };

        if (presets[preset]) {
            const colors = presets[preset];
            Object.keys(colors).forEach(key => {
                updateColorInput(`setting_${key}_color`, colors[key]);
            });
            updateLivePreview();
        }
    }

    function updateColorInput(name, color) {
        const colorInput = document.querySelector(`input[name="${name}"]`);
        const textInput = colorInput?.parentNode.querySelector('input[type="text"]');

        if (colorInput) {
            colorInput.value = color;
        }
        if (textInput) {
            textInput.value = color;
        }
    }

    function updateTypographyPreview() {
        const fontFamily = document.querySelector('select[name="setting_font_family"]')?.value || 'Inter';
        const fontSize = document.querySelector('input[name="setting_base_font_size"]')?.value || '16';
        const lineHeight = document.querySelector('input[name="setting_line_height"]')?.value || '1.5';

        const preview = document.querySelector('.typography-preview');
        if (preview) {
            preview.style.fontFamily = fontFamily;
            preview.style.fontSize = fontSize + 'px';
            preview.style.lineHeight = lineHeight;
        }
    }

    function updateLivePreview() {
        // This would update a live preview area
        // For now, we'll just update the typography preview
        updateTypographyPreview();
    }

    function resetToDefaults() {
        // Reset all form inputs to default values
        const form = document.getElementById('appearanceForm');
        if (form) {
            form.reset();

            // Reset color inputs to defaults
            updateColorInput('setting_primary_color', '#fd7e14');
            updateColorInput('setting_secondary_color', '#6c757d');
            updateColorInput('setting_success_color', '#28a745');
            updateColorInput('setting_danger_color', '#dc3545');

            updateLivePreview();
        }
    }

    // Initialize typography preview
    updateTypographyPreview();
});
</script>

<?php include "includes/footer.php"; ?>
