<?php
session_start();

require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo->beginTransaction();

        // Color scheme settings
        $colorSettings = [
            'primary_color' => $_POST['primary_color'] ?? '#007bff',
            'secondary_color' => $_POST['secondary_color'] ?? '#6c757d',
            'success_color' => $_POST['success_color'] ?? '#28a745',
            'danger_color' => $_POST['danger_color'] ?? '#dc3545',
            'warning_color' => $_POST['warning_color'] ?? '#ffc107',
            'info_color' => $_POST['info_color'] ?? '#17a2b8',
            'light_color' => $_POST['light_color'] ?? '#f8f9fa',
            'dark_color' => $_POST['dark_color'] ?? '#343a40',
            'background_color' => $_POST['background_color'] ?? '#ffffff',
            'text_color' => $_POST['text_color'] ?? '#212529',
            'link_color' => $_POST['link_color'] ?? '#007bff',
            'link_hover_color' => $_POST['link_hover_color'] ?? '#0056b3'
        ];

        // Typography settings
        $typographySettings = [
            'primary_font' => $_POST['primary_font'] ?? 'Inter',
            'secondary_font' => $_POST['secondary_font'] ?? 'Inter',
            'font_size_base' => $_POST['font_size_base'] ?? '16',
            'font_weight_normal' => $_POST['font_weight_normal'] ?? '400',
            'font_weight_bold' => $_POST['font_weight_bold'] ?? '600',
            'line_height_base' => $_POST['line_height_base'] ?? '1.5',
            'heading_font_weight' => $_POST['heading_font_weight'] ?? '600'
        ];

        // Layout settings
        $layoutSettings = [
            'sidebar_style' => $_POST['sidebar_style'] ?? 'default',
            'navbar_style' => $_POST['navbar_style'] ?? 'default',
            'card_style' => $_POST['card_style'] ?? 'default',
            'border_radius' => $_POST['border_radius'] ?? '0.375',
            'box_shadow' => $_POST['box_shadow'] ?? 'default',
            'container_width' => $_POST['container_width'] ?? 'fluid',
            'sidebar_width' => $_POST['sidebar_width'] ?? '250',
            'header_height' => $_POST['header_height'] ?? '60',
            'content_padding' => $_POST['content_padding'] ?? '1.5',
            'card_spacing' => $_POST['card_spacing'] ?? '1.5',
            'container_max_width' => $_POST['container_max_width'] ?? '1200',
            'sidebar_width' => $_POST['sidebar_width'] ?? '280',
            'content_spacing' => $_POST['content_spacing'] ?? '30',
            'sidebar_bg_color' => $_POST['sidebar_bg_color'] ?? '#343a40',
            'sidebar_text_color' => $_POST['sidebar_text_color'] ?? '#ffffff',
            'sidebar_hover_color' => $_POST['sidebar_hover_color'] ?? '#007bff'
        ];

        // Theme settings
        $themeSettings = [
            'theme_mode' => $_POST['theme_mode'] ?? 'light',
            'admin_theme' => $_POST['admin_theme'] ?? 'default',
            'user_theme' => $_POST['user_theme'] ?? 'default',
            'custom_css' => $_POST['custom_css'] ?? '',
            'enable_dark_mode' => isset($_POST['enable_dark_mode']) ? '1' : '0',
            'enable_theme_switcher' => isset($_POST['enable_theme_switcher']) ? '1' : '0'
        ];

        // Save all settings
        $allSettings = array_merge($colorSettings, $typographySettings, $layoutSettings, $themeSettings);

        foreach ($allSettings as $key => $value) {
            update_site_setting($key, $value);
        }

        $pdo->commit();
        $success_message = "Appearance settings updated successfully!";

        // Generate CSS file
        generateCustomCSS();

    } catch (Exception $e) {
        $pdo->rollback();
        $error_message = "Error updating appearance settings: " . $e->getMessage();
    }
}

// Get current settings
$currentSettings = [];
$settingKeys = [
    'primary_color', 'secondary_color', 'success_color', 'danger_color', 'warning_color',
    'info_color', 'light_color', 'dark_color', 'background_color', 'text_color',
    'link_color', 'link_hover_color', 'primary_font', 'secondary_font', 'font_size_base',
    'font_weight_normal', 'font_weight_bold', 'line_height_base', 'heading_font_weight',
    'sidebar_style', 'navbar_style', 'card_style', 'border_radius', 'box_shadow',
    'container_max_width', 'sidebar_width', 'theme_mode', 'admin_theme', 'user_theme',
    'custom_css', 'enable_dark_mode', 'enable_theme_switcher',
    // New sidebar and spacing settings
    'sidebar_bg_color', 'sidebar_text_color', 'sidebar_hover_color', 'content_spacing'
];

// Default values for settings
$defaults = [
    'sidebar_bg_color' => '#343a40',
    'sidebar_text_color' => '#ffffff',
    'sidebar_hover_color' => '#007bff',
    'content_spacing' => '30',
    'primary_color' => '#007bff',
    'secondary_color' => '#6c757d',
    'success_color' => '#28a745',
    'danger_color' => '#dc3545',
    'warning_color' => '#ffc107',
    'info_color' => '#17a2b8',
    'light_color' => '#f8f9fa',
    'dark_color' => '#343a40',
    'background_color' => '#ffffff',
    'text_color' => '#212529',
    'link_color' => '#007bff',
    'link_hover_color' => '#0056b3',
    'primary_font' => 'Inter',
    'font_size_base' => '16',
    'line_height_base' => '1.5',
    'border_radius' => '0.375',
    'container_max_width' => '1200',
    'sidebar_width' => '280'
];

foreach ($settingKeys as $key) {
    $default = isset($defaults[$key]) ? $defaults[$key] : '';
    $currentSettings[$key] = get_site_setting($key, $default);
}

// Function to generate custom CSS
function generateCustomCSS() {
    $css = ":root {\n";

    // Color variables
    $colorVars = [
        'primary_color' => '--bs-primary',
        'secondary_color' => '--bs-secondary',
        'success_color' => '--bs-success',
        'danger_color' => '--bs-danger',
        'warning_color' => '--bs-warning',
        'info_color' => '--bs-info',
        'light_color' => '--bs-light',
        'dark_color' => '--bs-dark',
        'background_color' => '--bs-body-bg',
        'text_color' => '--bs-body-color',
        'link_color' => '--bs-link-color',
        'link_hover_color' => '--bs-link-hover-color'
    ];

    foreach ($colorVars as $setting => $cssVar) {
        $value = get_site_setting($setting, '');
        if ($value) {
            $css .= "  $cssVar: $value;\n";
        }
    }

    // Add sidebar-specific color variables
    $sidebarBg = get_site_setting('sidebar_bg_color', '#343a40');
    $sidebarText = get_site_setting('sidebar_text_color', '#ffffff');
    $sidebarHover = get_site_setting('sidebar_hover_color', '#007bff');

    $css .= "  --sidebar-bg-color: $sidebarBg;\n";
    $css .= "  --sidebar-text-color: $sidebarText;\n";
    $css .= "  --sidebar-hover-color: $sidebarHover;\n";

    // Typography variables
    $fontFamily = get_site_setting('primary_font', 'Inter');
    $fontSize = get_site_setting('font_size_base', '16');
    $lineHeight = get_site_setting('line_height_base', '1.5');

    $css .= "  --bs-font-sans-serif: '$fontFamily', system-ui, -apple-system, sans-serif;\n";
    $css .= "  --bs-body-font-size: {$fontSize}px;\n";
    $css .= "  --bs-body-line-height: $lineHeight;\n";

    // Layout variables
    $borderRadius = get_site_setting('border_radius', '0.375');
    $sidebarWidth = get_site_setting('sidebar_width', '280');
    $contentSpacing = get_site_setting('content_spacing', '30');

    $css .= "  --bs-border-radius: {$borderRadius}rem;\n";
    $css .= "  --sidebar-width: {$sidebarWidth}px;\n";
    $css .= "  --content-spacing: {$contentSpacing}px;\n";

    $css .= "}\n\n";

    // Add sidebar theme CSS
    $css .= "/* Sidebar Theme Styles */\n";
    $css .= ".sidebar {\n";
    $css .= "  background-color: var(--sidebar-bg-color) !important;\n";
    $css .= "  color: var(--sidebar-text-color) !important;\n";
    $css .= "  width: var(--sidebar-width) !important;\n";
    $css .= "}\n\n";

    $css .= ".sidebar .nav-link, .sidebar a {\n";
    $css .= "  color: var(--sidebar-text-color) !important;\n";
    $css .= "}\n\n";

    $css .= ".sidebar .nav-link:hover, .sidebar a:hover {\n";
    $css .= "  background-color: var(--sidebar-hover-color) !important;\n";
    $css .= "  color: #ffffff !important;\n";
    $css .= "}\n\n";

    $css .= ".main-content {\n";
    $css .= "  margin-left: calc(var(--sidebar-width) + var(--content-spacing)) !important;\n";
    $css .= "  padding: var(--content-spacing) !important;\n";
    $css .= "}\n\n";

    // Custom CSS
    $customCSS = get_site_setting('custom_css', '');
    if ($customCSS) {
        $css .= "/* Custom CSS */\n" . $customCSS . "\n";
    }

    // Save CSS file to correct location
    $cssFile = __DIR__ . '/css/custom-theme.css';
    if (!is_dir(dirname($cssFile))) {
        mkdir(dirname($cssFile), 0755, true);
    }
    file_put_contents($cssFile, $css);

    // Also save to cache for faster loading
    $cacheFile = __DIR__ . '/../cache/theme-cache.css';
    if (!is_dir(dirname($cacheFile))) {
        mkdir(dirname($cacheFile), 0755, true);
    }
    file_put_contents($cacheFile, $css);
}
// Generate CSS on page load and after form submission
$cssFile = __DIR__ . '/css/custom-theme.css';
if (!file_exists($cssFile) || isset($_POST) && !empty($_POST)) {
    generateCustomCSS();
}

// Page title and header info
$page_title = "Appearance Settings";
$page_header = "Appearance Settings";
$page_description = "Customize the appearance and styling of your church website.";

include 'includes/header.php';
?>

<!-- Page Header -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><?php echo $page_header; ?></h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-secondary" id="preview-changes">
            <i class="bi bi-eye"></i> Preview Changes
        </button>
        <button type="button" class="btn btn-outline-secondary ms-2" id="reset-defaults">
            <i class="bi bi-arrow-clockwise"></i> Reset to Defaults
        </button>
    </div>
</div>

            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST" id="appearance-form" class="appearance-settings">

    <!-- Advanced Theme Customizer -->
    <div class="theme-customizer">
        <h3>Advanced Theme Customizer</h3>

        <!-- Preset Selector -->
        <div class="preset-selector">
            <h4>Theme Presets</h4>
            <div class="preset-grid">
                <div class="preset-card" data-preset="default">
                    <h5>Default Blue</h5>
                    <div class="preset-colors">
                        <div class="preset-color" style="background: #007bff;"></div>
                        <div class="preset-color" style="background: #6c757d;"></div>
                        <div class="preset-color" style="background: #28a745;"></div>
                        <div class="preset-color" style="background: #dc3545;"></div>
                    </div>
                    <small>Classic and professional</small>
                </div>
                <div class="preset-card" data-preset="modern">
                    <h5>Modern Purple</h5>
                    <div class="preset-colors">
                        <div class="preset-color" style="background: #6f42c1;"></div>
                        <div class="preset-color" style="background: #20c997;"></div>
                        <div class="preset-color" style="background: #e74c3c;"></div>
                        <div class="preset-color" style="background: #f39c12;"></div>
                    </div>
                    <small>Modern and vibrant</small>
                </div>
                <div class="preset-card" data-preset="minimal">
                    <h5>Minimal Gray</h5>
                    <div class="preset-colors">
                        <div class="preset-color" style="background: #495057;"></div>
                        <div class="preset-color" style="background: #51cf66;"></div>
                        <div class="preset-color" style="background: #ff6b6b;"></div>
                        <div class="preset-color" style="background: #ffd43b;"></div>
                    </div>
                    <small>Clean and minimal</small>
                </div>
                <div class="preset-card" data-preset="vibrant">
                    <h5>Vibrant Orange</h5>
                    <div class="preset-colors">
                        <div class="preset-color" style="background: #fd7e14;"></div>
                        <div class="preset-color" style="background: #20c997;"></div>
                        <div class="preset-color" style="background: #dc3545;"></div>
                        <div class="preset-color" style="background: #ffc107;"></div>
                    </div>
                    <small>Energetic and warm</small>
                </div>
                <div class="preset-card" data-preset="corporate">
                    <h5>Corporate Blue</h5>
                    <div class="preset-colors">
                        <div class="preset-color" style="background: #0d6efd;"></div>
                        <div class="preset-color" style="background: #198754;"></div>
                        <div class="preset-color" style="background: #dc3545;"></div>
                        <div class="preset-color" style="background: #ffc107;"></div>
                    </div>
                    <small>Professional and trustworthy</small>
                </div>
            </div>

            <!-- Theme Controls -->
            <div class="theme-controls">
                <div class="preview-toggle">
                    <input type="checkbox" id="previewToggle" class="form-check-input">
                    <label for="previewToggle" class="form-check-label">Live Preview</label>
                </div>

                <div class="theme-actions">
                    <button type="button" id="resetTheme" class="btn-theme btn-theme-secondary">
                        <i class="bi bi-arrow-clockwise"></i> Reset
                    </button>
                    <div class="import-export">
                        <button type="button" id="exportTheme" class="btn-theme btn-theme-secondary">
                            <i class="bi bi-download"></i> Export
                        </button>
                        <button type="button" id="importTheme" class="btn-theme btn-theme-secondary">
                            <i class="bi bi-upload"></i> Import
                        </button>
                        <input type="file" id="importFile" accept=".json">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Color Scheme Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-palette-fill"></i> Color Scheme
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="primary_color" class="form-label">Primary Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" value="<?php echo htmlspecialchars($currentSettings['primary_color'] ?: '#007bff'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['primary_color'] ?: '#007bff'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="secondary_color" class="form-label">Secondary Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color" value="<?php echo htmlspecialchars($currentSettings['secondary_color'] ?: '#6c757d'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['secondary_color'] ?: '#6c757d'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="success_color" class="form-label">Success Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="success_color" name="success_color" value="<?php echo htmlspecialchars($currentSettings['success_color'] ?: '#28a745'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['success_color'] ?: '#28a745'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="danger_color" class="form-label">Danger Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="danger_color" name="danger_color" value="<?php echo htmlspecialchars($currentSettings['danger_color'] ?: '#dc3545'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['danger_color'] ?: '#dc3545'); ?>" readonly>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="background_color" class="form-label">Background Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="background_color" name="background_color" value="<?php echo htmlspecialchars($currentSettings['background_color'] ?: '#ffffff'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['background_color'] ?: '#ffffff'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="text_color" class="form-label">Text Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="text_color" name="text_color" value="<?php echo htmlspecialchars($currentSettings['text_color'] ?: '#212529'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['text_color'] ?: '#212529'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="link_color" class="form-label">Link Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="link_color" name="link_color" value="<?php echo htmlspecialchars($currentSettings['link_color'] ?: '#007bff'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['link_color'] ?: '#007bff'); ?>" readonly>
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <h6>Color Presets</h6>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="default">Default</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="blue">Blue</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="green">Green</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="purple">Purple</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="orange">Orange</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Typography Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-fonts"></i> Typography
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-6 mb-3">
                    <label for="primary_font" class="form-label">Primary Font</label>
                    <select class="form-select" id="primary_font" name="primary_font">
                        <option value="Inter" <?php echo ($currentSettings['primary_font'] === 'Inter') ? 'selected' : ''; ?>>Inter</option>
                        <option value="Roboto" <?php echo ($currentSettings['primary_font'] === 'Roboto') ? 'selected' : ''; ?>>Roboto</option>
                        <option value="Open Sans" <?php echo ($currentSettings['primary_font'] === 'Open Sans') ? 'selected' : ''; ?>>Open Sans</option>
                        <option value="Lato" <?php echo ($currentSettings['primary_font'] === 'Lato') ? 'selected' : ''; ?>>Lato</option>
                        <option value="Montserrat" <?php echo ($currentSettings['primary_font'] === 'Montserrat') ? 'selected' : ''; ?>>Montserrat</option>
                        <option value="Poppins" <?php echo ($currentSettings['primary_font'] === 'Poppins') ? 'selected' : ''; ?>>Poppins</option>
                        <option value="Source Sans Pro" <?php echo ($currentSettings['primary_font'] === 'Source Sans Pro') ? 'selected' : ''; ?>>Source Sans Pro</option>
                    </select>
                </div>
                <div class="col-lg-6 mb-3">
                    <label for="font_size_base" class="form-label">Base Font Size (px)</label>
                    <input type="number" class="form-control" id="font_size_base" name="font_size_base" value="<?php echo htmlspecialchars($currentSettings['font_size_base'] ?: '16'); ?>" min="12" max="24">
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4 mb-3">
                    <label for="font_weight_normal" class="form-label">Normal Weight</label>
                    <select class="form-select" id="font_weight_normal" name="font_weight_normal">
                        <option value="300" <?php echo ($currentSettings['font_weight_normal'] === '300') ? 'selected' : ''; ?>>Light (300)</option>
                        <option value="400" <?php echo ($currentSettings['font_weight_normal'] === '400') ? 'selected' : ''; ?>>Normal (400)</option>
                        <option value="500" <?php echo ($currentSettings['font_weight_normal'] === '500') ? 'selected' : ''; ?>>Medium (500)</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="font_weight_bold" class="form-label">Bold Weight</label>
                    <select class="form-select" id="font_weight_bold" name="font_weight_bold">
                        <option value="600" <?php echo ($currentSettings['font_weight_bold'] === '600') ? 'selected' : ''; ?>>Semi-Bold (600)</option>
                        <option value="700" <?php echo ($currentSettings['font_weight_bold'] === '700') ? 'selected' : ''; ?>>Bold (700)</option>
                        <option value="800" <?php echo ($currentSettings['font_weight_bold'] === '800') ? 'selected' : ''; ?>>Extra-Bold (800)</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="line_height_base" class="form-label">Line Height</label>
                    <input type="number" class="form-control" id="line_height_base" name="line_height_base" value="<?php echo htmlspecialchars($currentSettings['line_height_base'] ?: '1.5'); ?>" min="1" max="2" step="0.1">
                </div>
            </div>

            <div class="mt-3">
                <h6>Typography Preview</h6>
                <div class="p-3 border rounded" id="typography-preview">
                    <h1>Heading 1</h1>
                    <h2>Heading 2</h2>
                    <h3>Heading 3</h3>
                    <p>This is a paragraph of text to demonstrate the typography settings. It shows how the font family, size, weight, and line height work together.</p>
                    <p><strong>Bold text</strong> and <a href="#">link text</a> examples.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Layout Settings Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-layout-sidebar"></i> Layout Settings
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-4 mb-3">
                    <label for="sidebar_style" class="form-label">Sidebar Style</label>
                    <select class="form-select" id="sidebar_style" name="sidebar_style">
                        <option value="default" <?php echo ($currentSettings['sidebar_style'] === 'default') ? 'selected' : ''; ?>>Default</option>
                        <option value="compact" <?php echo ($currentSettings['sidebar_style'] === 'compact') ? 'selected' : ''; ?>>Compact</option>
                        <option value="minimal" <?php echo ($currentSettings['sidebar_style'] === 'minimal') ? 'selected' : ''; ?>>Minimal</option>
                        <option value="dark" <?php echo ($currentSettings['sidebar_style'] === 'dark') ? 'selected' : ''; ?>>Dark</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="navbar_style" class="form-label">Navigation Style</label>
                    <select class="form-select" id="navbar_style" name="navbar_style">
                        <option value="default" <?php echo ($currentSettings['navbar_style'] === 'default') ? 'selected' : ''; ?>>Default</option>
                        <option value="fixed" <?php echo ($currentSettings['navbar_style'] === 'fixed') ? 'selected' : ''; ?>>Fixed Top</option>
                        <option value="sticky" <?php echo ($currentSettings['navbar_style'] === 'sticky') ? 'selected' : ''; ?>>Sticky</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="card_style" class="form-label">Card Style</label>
                    <select class="form-select" id="card_style" name="card_style">
                        <option value="default" <?php echo ($currentSettings['card_style'] === 'default') ? 'selected' : ''; ?>>Default</option>
                        <option value="elevated" <?php echo ($currentSettings['card_style'] === 'elevated') ? 'selected' : ''; ?>>Elevated</option>
                        <option value="flat" <?php echo ($currentSettings['card_style'] === 'flat') ? 'selected' : ''; ?>>Flat</option>
                        <option value="outlined" <?php echo ($currentSettings['card_style'] === 'outlined') ? 'selected' : ''; ?>>Outlined</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4 mb-3">
                    <label for="border_radius" class="form-label">Border Radius (rem)</label>
                    <input type="number" class="form-control" id="border_radius" name="border_radius" value="<?php echo htmlspecialchars($currentSettings['border_radius'] ?: '0.375'); ?>" min="0" max="2" step="0.125">
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="container_max_width" class="form-label">Container Max Width (px)</label>
                    <input type="number" class="form-control" id="container_max_width" name="container_max_width" value="<?php echo htmlspecialchars($currentSettings['container_max_width'] ?: '1200'); ?>" min="800" max="1920" step="50">
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="sidebar_width" class="form-label">Sidebar Width (px)</label>
                    <input type="number" class="form-control" id="sidebar_width" name="sidebar_width" value="<?php echo htmlspecialchars($currentSettings['sidebar_width'] ?: '280'); ?>" min="200" max="400" step="10">
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6 mb-3">
                    <label for="content_spacing" class="form-label">Content Spacing (px)</label>
                    <input type="number" class="form-control" id="content_spacing" name="content_spacing" value="<?php echo htmlspecialchars($currentSettings['content_spacing'] ?: '30'); ?>" min="10" max="50" step="5">
                    <div class="form-text">Space between sidebar and main content</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Colors Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-layout-sidebar"></i> Sidebar Colors
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="sidebar_bg_color" class="form-label">Sidebar Background</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="sidebar_bg_color" name="sidebar_bg_color" value="<?php echo htmlspecialchars($currentSettings['sidebar_bg_color'] ?: '#343a40'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['sidebar_bg_color'] ?: '#343a40'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="sidebar_text_color" class="form-label">Sidebar Text Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="sidebar_text_color" name="sidebar_text_color" value="<?php echo htmlspecialchars($currentSettings['sidebar_text_color'] ?: '#ffffff'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['sidebar_text_color'] ?: '#ffffff'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="sidebar_hover_color" class="form-label">Sidebar Hover Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="sidebar_hover_color" name="sidebar_hover_color" value="<?php echo htmlspecialchars($currentSettings['sidebar_hover_color'] ?: '#007bff'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['sidebar_hover_color'] ?: '#007bff'); ?>" readonly>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logo and Branding Link -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-image"></i> Logo and Branding
            </h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                <strong>Logo and favicon management is handled separately.</strong>
                <br>
                Use the <a href="logo_management.php" class="alert-link">Logo Management</a> page to upload and manage your logos and favicons.
            </div>
        </div>
    </div>

    <!-- Theme Settings Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-moon-stars"></i> Theme Settings
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-4 mb-3">
                    <label for="theme_mode" class="form-label">Default Theme Mode</label>
                    <select class="form-select" id="theme_mode" name="theme_mode">
                        <option value="light" <?php echo ($currentSettings['theme_mode'] === 'light') ? 'selected' : ''; ?>>Light</option>
                        <option value="dark" <?php echo ($currentSettings['theme_mode'] === 'dark') ? 'selected' : ''; ?>>Dark</option>
                        <option value="auto" <?php echo ($currentSettings['theme_mode'] === 'auto') ? 'selected' : ''; ?>>Auto (System)</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="admin_theme" class="form-label">Admin Theme</label>
                    <select class="form-select" id="admin_theme" name="admin_theme">
                        <option value="default" <?php echo ($currentSettings['admin_theme'] === 'default') ? 'selected' : ''; ?>>Default</option>
                        <option value="modern" <?php echo ($currentSettings['admin_theme'] === 'modern') ? 'selected' : ''; ?>>Modern</option>
                        <option value="classic" <?php echo ($currentSettings['admin_theme'] === 'classic') ? 'selected' : ''; ?>>Classic</option>
                        <option value="minimal" <?php echo ($currentSettings['admin_theme'] === 'minimal') ? 'selected' : ''; ?>>Minimal</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="user_theme" class="form-label">User Theme</label>
                    <select class="form-select" id="user_theme" name="user_theme">
                        <option value="default" <?php echo ($currentSettings['user_theme'] === 'default') ? 'selected' : ''; ?>>Default</option>
                        <option value="modern" <?php echo ($currentSettings['user_theme'] === 'modern') ? 'selected' : ''; ?>>Modern</option>
                        <option value="classic" <?php echo ($currentSettings['user_theme'] === 'classic') ? 'selected' : ''; ?>>Classic</option>
                        <option value="minimal" <?php echo ($currentSettings['user_theme'] === 'minimal') ? 'selected' : ''; ?>>Minimal</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="enable_dark_mode" name="enable_dark_mode" <?php echo ($currentSettings['enable_dark_mode'] === '1') ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="enable_dark_mode">
                            Enable Dark Mode Support
                        </label>
                    </div>
                </div>
                <div class="col-lg-6 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="enable_theme_switcher" name="enable_theme_switcher" <?php echo ($currentSettings['enable_theme_switcher'] === '1') ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="enable_theme_switcher">
                            Show Theme Switcher to Users
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom CSS Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-code-slash"></i> Custom CSS
            </h5>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label for="custom_css" class="form-label">Additional CSS</label>
                <textarea class="form-control font-monospace" id="custom_css" name="custom_css" rows="10" placeholder="/* Add your custom CSS here */"><?php echo htmlspecialchars($currentSettings['custom_css']); ?></textarea>
                <div class="form-text">
                    Add custom CSS to override default styles. Use CSS custom properties (variables) for better theme integration.
                </div>
            </div>

            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> Available CSS Variables</h6>
                <div class="row">
                    <div class="col-md-6">
                        <small>
                            <strong>Colors:</strong><br>
                            --bs-primary, --bs-secondary<br>
                            --bs-success, --bs-danger<br>
                            --bs-warning, --bs-info<br>
                            --bs-body-bg, --bs-body-color
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small>
                            <strong>Layout:</strong><br>
                            --sidebar-width<br>
                            --bs-border-radius<br>
                            --bs-font-sans-serif<br>
                            --bs-body-font-size
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-save"></i> Save Appearance Settings
            </button>
            <button type="button" class="btn btn-outline-secondary ms-2" onclick="window.location.reload()">
                <i class="bi bi-arrow-clockwise"></i> Cancel
            </button>
        </div>
    </div>
</form>

<link href="<?php echo ADMIN_URL; ?>/css/custom-theme.css" rel="stylesheet">
<link href="<?php echo ADMIN_URL; ?>/css/theme-customizer.css" rel="stylesheet">
<script src="<?php echo ADMIN_URL; ?>/js/theme-customizer.js"></script>
<script src="<?php echo ADMIN_URL; ?>/assets/js/appearance-customizer.js"></script>

<?php include 'includes/footer.php'; ?>

                <!-- Advanced Theme Customizer -->
                <div class="theme-customizer">
                    <h3>Advanced Theme Customizer</h3>

                    <!-- Preset Selector -->
                    <div class="preset-selector">
                        <h4>Theme Presets</h4>
                        <div class="preset-grid">
                            <div class="preset-card" data-preset="default">
                                <h5>Default Blue</h5>
                                <div class="preset-colors">
                                    <div class="preset-color" style="background: #007bff;"></div>
                                    <div class="preset-color" style="background: #6c757d;"></div>
                                    <div class="preset-color" style="background: #28a745;"></div>
                                    <div class="preset-color" style="background: #dc3545;"></div>
                                </div>
                                <small>Classic and professional</small>
                            </div>
                            <div class="preset-card" data-preset="modern">
                                <h5>Modern Purple</h5>
                                <div class="preset-colors">
                                    <div class="preset-color" style="background: #6f42c1;"></div>
                                    <div class="preset-color" style="background: #20c997;"></div>
                                    <div class="preset-color" style="background: #e74c3c;"></div>
                                    <div class="preset-color" style="background: #f39c12;"></div>
                                </div>
                                <small>Modern and vibrant</small>
                            </div>
                            <div class="preset-card" data-preset="minimal">
                                <h5>Minimal Gray</h5>
                                <div class="preset-colors">
                                    <div class="preset-color" style="background: #495057;"></div>
                                    <div class="preset-color" style="background: #51cf66;"></div>
                                    <div class="preset-color" style="background: #ff6b6b;"></div>
                                    <div class="preset-color" style="background: #ffd43b;"></div>
                                </div>
                                <small>Clean and minimal</small>
                            </div>
                            <div class="preset-card" data-preset="vibrant">
                                <h5>Vibrant Orange</h5>
                                <div class="preset-colors">
                                    <div class="preset-color" style="background: #fd7e14;"></div>
                                    <div class="preset-color" style="background: #20c997;"></div>
                                    <div class="preset-color" style="background: #dc3545;"></div>
                                    <div class="preset-color" style="background: #ffc107;"></div>
                                </div>
                                <small>Energetic and warm</small>
                            </div>
                            <div class="preset-card" data-preset="corporate">
                                <h5>Corporate Blue</h5>
                                <div class="preset-colors">
                                    <div class="preset-color" style="background: #0d6efd;"></div>
                                    <div class="preset-color" style="background: #198754;"></div>
                                    <div class="preset-color" style="background: #dc3545;"></div>
                                    <div class="preset-color" style="background: #ffc107;"></div>
                                </div>
                                <small>Professional and trustworthy</small>
                            </div>
                        </div>

                        <!-- Theme Controls -->
                        <div class="theme-controls">
                            <div class="preview-toggle">
                                <input type="checkbox" id="previewToggle" class="form-check-input">
                                <label for="previewToggle" class="form-check-label">Live Preview</label>
                            </div>

                            <div class="theme-actions">
                                <button type="button" id="resetTheme" class="btn-theme btn-theme-secondary">
                                    <i class="bi bi-arrow-clockwise"></i> Reset
                                </button>
                                <div class="import-export">
                                    <button type="button" id="exportTheme" class="btn-theme btn-theme-secondary">
                                        <i class="bi bi-download"></i> Export
                                    </button>
                                    <button type="button" id="importTheme" class="btn-theme btn-theme-secondary">
                                        <i class="bi bi-upload"></i> Import
                                    </button>
                                    <input type="file" id="importFile" accept=".json">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Color Scheme Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-palette-fill"></i> Color Scheme
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <label for="primary_color" class="form-label">Primary Color</label>
                                <div class="input-group">
                                    <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" value="<?php echo htmlspecialchars($currentSettings['primary_color'] ?: '#007bff'); ?>">
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['primary_color'] ?: '#007bff'); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <label for="secondary_color" class="form-label">Secondary Color</label>
                                <div class="input-group">
                                    <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color" value="<?php echo htmlspecialchars($currentSettings['secondary_color'] ?: '#6c757d'); ?>">
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['secondary_color'] ?: '#6c757d'); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <label for="success_color" class="form-label">Success Color</label>
                                <div class="input-group">
                                    <input type="color" class="form-control form-control-color" id="success_color" name="success_color" value="<?php echo htmlspecialchars($currentSettings['success_color'] ?: '#28a745'); ?>">
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['success_color'] ?: '#28a745'); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <label for="danger_color" class="form-label">Danger Color</label>
                                <div class="input-group">
                                    <input type="color" class="form-control form-control-color" id="danger_color" name="danger_color" value="<?php echo htmlspecialchars($currentSettings['danger_color'] ?: '#dc3545'); ?>">
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['danger_color'] ?: '#dc3545'); ?>" readonly>
                                </div>
                            </div>
                        </div>
                            <h6 class="mb-3">Live Preview</h6>
                            <div class="live-preview-container p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="resetPreview">
                                        <i class="bi bi-arrow-clockwise"></i> Reset
                                    </button>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-secondary" id="exportPreview">
                                            <i class="bi bi-download"></i> Export
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="importPreview">
                                            <i class="bi bi-upload"></i> Import
                                        </button>
                                    </div>
                                </div>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-eye display-4"></i>
                                    <p class="mt-2">Live preview will appear here when you make changes</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Color Scheme Section -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-palette me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('color_scheme'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <!-- Primary Color -->
                            <div class="col-md-3">
                                <label class="form-label"><strong>Primary Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_primary_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#fd7e14'); ?>" id="primaryColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#fd7e14'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Secondary Color -->
                            <div class="col-md-3">
                                <label class="form-label"><strong>Secondary Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_secondary_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#6c757d'); ?>" id="secondaryColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#6c757d'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Success Color -->
                            <div class="col-md-3">
                                <label class="form-label"><strong>Success Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_success_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['success_color'] ?? '#28a745'); ?>" id="successColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['success_color'] ?? '#28a745'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Danger Color -->
                            <div class="col-md-3">
                                <label class="form-label"><strong>Danger Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_danger_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['danger_color'] ?? '#dc3545'); ?>" id="dangerColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['danger_color'] ?? '#dc3545'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Background Color -->
                            <div class="col-md-4">
                                <label class="form-label"><strong>Background Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_background_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['background_color'] ?? '#ffffff'); ?>" id="backgroundColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['background_color'] ?? '#ffffff'); ?>" readonly>
                                </div>
                                <small class="text-muted">Use contrast with background color</small>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Text Color -->
                            <div class="col-md-4">
                                <label class="form-label"><strong>Text Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_text_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['text_color'] ?? '#8b4513'); ?>" id="textColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['text_color'] ?? '#8b4513'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Link Color -->
                            <div class="col-md-4">
                                <label class="form-label"><strong>Link Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_link_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['link_color'] ?? '#fd7e14'); ?>" id="linkColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['link_color'] ?? '#fd7e14'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Color Presets -->
                        <div class="mt-4">
                            <h6 class="mb-3">Color Presets</h6>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="default">Default</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="blue">Blue</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="green">Green</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="purple">Purple</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="orange">Orange</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Typography Section -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-fonts me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('typography'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label"><strong>Primary Font</strong></label>
                                <select name="setting_font_family" class="form-select" id="primaryFont">
                                    <option value="Inter" <?php echo ($settings['font_family'] ?? '') === 'Inter' ? 'selected' : ''; ?>>Inter</option>
                                    <option value="Arial, sans-serif" <?php echo ($settings['font_family'] ?? '') === 'Arial, sans-serif' ? 'selected' : ''; ?>>Arial</option>
                                    <option value="Helvetica, sans-serif" <?php echo ($settings['font_family'] ?? '') === 'Helvetica, sans-serif' ? 'selected' : ''; ?>>Helvetica</option>
                                    <option value="Georgia, serif" <?php echo ($settings['font_family'] ?? '') === 'Georgia, serif' ? 'selected' : ''; ?>>Georgia</option>
                                    <option value="Times New Roman, serif" <?php echo ($settings['font_family'] ?? '') === 'Times New Roman, serif' ? 'selected' : ''; ?>>Times New Roman</option>
                                    <option value="Roboto, sans-serif" <?php echo ($settings['font_family'] ?? '') === 'Roboto, sans-serif' ? 'selected' : ''; ?>>Roboto</option>
                                    <option value="Open Sans, sans-serif" <?php echo ($settings['font_family'] ?? '') === 'Open Sans, sans-serif' ? 'selected' : ''; ?>>Open Sans</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Base Font Size (px)</strong></label>
                                <input type="number" name="setting_base_font_size" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['base_font_size'] ?? '16'); ?>" min="12" max="24">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Normal Weight</strong></label>
                                <select name="setting_font_weight_normal" class="form-select">
                                    <option value="300" <?php echo ($settings['font_weight_normal'] ?? '') === '300' ? 'selected' : ''; ?>>Light (300)</option>
                                    <option value="400" <?php echo ($settings['font_weight_normal'] ?? '') === '400' ? 'selected' : ''; ?>>Normal (400)</option>
                                    <option value="500" <?php echo ($settings['font_weight_normal'] ?? '') === '500' ? 'selected' : ''; ?>>Medium (500)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Bold Weight</strong></label>
                                <select name="setting_font_weight_bold" class="form-select">
                                    <option value="600" <?php echo ($settings['font_weight_bold'] ?? '') === '600' ? 'selected' : ''; ?>>Semi Bold (600)</option>
                                    <option value="700" <?php echo ($settings['font_weight_bold'] ?? '') === '700' ? 'selected' : ''; ?>>Bold (700)</option>
                                    <option value="800" <?php echo ($settings['font_weight_bold'] ?? '') === '800' ? 'selected' : ''; ?>>Extra Bold (800)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Line Height</strong></label>
                                <input type="number" name="setting_line_height" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['line_height'] ?? '1.5'); ?>" min="1" max="3" step="0.1">
                            </div>
                        </div>

                        <!-- Typography Preview -->
                        <div class="mt-4">
                            <h6 class="mb-3">Typography Preview</h6>
                            <div class="typography-preview p-3 border rounded">
                                <h1 class="preview-heading">Heading 1</h1>
                                <h2 class="preview-heading">Heading 2</h2>
                                <h3 class="preview-heading">Heading 3</h3>
                                <p class="preview-text">This is a paragraph of text to demonstrate the typography settings. It shows how the font family, size, weight, and line height work together.</p>
                                <p class="preview-text"><strong>Bold text</strong> and <a href="#" class="preview-link">link text</a> examples.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Layout Settings -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-layout-sidebar me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('layout_settings'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label"><strong>Sidebar Style</strong></label>
                                <select name="setting_sidebar_style" class="form-select">
                                    <option value="default" <?php echo ($settings['sidebar_style'] ?? '') === 'default' ? 'selected' : ''; ?>>Default</option>
                                    <option value="compact" <?php echo ($settings['sidebar_style'] ?? '') === 'compact' ? 'selected' : ''; ?>>Compact</option>
                                    <option value="minimal" <?php echo ($settings['sidebar_style'] ?? '') === 'minimal' ? 'selected' : ''; ?>>Minimal</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Navigation Style</strong></label>
                                <select name="setting_navigation_style" class="form-select">
                                    <option value="default" <?php echo ($settings['navigation_style'] ?? '') === 'default' ? 'selected' : ''; ?>>Default</option>
                                    <option value="pills" <?php echo ($settings['navigation_style'] ?? '') === 'pills' ? 'selected' : ''; ?>>Pills</option>
                                    <option value="underline" <?php echo ($settings['navigation_style'] ?? '') === 'underline' ? 'selected' : ''; ?>>Underline</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Card Style</strong></label>
                                <select name="setting_card_style" class="form-select">
                                    <option value="default" <?php echo ($settings['card_style'] ?? '') === 'default' ? 'selected' : ''; ?>>Default</option>
                                    <option value="shadow" <?php echo ($settings['card_style'] ?? '') === 'shadow' ? 'selected' : ''; ?>>Shadow</option>
                                    <option value="border" <?php echo ($settings['card_style'] ?? '') === 'border' ? 'selected' : ''; ?>>Border</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Border Radius (rem)</strong></label>
                                <input type="number" name="setting_border_radius" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['border_radius'] ?? '0.375'); ?>" min="0" max="2" step="0.125">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Container Max Width (px)</strong></label>
                                <input type="number" name="setting_container_max_width" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['container_max_width'] ?? '1200'); ?>" min="800" max="1600" step="100">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Sidebar Width (px)</strong></label>
                                <input type="number" name="setting_sidebar_width" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['sidebar_width'] ?? '300'); ?>" min="200" max="400" step="10">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label"><strong>Content Spacing (px)</strong></label>
                                <input type="number" name="setting_content_spacing" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['content_spacing'] ?? '30'); ?>" min="10" max="60" step="5">
                                <small class="text-muted">Space between sidebar and main content</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Colors -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-palette me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('sidebar_colors'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label"><strong>Sidebar Background</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_sidebar_background" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['sidebar_background'] ?? '#e67f0a'); ?>" id="sidebarBackground">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['sidebar_background'] ?? '#e67f0a'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Sidebar Text Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_sidebar_text_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['sidebar_text_color'] ?? '#ffffff'); ?>" id="sidebarTextColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['sidebar_text_color'] ?? '#ffffff'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Sidebar Hover Color</strong></label>
                                <div class="color-input-group">
                                    <input type="color" name="setting_sidebar_hover_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['sidebar_hover_color'] ?? '#102841'); ?>" id="sidebarHoverColor">
                                    <input type="text" class="form-control mt-1" value="<?php echo htmlspecialchars($settings['sidebar_hover_color'] ?? '#102841'); ?>" readonly>
                                </div>
                                <div class="color-palette mt-2">
                                    <div class="color-swatches">
                                        <span class="color-swatch" style="background: #007bff;" data-color="#007bff"></span>
                                        <span class="color-swatch" style="background: #6f42c1;" data-color="#6f42c1"></span>
                                        <span class="color-swatch" style="background: #e83e8c;" data-color="#e83e8c"></span>
                                        <span class="color-swatch" style="background: #dc3545;" data-color="#dc3545"></span>
                                        <span class="color-swatch" style="background: #fd7e14;" data-color="#fd7e14"></span>
                                        <span class="color-swatch" style="background: #ffc107;" data-color="#ffc107"></span>
                                        <span class="color-swatch" style="background: #28a745;" data-color="#28a745"></span>
                                        <span class="color-swatch" style="background: #20c997;" data-color="#20c997"></span>
                                        <span class="color-swatch" style="background: #17a2b8;" data-color="#17a2b8"></span>
                                        <span class="color-swatch" style="background: #6c757d;" data-color="#6c757d"></span>
                                        <span class="color-swatch" style="background: #343a40;" data-color="#343a40"></span>
                                        <span class="color-swatch" style="background: #000000;" data-color="#000000"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Logo and Branding -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-image me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('logo_and_branding'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label"><strong>Organization Name</strong></label>
                                <input type="text" name="setting_organization_name" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['organization_name'] ?? 'Church Management System'); ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Tagline</strong></label>
                                <input type="text" name="setting_tagline" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['tagline'] ?? 'Connecting Faith, Building Community'); ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Theme Settings -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-gear me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('theme_settings'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label"><strong>Default Theme Mode</strong></label>
                                <select name="setting_default_theme_mode" class="form-select">
                                    <option value="auto" <?php echo ($settings['default_theme_mode'] ?? '') === 'auto' ? 'selected' : ''; ?>>Auto (System)</option>
                                    <option value="light" <?php echo ($settings['default_theme_mode'] ?? '') === 'light' ? 'selected' : ''; ?>>Light</option>
                                    <option value="dark" <?php echo ($settings['default_theme_mode'] ?? '') === 'dark' ? 'selected' : ''; ?>>Dark</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>Admin Theme</strong></label>
                                <select name="setting_admin_theme" class="form-select">
                                    <option value="modern" <?php echo ($settings['admin_theme'] ?? '') === 'modern' ? 'selected' : ''; ?>>Modern</option>
                                    <option value="classic" <?php echo ($settings['admin_theme'] ?? '') === 'classic' ? 'selected' : ''; ?>>Classic</option>
                                    <option value="minimal" <?php echo ($settings['admin_theme'] ?? '') === 'minimal' ? 'selected' : ''; ?>>Minimal</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label"><strong>User Theme</strong></label>
                                <select name="setting_user_theme" class="form-select">
                                    <option value="minimal" <?php echo ($settings['user_theme'] ?? '') === 'minimal' ? 'selected' : ''; ?>>Minimal</option>
                                    <option value="modern" <?php echo ($settings['user_theme'] ?? '') === 'modern' ? 'selected' : ''; ?>>Modern</option>
                                    <option value="classic" <?php echo ($settings['user_theme'] ?? '') === 'classic' ? 'selected' : ''; ?>>Classic</option>
                                </select>
                            </div>
                        </div>
                        <div class="row g-4 mt-2">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="setting_enable_dark_mode" id="enableDarkMode"
                                           <?php echo ($settings['enable_dark_mode'] ?? '') === '1' ? 'checked' : ''; ?> value="1">
                                    <label class="form-check-label" for="enableDarkMode">
                                        <strong>Enable Dark Mode Support</strong>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="setting_show_theme_switcher" id="showThemeSwitcher"
                                           <?php echo ($settings['show_theme_switcher'] ?? '') === '1' ? 'checked' : ''; ?> value="1">
                                    <label class="form-check-label" for="showThemeSwitcher">
                                        <strong>Show Theme Switcher to Users</strong>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom CSS -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="bi bi-code-slash me-2 text-primary"></i>
                        <h5 class="mb-0"><?php _e('custom_css'); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label"><strong>Additional CSS</strong></label>
                            <textarea name="setting_custom_css" class="form-control" rows="8"
                                      placeholder="/* Add your custom CSS here */"><?php echo htmlspecialchars($settings['custom_css'] ?? ''); ?></textarea>
                            <small class="text-muted">Add custom CSS to override default styles. Use CSS custom properties (variables) for better theme integration.</small>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="d-flex justify-content-end mb-4">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="bi bi-check-lg"></i> Save Appearance Settings
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-lg ms-2" id="cancelChanges">
                        <i class="bi bi-x-lg"></i> Cancel
                    </button>
                </div>
            </form>

<style>
.theme-preset {
    cursor: pointer;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
}

.theme-preset:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.theme-preset.active {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.theme-colors {
    display: flex;
    justify-content: center;
    gap: 5px;
}

.color-dot {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-block;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.color-input-group {
    position: relative;
}

.color-palette {
    margin-top: 8px;
}

.color-swatches {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.color-swatch {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    transition: transform 0.2s ease;
}

.color-swatch:hover {
    transform: scale(1.1);
}

.live-preview-container {
    background: #f8f9fa;
    min-height: 200px;
}

.typography-preview {
    background: #fff;
}

.preview-heading {
    color: var(--bs-primary, #007bff);
    margin-bottom: 0.5rem;
}

.preview-text {
    color: var(--bs-body-color, #212529);
    margin-bottom: 0.5rem;
}

.preview-link {
    color: var(--bs-link-color, #007bff);
    text-decoration: none;
}

.preview-link:hover {
    text-decoration: underline;
}

.color-preset.active {
    background-color: #007bff;
    color: white;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Color swatch functionality
    document.querySelectorAll('.color-swatch').forEach(swatch => {
        swatch.addEventListener('click', function() {
            const color = this.dataset.color;
            const colorInput = this.closest('.col-md-3, .col-md-4').querySelector('input[type="color"]');
            const textInput = this.closest('.col-md-3, .col-md-4').querySelector('input[type="text"]');

            if (colorInput && textInput) {
                colorInput.value = color;
                textInput.value = color;

                // Trigger change event for live preview
                colorInput.dispatchEvent(new Event('change'));
            }
        });
    });

    // Color input sync
    document.querySelectorAll('input[type="color"]').forEach(colorInput => {
        colorInput.addEventListener('change', function() {
            const textInput = this.parentNode.querySelector('input[type="text"]');
            if (textInput) {
                textInput.value = this.value;
            }

            // Update live preview
            updateLivePreview();
        });
    });

    // Theme preset functionality
    document.querySelectorAll('.theme-preset').forEach(preset => {
        preset.addEventListener('click', function() {
            // Remove active class from all presets
            document.querySelectorAll('.theme-preset').forEach(p => p.classList.remove('active'));

            // Add active class to clicked preset
            this.classList.add('active');

            // Apply theme colors based on preset
            const theme = this.dataset.theme;
            applyThemePreset(theme);
        });
    });

    // Color preset functionality
    document.querySelectorAll('.color-preset').forEach(preset => {
        preset.addEventListener('click', function() {
            // Remove active class from all presets
            document.querySelectorAll('.color-preset').forEach(p => p.classList.remove('active'));

            // Add active class to clicked preset
            this.classList.add('active');

            // Apply color preset
            const presetName = this.dataset.preset;
            applyColorPreset(presetName);
        });
    });

    // Typography preview update
    document.querySelectorAll('select[name^="setting_font"], input[name^="setting_font"], input[name="setting_line_height"]').forEach(input => {
        input.addEventListener('change', updateTypographyPreview);
    });

    // Preview changes button
    document.getElementById('previewChanges')?.addEventListener('click', function() {
        updateLivePreview();
        alert('Preview updated! Changes are visible in the preview area.');
    });

    // Reset to defaults button
    document.getElementById('resetToDefaults')?.addEventListener('click', function() {
        if (confirm('Are you sure you want to reset all appearance settings to defaults? This action cannot be undone.')) {
            resetToDefaults();
        }
    });

    // Cancel changes button
    document.getElementById('cancelChanges')?.addEventListener('click', function() {
        if (confirm('Are you sure you want to cancel all changes? Unsaved changes will be lost.')) {
            location.reload();
        }
    });

    function applyThemePreset(theme) {
        const presets = {
            'default-blue': {
                primary: '#007bff',
                secondary: '#6c757d',
                success: '#28a745',
                danger: '#dc3545'
            },
            'modern-purple': {
                primary: '#6f42c1',
                secondary: '#e83e8c',
                success: '#20c997',
                danger: '#fd7e14'
            },
            'minimal-gray': {
                primary: '#495057',
                secondary: '#6c757d',
                success: '#28a745',
                danger: '#dc3545'
            },
            'vibrant-orange': {
                primary: '#fd7e14',
                secondary: '#ffc107',
                success: '#28a745',
                danger: '#dc3545'
            },
            'corporate-blue': {
                primary: '#0d6efd',
                secondary: '#198754',
                success: '#20c997',
                danger: '#dc3545'
            }
        };

        if (presets[theme]) {
            const colors = presets[theme];

            // Update color inputs
            updateColorInput('setting_primary_color', colors.primary);
            updateColorInput('setting_secondary_color', colors.secondary);
            updateColorInput('setting_success_color', colors.success);
            updateColorInput('setting_danger_color', colors.danger);

            updateLivePreview();
        }
    }

    function applyColorPreset(preset) {
        const presets = {
            'default': { primary: '#007bff', secondary: '#6c757d', success: '#28a745', danger: '#dc3545' },
            'blue': { primary: '#0d6efd', secondary: '#0dcaf0', success: '#198754', danger: '#dc3545' },
            'green': { primary: '#198754', secondary: '#20c997', success: '#28a745', danger: '#dc3545' },
            'purple': { primary: '#6f42c1', secondary: '#d63384', success: '#20c997', danger: '#dc3545' },
            'orange': { primary: '#fd7e14', secondary: '#ffc107', success: '#198754', danger: '#dc3545' }
        };

        if (presets[preset]) {
            const colors = presets[preset];
            Object.keys(colors).forEach(key => {
                updateColorInput(`setting_${key}_color`, colors[key]);
            });
            updateLivePreview();
        }
    }

    function updateColorInput(name, color) {
        const colorInput = document.querySelector(`input[name="${name}"]`);
        const textInput = colorInput?.parentNode.querySelector('input[type="text"]');

        if (colorInput) {
            colorInput.value = color;
        }
        if (textInput) {
            textInput.value = color;
        }
    }

    function updateTypographyPreview() {
        const fontFamily = document.querySelector('select[name="setting_font_family"]')?.value || 'Inter';
        const fontSize = document.querySelector('input[name="setting_base_font_size"]')?.value || '16';
        const lineHeight = document.querySelector('input[name="setting_line_height"]')?.value || '1.5';

        const preview = document.querySelector('.typography-preview');
        if (preview) {
            preview.style.fontFamily = fontFamily;
            preview.style.fontSize = fontSize + 'px';
            preview.style.lineHeight = lineHeight;
        }
    }

    function updateLivePreview() {
        // This would update a live preview area
        // For now, we'll just update the typography preview
        updateTypographyPreview();
    }

    function resetToDefaults() {
        // Reset all form inputs to default values
        const form = document.getElementById('appearanceForm');
        if (form) {
            form.reset();

            // Reset color inputs to defaults
            updateColorInput('setting_primary_color', '#fd7e14');
            updateColorInput('setting_secondary_color', '#6c757d');
            updateColorInput('setting_success_color', '#28a745');
            updateColorInput('setting_danger_color', '#dc3545');

            updateLivePreview();
        }
    }

    // Initialize typography preview
    updateTypographyPreview();
});
</script>

<?php include "includes/footer.php"; ?>
