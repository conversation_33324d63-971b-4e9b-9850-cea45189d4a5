<?php
// Set error reporting and execution time
ini_set('display_errors', 1);
error_reporting(E_ALL);
set_time_limit(300); // 5 minutes max execution time

// Include configuration
require_once 'config.php';
require_once 'send_birthday_reminders.php';

// Initialize variables
$success_message = '';
$error_message = '';

// Get admin email from settings, config, or set a default
$admin_email = '<EMAIL>';

// Initialize BirthdayReminder with admin email
$birthdayReminder = new BirthdayReminder($pdo, $admin_email);

// Get a specific birthday template for testing
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = 18");
$stmt->execute();
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    die("Could not find template with ID 18");
}

// Create test member data
$birthdayMember = [
    'id' => 123,
    'full_name' => '<PERSON>',
    'first_name' => '<PERSON>',
    'last_name' => '<PERSON>',
    'email' => '<EMAIL>',
    'phone_number' => '************',
    'birth_date' => '1985-03-22',
];

$recipientMember = [
    'id' => 456,
    'full_name' => '<PERSON>',
    'first_name' => 'John',
    'last_name' => 'Smith', 
    'email' => '<EMAIL>',
    'phone_number' => '************',
];

// Test the processing of subject and content
echo "<h1>Testing Email Template Processing</h1>";

echo "<h2>Original Subject:</h2>";
echo htmlspecialchars($template['subject']);

echo "<h2>Original Content (First 200 chars):</h2>";
echo htmlspecialchars(substr($template['content'], 0, 200)) . "...";

// Process the template using our updated function
$processed_subject = $birthdayReminder->processBirthdayMemberTemplate($template['subject'], $recipientMember, $birthdayMember, 4);
$processed_content = $birthdayReminder->processBirthdayMemberTemplate($template['content'], $recipientMember, $birthdayMember, 4);

echo "<h2>Processed Subject:</h2>";
echo htmlspecialchars($processed_subject);

echo "<h2>Processed Content (First 200 chars):</h2>";
echo htmlspecialchars(substr($processed_content, 0, 200)) . "...";

// Check for duplicate names in subject and content
$duplicateSubject = false;
if (strpos($processed_subject, "MichaelMichael") !== false || 
    strpos($processed_subject, "MichaelJohnson") !== false || 
    strpos($processed_subject, "JohnsonMichael") !== false) {
    $duplicateSubject = true;
}

$duplicateContent = false;
if (strpos($processed_content, "MichaelMichael") !== false || 
    strpos($processed_content, "Let's make MichaelMichael's") !== false) {
    $duplicateContent = true;
}

echo "<h2>Results:</h2>";
echo "<p>Duplicate names in subject: " . ($duplicateSubject ? "YES - PROBLEM!" : "No - Good!") . "</p>";
echo "<p>Duplicate names in content: " . ($duplicateContent ? "YES - PROBLEM!" : "No - Good!") . "</p>";

echo "<h2>Full Processed Subject:</h2>";
echo htmlspecialchars($processed_subject);

echo "<h2>Sample Content Fragment:</h2>";
if (preg_match('/<div class="profile-photo">(.*?)<\/h2>/s', $processed_content, $matches)) {
    echo "<pre>" . htmlspecialchars($matches[0]) . "</pre>";
}

if (preg_match('/Let\'s make(.*?)birthday special/s', $processed_content, $matches)) {
    echo "<h2>Let's make section:</h2>";
    echo "<pre>" . htmlspecialchars($matches[0]) . "</pre>";
}

echo "<p>Test completed.</p>"; 