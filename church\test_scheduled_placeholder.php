<?php
/**
 * Test Script for Scheduled Email Placeholders
 * 
 * This script tests the scheduled email placeholder functionality with the fix.
 */

// Include necessary files
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/email_functions.php';

// Set up logging
$logFile = __DIR__ . '/logs/test_scheduled_placeholder.log';
file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test scheduled email placeholder script started.' . PHP_EOL);

// Create a test email with placeholders
$testEmailContent = '
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Test Scheduled Email Placeholders</title>
    <style>
        body { font-family: Arial, sans-serif; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        h1 { color: #3366cc; text-align: center; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .highlight { background-color: #f8f8f8; padding: 15px; border-left: 4px solid #3366cc; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th { background-color: #3366cc; color: white; padding: 10px; text-align: left; border: 1px solid #ddd; }
        td { padding: 10px; border: 1px solid #ddd; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        .button { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; }
        .button a { display: inline-block; background-color: #3366cc; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .footer { font-size: 12px; color: #666; text-align: center; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hello, {full_name}!</h1>
        
        <p>This is a test email to verify that placeholders are properly replaced in scheduled emails.</p>
        
        <div class="highlight">
            <p>Your email address is: {email}</p>
            <p>Your first name is: {first_name}</p>
            <p>Your full name is: {full_name}</p>
        </div>
        
        <table>
            <tr>
                <th>Field</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Member Name</td>
                <td>{member_name}</td>
            </tr>
            <tr>
                <td>Member Email</td>
                <td>{member_email}</td>
            </tr>
            <tr>
                <td>Current Date</td>
                <td>{current_date}</td>
            </tr>
            <tr>
                <td>Church Name</td>
                <td>{church_name}</td>
            </tr>
        </table>
        
        <div class="button">
            <a href="{unsubscribe_link}">Unsubscribe</a>
        </div>
        
        <p class="footer">
            This is a test email from {church_name}. Please ignore.
        </p>
    </div>
</body>
</html>
';

// Get a test recipient
try {
    $stmt = $pdo->prepare("SELECT id, email, full_name FROM members WHERE email IS NOT NULL AND email != '' LIMIT 1");
    $stmt->execute();
    $recipient = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$recipient) {
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] No recipients found with valid email addresses.' . PHP_EOL, FILE_APPEND);
        echo "No recipients found with valid email addresses.\n";
        exit;
    }
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test recipient: ' . $recipient['email'] . PHP_EOL, FILE_APPEND);
    
    // Create a test schedule in the database
    $stmt = $pdo->prepare("
        INSERT INTO email_schedules 
        (name, description, frequency, status, next_run, created_at, updated_at)
        VALUES 
        ('Test Placeholder Fix', 'Testing placeholder replacement in scheduled emails', 'once', 'active', NOW(), NOW(), NOW())
    ");
    $stmt->execute();
    $scheduleId = $pdo->lastInsertId();
    
    // Add schedule settings
    $stmt = $pdo->prepare("
        INSERT INTO email_schedule_settings
        (schedule_id, template_id, custom_subject, custom_content, track_opens, track_clicks)
        VALUES
        (?, NULL, 'Test Scheduled Email Placeholders for {full_name}', ?, 1, 1)
    ");
    $stmt->execute([$scheduleId, $testEmailContent]);
    
    // Add recipient to schedule
    $stmt = $pdo->prepare("
        INSERT INTO email_schedule_recipients
        (schedule_id, recipient_id, recipient_type, status)
        VALUES
        (?, ?, 'member', 'pending')
    ");
    $stmt->execute([$scheduleId, $recipient['id']]);
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Created test schedule with ID: ' . $scheduleId . PHP_EOL, FILE_APPEND);
    
    // Run the scheduled email processor
    echo "Created test schedule with ID: $scheduleId\n";
    echo "Running scheduled email processor...\n";
    
    // Execute the process_scheduled_emails.php script
    require_once __DIR__ . '/cron/process_scheduled_emails.php';
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test scheduled email placeholder script completed.' . PHP_EOL, FILE_APPEND);
    echo "Test completed. Check logs for details.\n";
    
} catch (Exception $e) {
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
    echo "Error: " . $e->getMessage() . "\n";
}
