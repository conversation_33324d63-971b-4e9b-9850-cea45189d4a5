<?php
// Get the current page filename to highlight the active menu item
$current_page = basename($_SERVER['PHP_SELF']);

// Helper function to build admin URL consistently
if (!function_exists('admin_url_for')) {
    function admin_url_for($page) {
        if (defined('ADMIN_URL')) {
            return ADMIN_URL . '/' . $page;
        }
        return $page; // Fallback for pages in the same directory
    }
}

// Helper function to check if current page is active
if (!function_exists('is_active')) {
    function is_active($page_name) {
        global $current_page;
        return ($current_page == $page_name) ? 'class="active"' : '';
    }
}

// Helper function to check if any page in array is active (for collapsible sections)
if (!function_exists('is_section_active')) {
    function is_section_active($pages) {
        global $current_page;
        return in_array($current_page, $pages) ? 'show' : '';
    }
}
?>

<!-- Sidebar -->
<div class="col-auto sidebar themed-sidebar" id="sidebar">
    <div class="sidebar-header position-relative">
        <?php
        // Use the existing logo management system
        $headerLogo = get_site_setting('header_logo', '');
        $mainLogo = get_site_setting('main_logo', '');
        $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;

        // Get organization name for initials
        $organizationName = get_organization_name();
        $siteInitials = '';
        if (!empty($organizationName)) {
            $words = explode(' ', $organizationName);
            foreach ($words as $word) {
                if (!empty($word)) {
                    $siteInitials .= strtoupper(substr($word, 0, 1));
                }
            }
            // Limit to 3 characters max
            $siteInitials = substr($siteInitials, 0, 3);
        }
        if (empty($siteInitials)) {
            $siteInitials = 'CA'; // Default fallback
        }
        ?>

        <!-- Logo/Brand for expanded sidebar -->
        <div class="sidebar-brand-container">
            <?php if (!empty($logoToUse)): ?>
                <div class="navbar-brand mb-0 logo-container">
                    <img src="<?php echo get_base_url() . '/' . htmlspecialchars($logoToUse); ?>"
                         alt="<?php echo get_admin_title(); ?>"
                         class="sidebar-logo">
                </div>
            <?php else: ?>
                <h5 class="navbar-brand mb-0"><span class="navbar-brand-text"><?php echo get_admin_title(); ?></span></h5>
            <?php endif; ?>
        </div>

        <!-- Initials for collapsed sidebar -->
        <div class="sidebar-initials-container">
            <div class="sidebar-initials">
                <?php echo $siteInitials; ?>
            </div>
        </div>

        <!-- Internal toggle button for desktop -->
        <button class="sidebar-toggle-btn d-none d-md-block" id="sidebarToggleDesktop" title="Toggle Sidebar">
            <i class="bi bi-chevron-left"></i>
        </button>

        <!-- Mobile toggle button -->
        <button class="d-md-none btn btn-sm btn-outline-light" id="sidebarToggle">
            <i class="bi bi-list"></i>
        </button>
    </div>
    <div class="sidebar-content">
        <ul class="nav flex-column">
            <!-- Dashboard -->
            <li class="nav-item">
                <a <?php echo is_active('dashboard.php'); ?> href="<?php echo admin_url_for('dashboard.php'); ?>" title="Dashboard">
                    <i class="bi bi-speedometer2"></i> <span class="menu-text">Dashboard</span>
                </a>
            </li>
            
            <!-- Member Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="bi bi-people-fill"></i> <span class="menu-text"><?php echo get_member_term(); ?> Management</span>
                </div>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('members.php'); ?> href="<?php echo admin_url_for('members.php'); ?>" title="<?php echo get_member_term(true); ?>">
                    <i class="bi bi-people"></i> <span class="menu-text"><?php echo get_member_term(true); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('add_member.php'); ?> href="<?php echo admin_url_for('add_member.php'); ?>" title="Add <?php echo get_member_term(); ?>">
                    <i class="bi bi-person-plus"></i> <span class="menu-text">Add <?php echo get_member_term(); ?></span>
                </a>
            </li>

            <!-- Events Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="bi bi-calendar-event-fill"></i> <span class="menu-text">Events Management</span>
                </div>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('events.php'); ?> href="<?php echo admin_url_for('events.php'); ?>" title="Events">
                    <i class="bi bi-calendar-event"></i> <span class="menu-text">Events</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('event_attendance.php'); ?> href="<?php echo admin_url_for('event_attendance.php'); ?>" title="Event Attendance">
                    <i class="bi bi-calendar-check"></i> <span class="menu-text">Event Attendance</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('event_categories.php'); ?> href="<?php echo admin_url_for('event_categories.php'); ?>" title="Event Categories">
                    <i class="bi bi-tags"></i> <span class="menu-text">Event Categories</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('event_reports.php'); ?> href="<?php echo admin_url_for('event_reports.php'); ?>" title="Event Reports">
                    <i class="bi bi-file-earmark-bar-graph"></i> <span class="menu-text">Event Reports</span>
                </a>
            </li>

            <!-- Email Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="bi bi-envelope-fill"></i> <span class="menu-text">Email Management</span>
                </div>
            </li>
            <!-- Email Communication -->
            <li class="nav-item">
                <a <?php echo is_active('bulk_email.php'); ?> href="<?php echo admin_url_for('bulk_email.php'); ?>" title="Bulk Email">
                    <i class="bi bi-envelope-fill"></i> <span class="menu-text">Bulk Email</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('email_scheduler.php'); ?> href="<?php echo admin_url_for('email_scheduler.php'); ?>" title="Email Scheduler">
                    <i class="bi bi-calendar-event"></i> <span class="menu-text">Email Scheduler</span>
                </a>
            </li>

            <!-- Contact Management -->
            <li class="nav-item">
                <a <?php echo is_active('contacts.php'); ?> href="<?php echo admin_url_for('contacts.php'); ?>" title="Contact Management">
                    <i class="bi bi-person-lines-fill"></i> <span class="menu-text">Contacts</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('contact_groups.php'); ?> href="<?php echo admin_url_for('contact_groups.php'); ?>" title="Contact Groups">
                    <i class="bi bi-folder"></i> <span class="menu-text">Contact Groups</span>
                </a>
            </li>

            <!-- Birthday Management -->
            <li class="nav-item">
                <a <?php echo is_active('birthday.php'); ?> href="<?php echo admin_url_for('birthday.php'); ?>" title="Birthday Messages">
                    <i class="bi bi-gift"></i> <span class="menu-text">Birthday Messages</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('send_birthday_emails.php'); ?> href="<?php echo admin_url_for('send_birthday_emails.php'); ?>" title="Send Birthday Emails">
                    <i class="bi bi-envelope-paper"></i> <span class="menu-text">Send Birthday Emails</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('test_birthday_email.php'); ?> href="<?php echo admin_url_for('test_birthday_email.php'); ?>" title="Test Birthday Emails">
                    <i class="bi bi-envelope-check"></i> <span class="menu-text">Test Birthday Emails</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('debug_placeholders.php'); ?> href="<?php echo admin_url_for('debug_placeholders.php'); ?>" title="Debug Email Placeholders">
                    <i class="bi bi-bug"></i> <span class="menu-text">Debug Placeholders</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('send_birthday_notification.php'); ?> href="<?php echo admin_url_for('send_birthday_notification.php'); ?>" title="Birthday Notifications">
                    <i class="bi bi-bell"></i> <span class="menu-text">Birthday Notifications</span>
                </a>
            </li>

            <!-- Email Templates -->
            <li class="nav-item">
                <a <?php echo is_active('email_templates.php'); ?> href="<?php echo admin_url_for('email_templates.php'); ?>" title="Email Templates">
                    <i class="bi bi-envelope"></i> <span class="menu-text">Email Templates</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('automated_email_templates.php'); ?> href="<?php echo admin_url_for('automated_email_templates.php'); ?>" title="Automated Email Templates">
                    <i class="bi bi-clock"></i> <span class="menu-text">Automated Templates</span>
                </a>
            </li>

            <!-- WhatsApp Integration -->
            <li class="nav-item">
                <a <?php echo is_active('whatsapp_templates.php'); ?> href="<?php echo admin_url_for('whatsapp_templates.php'); ?>" title="WhatsApp Templates">
                    <i class="bi bi-whatsapp"></i> <span class="menu-text">WhatsApp Templates</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('whatsapp_messages.php'); ?> href="<?php echo admin_url_for('whatsapp_messages.php'); ?>" title="WhatsApp Messages">
                    <i class="bi bi-whatsapp"></i> <span class="menu-text">WhatsApp Messages</span>
                </a>
            </li>

            <!-- Analytics & Tools -->
            <li class="nav-item">
                <a <?php echo is_active('email_analytics.php'); ?> href="<?php echo admin_url_for('email_analytics.php'); ?>" title="Email Analytics">
                    <i class="bi bi-graph-up"></i> <span class="menu-text">Email Analytics</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('about_shortcodes.php'); ?> href="<?php echo admin_url_for('about_shortcodes.php'); ?>" title="About & Shortcodes">
                    <i class="bi bi-info-circle"></i> <span class="menu-text">About & Shortcodes</span>
                </a>
            </li>

            <!-- SMS Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="bi bi-chat-text-fill"></i> <span class="menu-text">SMS Management</span>
                </div>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('single_sms.php'); ?> href="<?php echo admin_url_for('single_sms.php'); ?>" title="Single SMS">
                    <i class="bi bi-chat-text"></i> <span class="menu-text">Single SMS</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('bulk_sms.php'); ?> href="<?php echo admin_url_for('bulk_sms.php'); ?>" title="Bulk SMS">
                    <i class="bi bi-chat-text-fill"></i> <span class="menu-text">Bulk SMS</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('sms_campaigns.php'); ?> href="<?php echo admin_url_for('sms_campaigns.php'); ?>" title="SMS Campaigns">
                    <i class="bi bi-megaphone"></i> <span class="menu-text">SMS Campaigns</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('sms_templates.php'); ?> href="<?php echo admin_url_for('sms_templates.php'); ?>" title="SMS Templates">
                    <i class="bi bi-file-text"></i> <span class="menu-text">SMS Templates</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('sms_analytics.php'); ?> href="<?php echo admin_url_for('sms_analytics.php'); ?>" title="SMS Analytics">
                    <i class="bi bi-graph-up"></i> <span class="menu-text">SMS Analytics</span>
                </a>
            </li>

            <!-- Integrations -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="bi bi-puzzle-fill"></i> <span class="menu-text">Integrations</span>
                </div>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('calendar_integration.php'); ?> href="<?php echo admin_url_for('calendar_integration.php'); ?>" title="Calendar Integration">
                    <i class="bi bi-calendar-check"></i> <span class="menu-text">Calendar Integration</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('social_media_integration.php'); ?> href="<?php echo admin_url_for('social_media_integration.php'); ?>" title="Social Media Integration">
                    <i class="bi bi-share"></i> <span class="menu-text">Social Media</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('sms_integration.php'); ?> href="<?php echo admin_url_for('sms_integration.php'); ?>" title="SMS Integration">
                    <i class="bi bi-chat-dots"></i> <span class="menu-text">SMS Integration</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('payment_integration.php'); ?> href="<?php echo admin_url_for('payment_integration.php'); ?>" title="Payment Integration">
                    <i class="bi bi-credit-card"></i> <span class="menu-text">Payment Integration</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('payment_tables.php'); ?> href="<?php echo admin_url_for('payment_tables.php'); ?>" title="Payment Tables Setup">
                    <i class="bi bi-table"></i> <span class="menu-text">Payment Tables</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('donations.php'); ?> href="<?php echo admin_url_for('donations.php'); ?>" title="Manage Donations">
                    <i class="bi bi-cash-coin"></i> <span class="menu-text">Donations</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('check_payment_sdks.php'); ?> href="<?php echo admin_url_for('check_payment_sdks.php'); ?>" title="Check Payment SDKs">
                    <i class="bi bi-check-circle"></i> <span class="menu-text">Check Payment SDKs</span>
                </a>
            </li>
            
            <!-- Account -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="bi bi-gear-fill"></i> <span class="menu-text">Account</span>
                </div>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('settings.php'); ?> href="<?php echo admin_url_for('settings.php'); ?>" title="Settings">
                    <i class="bi bi-gear"></i> <span class="menu-text">Settings</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('appearance_settings.php'); ?> href="<?php echo admin_url_for('appearance_settings.php'); ?>" title="Appearance Settings">
                    <i class="bi bi-palette"></i> <span class="menu-text">Appearance</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('custom_fields.php'); ?> href="<?php echo admin_url_for('custom_fields.php'); ?>" title="Custom Fields">
                    <i class="bi bi-ui-checks-grid"></i> <span class="menu-text">Custom Fields</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('logo_upload.php'); ?> href="<?php echo admin_url_for('logo_upload.php'); ?>" title="Logo Management">
                    <i class="bi bi-image"></i> <span class="menu-text">Logo Upload</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('branding_settings.php'); ?> href="<?php echo admin_url_for('branding_settings.php'); ?>" title="Branding Settings">
                    <i class="bi bi-brush"></i> <span class="menu-text">Branding</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('logo_management.php'); ?> href="<?php echo admin_url_for('logo_management.php'); ?>" title="Logo Management">
                    <i class="bi bi-image"></i> <span class="menu-text">Logo Management</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('setup_security.php'); ?> href="<?php echo admin_url_for('setup_security.php'); ?>" title="Security Setup">
                    <i class="bi bi-shield-plus"></i> <span class="menu-text">Security Setup</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('security_audit.php'); ?> href="<?php echo admin_url_for('security_audit.php'); ?>" title="Security Audit">
                    <i class="bi bi-shield-lock"></i> <span class="menu-text">Security Audit</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('security_settings.php'); ?> href="<?php echo admin_url_for('security_settings.php'); ?>" title="Security Settings">
                    <i class="bi bi-shield-check"></i> <span class="menu-text">Security Settings</span>
                </a>
            </li>
            <li class="nav-item">
                <a <?php echo is_active('profile.php'); ?> href="<?php echo admin_url_for('profile.php'); ?>" title="My Profile">
                    <i class="bi bi-person-circle"></i> <span class="menu-text">My Profile</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo admin_url_for('logout.php'); ?>" title="Logout">
                    <i class="bi bi-box-arrow-right"></i> <span class="menu-text">Logout</span>
                </a>
            </li>
        </ul>
    </div>
</div>



<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const collapseToggle = document.getElementById('sidebarCollapseToggle');
    const desktopToggle = document.getElementById('sidebarToggleDesktop');
    const mainContent = document.querySelector('.main-content');
    const isMobile = window.innerWidth <= 768;

    // Mobile sidebar toggle
    if(sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.toggle('sidebar-collapsed');
        });
    }

    // Desktop sidebar toggle - Only apply for non-mobile
    if(!isMobile) {
        // Check for user preference in localStorage
        if(localStorage.getItem('sidebarCollapsed') === 'true') {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
            if(collapseToggle && collapseToggle.querySelector('i')) {
                collapseToggle.querySelector('i').classList.remove('bi-chevron-left');
                collapseToggle.querySelector('i').classList.add('bi-chevron-right');
            }
        }

        // Enhanced desktop toggle functionality
        function toggleSidebar() {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            // Update icon for external toggle
            const externalIcon = collapseToggle ? collapseToggle.querySelector('i') : null;
            if(externalIcon) {
                if(sidebar.classList.contains('collapsed')) {
                    externalIcon.classList.remove('bi-chevron-left');
                    externalIcon.classList.add('bi-chevron-right');
                } else {
                    externalIcon.classList.remove('bi-chevron-right');
                    externalIcon.classList.add('bi-chevron-left');
                }
            }

            // Save state
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed') ? 'true' : 'false');

            // Force window resize event to update any responsive components
            window.dispatchEvent(new Event('resize'));
        }

        // External collapse toggle (existing)
        if(collapseToggle) {
            collapseToggle.addEventListener('click', toggleSidebar);
        }

        // Internal desktop toggle (new)
        if(desktopToggle) {
            desktopToggle.addEventListener('click', toggleSidebar);
        }
    }

    // Smooth scrolling for sidebar content
    const sidebarContent = document.querySelector('.sidebar-content');
    if(sidebarContent) {
        sidebarContent.style.scrollBehavior = 'smooth';
    }
});
</script>