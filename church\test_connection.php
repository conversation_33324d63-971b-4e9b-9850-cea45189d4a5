<?php
require_once 'config.php';

// Test database connection
try {
    $query = "SELECT 1";
    $stmt = $pdo->query($query);
    echo "Database connection successful!\n";
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
}

// Test file permissions
$testDirs = ['logs', 'uploads'];
foreach ($testDirs as $dir) {
    if (!is_dir($dir)) {
        echo "$dir directory doesn't exist\n";
        continue;
    }
    if (is_writable($dir)) {
        echo "$dir directory is writable\n";
    } else {
        echo "$dir directory is NOT writable\n";
    }
}

// Test PHP version and extensions
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Required extensions:\n";
$required = ['pdo', 'pdo_mysql', 'fileinfo'];
foreach ($required as $ext) {
    echo "$ext: " . (extension_loaded($ext) ? "Loaded" : "Not loaded") . "\n";
}
?>