<?php
session_start();

// Include configuration and classes
require_once 'config.php';
require_once 'classes/SecurityManager.php';
require_once 'classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Simulate user login for testing
if (!$userAuth->isAuthenticated()) {
    // Get a test user
    $stmt = $pdo->prepare("SELECT id, full_name, email FROM members WHERE status = 'active' AND password_hash IS NOT NULL LIMIT 1");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['full_name'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['is_authenticated'] = true;
    }
}

// Check if this is a POST request (simulating the RSVP request)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    echo "Raw POST data received:\n";
    echo "POST: " . print_r($_POST, true) . "\n";
    echo "FILES: " . print_r($_FILES, true) . "\n";
    echo "Session: " . print_r($_SESSION, true) . "\n";
    echo "Authenticated: " . ($userAuth->isAuthenticated() ? 'Yes' : 'No') . "\n";
    
    // Now call the actual RSVP handler
    echo "\n--- Calling RSVP Handler ---\n";
    
    // Capture the output from the RSVP handler
    ob_start();
    include 'user/rsvp_handler.php';
    $rsvp_output = ob_get_clean();
    
    echo "RSVP Handler Output: " . $rsvp_output . "\n";
    exit;
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>RSVP Form Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>RSVP Form Test</h2>
        
        <div class="alert alert-info">
            <strong>Session Info:</strong><br>
            Authenticated: <?php echo $userAuth->isAuthenticated() ? 'Yes' : 'No'; ?><br>
            <?php if ($userAuth->isAuthenticated()): ?>
                User ID: <?php echo $_SESSION['user_id']; ?><br>
                Username: <?php echo $_SESSION['username']; ?>
            <?php endif; ?>
        </div>
        
        <form id="rsvpForm">
            <input type="hidden" id="rsvp_event_id" name="event_id" value="3">
            <input type="hidden" id="rsvp_status" name="status" value="">
            
            <div class="mb-3">
                <label class="form-label">RSVP Status</label>
                <div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="rsvp_response" id="attending" value="attending">
                        <label class="form-check-label" for="attending">I will attend</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="rsvp_response" id="maybe" value="maybe" checked>
                        <label class="form-check-label" for="maybe">Maybe</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="rsvp_response" id="not_attending" value="not_attending">
                        <label class="form-check-label" for="not_attending">Cannot attend</label>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="notes" class="form-label">Notes (Optional)</label>
                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Any additional comments...">Test RSVP submission</textarea>
            </div>
            
            <button type="button" class="btn btn-primary" onclick="submitRSVP()">Submit RSVP</button>
        </form>
        
        <div id="result" class="mt-4"></div>
    </div>
    
    <script>
        function submitRSVP() {
            const form = document.getElementById('rsvpForm');
            const formData = new FormData(form);
            
            // Get selected response
            const selectedResponse = document.querySelector('input[name="rsvp_response"]:checked');
            if (!selectedResponse) {
                alert('Please select an RSVP status.');
                return;
            }
            
            formData.set('status', selectedResponse.value);
            formData.append('action', 'rsvp');
            
            // Debug: Show what we're sending
            console.log('Form data being sent:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ': ' + value);
            }
            
            fetch('test_rsvp_form.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.text())
            .then(data => {
                document.getElementById('result').innerHTML = '<h3>Response:</h3><pre>' + data + '</pre>';
            })
            .catch(error => {
                document.getElementById('result').innerHTML = '<h3>Error:</h3><pre>' + error + '</pre>';
            });
        }
    </script>
</body>
</html>
