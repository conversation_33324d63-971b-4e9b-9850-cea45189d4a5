<?php
/**
 * Setup User Preferences Table
 * Creates the user_preferences table for storing admin user preferences
 */

session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

echo "<h2>Setting up User Preferences System</h2>";
echo "<p>Creating user preferences table for enhanced UI/UX features...</p>";

try {
    // Create user_preferences table for admin users
    $sql = "CREATE TABLE IF NOT EXISTS `user_preferences` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `admin_id` int(11) NOT NULL,
        `preference_key` varchar(100) NOT NULL,
        `preference_value` text DEFAULT NULL,
        `preference_type` enum('string','integer','boolean','json') NOT NULL DEFAULT 'string',
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_admin_preference` (`admin_id`, `preference_key`),
        KEY `idx_admin_id` (`admin_id`),
        KEY `idx_preference_key` (`preference_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $pdo->exec($sql);
    echo "<span style='color: green;'>✓</span> Created user_preferences table<br>";
    
    // Add some default preferences for current admin
    $adminId = $_SESSION['admin_id'];
    
    $defaultPreferences = [
        ['theme_preference', 'auto', 'string'],
        ['language_preference', 'en', 'string'],
        ['timezone_preference', 'America/New_York', 'string'],
        ['dashboard_layout', 'default', 'string'],
        ['sidebar_collapsed', '0', 'boolean'],
        ['items_per_page', '25', 'integer'],
        ['accessibility_mode', '0', 'boolean'],
        ['high_contrast', '0', 'boolean'],
        ['reduced_motion', '0', 'boolean'],
        ['font_size', 'normal', 'string']
    ];
    
    $insertCount = 0;
    foreach ($defaultPreferences as $pref) {
        list($key, $value, $type) = $pref;
        
        // Check if preference already exists
        $stmt = $pdo->prepare("SELECT id FROM user_preferences WHERE admin_id = ? AND preference_key = ?");
        $stmt->execute([$adminId, $key]);
        
        if (!$stmt->fetch()) {
            // Insert default preference
            $stmt = $pdo->prepare("INSERT INTO user_preferences (admin_id, preference_key, preference_value, preference_type) VALUES (?, ?, ?, ?)");
            $stmt->execute([$adminId, $key, $value, $type]);
            $insertCount++;
        }
    }
    
    echo "<span style='color: green;'>✓</span> Added " . $insertCount . " default preferences<br>";
    
    // Test the table
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM user_preferences WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    $total = $stmt->fetch()['total'];
    
    echo "<span style='color: green;'>✓</span> Total preferences for current admin: " . $total . "<br>";
    
    // Show current preferences
    echo "<h3>Current User Preferences:</h3>";
    $stmt = $pdo->prepare("SELECT preference_key, preference_value, preference_type, created_at FROM user_preferences WHERE admin_id = ? ORDER BY preference_key");
    $stmt->execute([$adminId]);
    $preferences = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($preferences)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Key</th><th>Value</th><th>Type</th><th>Created</th></tr>";
        foreach ($preferences as $pref) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($pref['preference_key']) . "</td>";
            echo "<td>" . htmlspecialchars($pref['preference_value']) . "</td>";
            echo "<td>" . htmlspecialchars($pref['preference_type']) . "</td>";
            echo "<td>" . htmlspecialchars($pref['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>✅ User Preferences System Setup Complete!</h3>";
    echo "<p><strong>Features enabled:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Dark/Light mode preferences</li>";
    echo "<li>✅ Language preferences</li>";
    echo "<li>✅ Timezone preferences</li>";
    echo "<li>✅ Dashboard layout preferences</li>";
    echo "<li>✅ Accessibility preferences</li>";
    echo "<li>✅ UI customization preferences</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>✗</span> Error: " . $e->getMessage() . "<br>";
}

echo "<br><br>";
echo "<a href='dashboard.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Go to Dashboard</a> ";
echo "<a href='appearance_settings.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>Appearance Settings</a>";
?>
