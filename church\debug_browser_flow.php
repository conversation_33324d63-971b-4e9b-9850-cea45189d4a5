<?php
session_start();

// Include configuration and classes
require_once 'config.php';
require_once 'classes/SecurityManager.php';
require_once 'classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Browser Flow</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Debug Browser Flow</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Current Session Status</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Session ID:</strong> <?php echo session_id(); ?></p>
                        <p><strong>Authenticated:</strong> <?php echo $userAuth->isAuthenticated() ? 'Yes' : 'No'; ?></p>
                        <?php if ($userAuth->isAuthenticated()): ?>
                            <p><strong>User ID:</strong> <?php echo $_SESSION['user_id'] ?? 'Not set'; ?></p>
                            <p><strong>Username:</strong> <?php echo $_SESSION['username'] ?? 'Not set'; ?></p>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <strong>Not authenticated!</strong> This simulates what users see when they're not logged in.
                            </div>
                        <?php endif; ?>
                        
                        <h6>Full Session Data:</h6>
                        <pre><?php print_r($_SESSION); ?></pre>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test RSVP Request</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testRSVP()">Test RSVP (like events page)</button>
                        <button class="btn btn-secondary" onclick="testAuth()">Test Auth Status</button>
                        <button class="btn btn-success" onclick="simulateLogin()">Simulate Login</button>
                        
                        <div id="result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Browser Console Logs</h5>
                    </div>
                    <div class="card-body">
                        <div id="console-logs"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let logContainer = document.getElementById('console-logs');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `<div class="text-muted">[${timestamp}] ${message}</div>`;
        }
        
        function testAuth() {
            log('Testing authentication status...');
            
            fetch('user/rsvp_handler.php', {
                method: 'POST',
                body: new FormData(),
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                log('Auth test response: ' + JSON.stringify(data));
                document.getElementById('result').innerHTML = '<div class="alert alert-info"><strong>Auth Test:</strong><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
            })
            .catch(error => {
                log('Auth test error: ' + error);
                document.getElementById('result').innerHTML = '<div class="alert alert-danger"><strong>Auth Test Error:</strong> ' + error + '</div>';
            });
        }
        
        function testRSVP() {
            log('Testing RSVP submission (simulating events page behavior)...');
            
            const formData = new FormData();
            formData.append('event_id', '3');
            formData.append('status', 'maybe');
            formData.append('action', 'rsvp');
            formData.append('notes', 'Test from debug page');
            
            log('Sending RSVP data: event_id=3, status=maybe, action=rsvp');
            
            fetch('user/rsvp_handler.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                log('RSVP response status: ' + response.status);
                return response.json();
            })
            .then(data => {
                log('RSVP response data: ' + JSON.stringify(data));
                if (data.success) {
                    document.getElementById('result').innerHTML = '<div class="alert alert-success"><strong>RSVP Success:</strong><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                } else {
                    document.getElementById('result').innerHTML = '<div class="alert alert-warning"><strong>RSVP Failed:</strong><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                }
            })
            .catch(error => {
                log('RSVP error: ' + error);
                document.getElementById('result').innerHTML = '<div class="alert alert-danger"><strong>RSVP Error:</strong> ' + error + '</div>';
            });
        }
        
        function simulateLogin() {
            log('Simulating user login...');
            
            fetch('test_rsvp_with_auth.php')
            .then(response => response.text())
            .then(data => {
                log('Login simulation completed');
                location.reload(); // Reload to see updated session
            })
            .catch(error => {
                log('Login simulation error: ' + error);
            });
        }
        
        // Log initial page load
        log('Page loaded. Session authenticated: <?php echo $userAuth->isAuthenticated() ? "true" : "false"; ?>');
    </script>
</body>
</html>
