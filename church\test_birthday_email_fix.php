<?php
// Set error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Include configuration
require_once 'config.php';
require_once 'send_birthday_reminders.php';

// Initialize BirthdayReminder
$birthdayReminder = new BirthdayReminder($pdo);

// Check if template was specified
$template_id = isset($_GET['template_id']) ? intval($_GET['template_id']) : null;
$test_email = isset($_GET['email']) ? $_GET['email'] : '<EMAIL>';

// If template ID is not specified, list all birthday templates
if (!$template_id) {
    echo "<!DOCTYPE html>
    <html>
    <head>
        <title>Test Birthday Email Fix</title>
        <meta charset='utf-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1'>
        <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            h1 { color: #333; }
            table { border-collapse: collapse; width: 100%; }
            th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
            th { background-color: #f2f2f2; }
            tr:hover { background-color: #f5f5f5; }
            .btn { display: inline-block; padding: 8px 16px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px; }
            .btn:hover { background-color: #45a049; }
            .form-group { margin-bottom: 15px; }
            label { font-weight: bold; }
            input[type=text], input[type=email] { width: 100%; padding: 8px; box-sizing: border-box; }
        </style>
    </head>
    <body>
        <h1>Test Birthday Email Fix</h1>
        <p>Select a birthday template to test:</p>";
    
    try {
        $stmt = $pdo->query("SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1");
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($templates) > 0) {
            echo "<table>
                <tr>
                    <th>ID</th>
                    <th>Template Name</th>
                    <th>Subject</th>
                    <th>Action</th>
                </tr>";
            
            foreach ($templates as $template) {
                echo "<tr>
                    <td>{$template['id']}</td>
                    <td>{$template['template_name']}</td>
                    <td>{$template['subject']}</td>
                    <td><a href='?template_id={$template['id']}' class='btn'>Select</a></td>
                </tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>No birthday templates found.</p>";
        }
    } catch (PDOException $e) {
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
    
    echo "</body></html>";
    exit;
}

// Get the selected template
try {
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
    $stmt->execute([$template_id]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        die("Template not found");
    }
    
    // Show template info and form to send test email
    echo "<!DOCTYPE html>
    <html>
    <head>
        <title>Send Test Birthday Email</title>
        <meta charset='utf-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1'>
        <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            h1, h2 { color: #333; }
            .template-info { background-color: #f9f9f9; padding: 15px; margin-bottom: 20px; border-radius: 4px; }
            .form-group { margin-bottom: 15px; }
            label { font-weight: bold; display: block; margin-bottom: 5px; }
            input[type=text], input[type=email] { width: 100%; padding: 8px; box-sizing: border-box; }
            .btn { display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }
            .btn:hover { background-color: #45a049; }
            .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
            .success { background-color: #dff0d8; color: #3c763d; }
            .error { background-color: #f2dede; color: #a94442; }
        </style>
    </head>
    <body>
        <h1>Send Test Birthday Email</h1>
        
        <div class='template-info'>
            <h2>Template Information:</h2>
            <p><strong>ID:</strong> {$template['id']}</p>
            <p><strong>Name:</strong> {$template['template_name']}</p>
            <p><strong>Subject:</strong> {$template['subject']}</p>
        </div>";
    
    // Process form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $full_name = $_POST['full_name'] ?? 'Test Member';
        $email = $_POST['email'] ?? '<EMAIL>';
        $birth_date = $_POST['birth_date'] ?? '1990-01-01';
        
        // Create test member data
        $testMember = [
            'id' => 999,
            'full_name' => $full_name,
            'first_name' => explode(' ', $full_name)[0],
            'last_name' => explode(' ', $full_name, 2)[1] ?? '',
            'email' => $email,
            'birth_date' => $birth_date,
        ];
        
        // Use original sending method to test the fix
        $result = $birthdayReminder->sendBirthdayEmails($template['id'], 0, true, [$testMember]);
        
        if ($result && isset($result['total_sent']) && $result['total_sent'] > 0) {
            echo "<div class='result success'>
                <h3>Success!</h3>
                <p>Test email sent successfully to {$email}.</p>
                <p>Check your inbox to verify the fix is working properly. The subject line should show:<br>
                <strong>" . str_replace('{full_name}', $full_name, $template['subject']) . "</strong></p>
                <p><a href='?' class='btn'>Test Another Template</a></p>
            </div>";
        } else {
            $error = isset($result['error']) ? $result['error'] : 'Unknown error occurred';
            echo "<div class='result error'>
                <h3>Error</h3>
                <p>Failed to send test email: {$error}</p>
            </div>";
        }
    }
    
    // Display form
    echo "<form method='post' action='?template_id={$template_id}'>
            <div class='form-group'>
                <label for='full_name'>Full Name:</label>
                <input type='text' id='full_name' name='full_name' value='Test Member' required>
            </div>
            
            <div class='form-group'>
                <label for='email'>Email Address:</label>
                <input type='email' id='email' name='email' value='{$test_email}' required>
            </div>
            
            <div class='form-group'>
                <label for='birth_date'>Birth Date:</label>
                <input type='text' id='birth_date' name='birth_date' value='1990-01-01' placeholder='YYYY-MM-DD' required>
            </div>
            
            <button type='submit' class='btn'>Send Test Email</button>
            <a href='?' style='margin-left: 10px;'>Back to Template List</a>
        </form>
    </body>
    </html>";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
} 