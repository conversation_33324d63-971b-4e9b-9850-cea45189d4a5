<?php
require_once 'config.php';

// Get donation ID
$donation_id = filter_var($_GET['id'] ?? null, FILTER_VALIDATE_INT);
if (!$donation_id) {
    header("Location: donate.php");
    exit();
}

// Get donation details
try {
    $stmt = $pdo->prepare("SELECT d.*, m.full_name as recipient_name 
                          FROM donations d 
                          LEFT JOIN members m ON d.recipient_id = m.id 
                          WHERE d.id = ? AND d.payment_status = 'completed'");
    $stmt->execute([$donation_id]);
    $donation = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$donation) {
        header("Location: donate.php");
        exit();
    }
    
    // Get payment settings for success message
    $stmt = $pdo->prepare("SELECT * FROM payment_settings");
    $stmt->execute();
    $payment_settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $payment_settings[$row['setting_key']] = $row['setting_value'];
    }
    
} catch (PDOException $e) {
    error_log("Database Error: " . $e->getMessage());
    header("Location: donate.php");
    exit();
}

// Currency options
$currencies = [
    'USD' => ['name' => 'US Dollar', 'symbol' => '$'],
    'EUR' => ['name' => 'Euro', 'symbol' => '€'],
    'GBP' => ['name' => 'British Pound', 'symbol' => '£'],
    'ZAR' => ['name' => 'South African Rand', 'symbol' => 'R'],
    'NGN' => ['name' => 'Nigerian Naira', 'symbol' => '₦'],
    'KES' => ['name' => 'Kenyan Shilling', 'symbol' => 'KSh'],
    'UGX' => ['name' => 'Ugandan Shilling', 'symbol' => 'USh'],
    'GHS' => ['name' => 'Ghanaian Cedi', 'symbol' => 'GH₵']
];

$currency_symbol = $currencies[$donation['currency']]['symbol'] ?? '';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Donation Successful - Freedom Assembly Church</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .success-icon {
            font-size: 4rem;
            color: #198754;
        }
        .donation-details {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <?php include 'navbar.php'; ?>
    
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-md-8 text-center">
                <i class="bi bi-check-circle-fill success-icon mb-4"></i>
                <h1 class="mb-4">Thank You for Your Donation!</h1>
                
                <?php if (isset($payment_settings['donation_success_message'])): ?>
                    <p class="lead mb-5"><?php echo htmlspecialchars($payment_settings['donation_success_message']); ?></p>
                <?php endif; ?>
                
                <div class="donation-details text-start mb-5">
                    <h4 class="mb-4">Donation Details</h4>
                    <div class="row">
                        <div class="col-sm-6">
                            <p><strong>Amount:</strong><br>
                            <?php echo $currency_symbol . number_format($donation['amount'], 2); ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p><strong>Date:</strong><br>
                            <?php echo date('F j, Y', strtotime($donation['created_at'])); ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p><strong>Type:</strong><br>
                            <?php echo ucfirst(str_replace('_', ' ', $donation['donation_type'])); ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p><strong>Payment Method:</strong><br>
                            <?php echo ucfirst($donation['payment_method']); ?></p>
                        </div>
                        <?php if ($donation['donation_type'] === 'birthday_gift' && $donation['recipient_name']): ?>
                        <div class="col-12">
                            <p><strong>Gift Recipient:</strong><br>
                            <?php echo htmlspecialchars($donation['recipient_name']); ?></p>
                        </div>
                        <?php endif; ?>
                        <?php if ($donation['message']): ?>
                        <div class="col-12">
                            <p><strong>Message:</strong><br>
                            <?php echo nl2br(htmlspecialchars($donation['message'])); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <p>A confirmation email has been sent to <?php echo htmlspecialchars($donation['donor_email']); ?></p>
                
                <div class="mt-4">
                    <a href="register.php" class="btn btn-primary me-3">Return to Homepage</a>
                    <a href="donate.php" class="btn btn-outline-primary">Make Another Donation</a>
                </div>
            </div>
        </div>
    </div>
    
    <?php include 'footer.php'; ?>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 