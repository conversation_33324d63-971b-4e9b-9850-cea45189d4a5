<?php
require_once __DIR__ . '/config.php';

// Check database structure
echo "<h2>Events Database Structure Check</h2>";

try {
    // Check events table structure
    echo "<h3>Events Table Structure:</h3>";
    try {
        $stmt = $pdo->query("DESCRIBE events");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<pre>";
        print_r($columns);
        echo "</pre>";
    } catch (PDOException $e) {
        echo "Error accessing events table: " . $e->getMessage() . "<br>";
    }
    
    // Check event_rsvps table structure
    echo "<h3>Event RSVPs Table Structure:</h3>";
    try {
        $stmt = $pdo->query("DESCRIBE event_rsvps");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<pre>";
        print_r($columns);
        echo "</pre>";
    } catch (PDOException $e) {
        echo "Error accessing event_rsvps table: " . $e->getMessage() . "<br>";
    }
    
    // Check if tables exist
    echo "<h3>Tables Check:</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'events'");
    $eventsExists = $stmt->rowCount() > 0;
    echo "Events table exists: " . ($eventsExists ? "YES" : "NO") . "<br>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'event_rsvps'");
    $rsvpsExists = $stmt->rowCount() > 0;
    echo "Event RSVPs table exists: " . ($rsvpsExists ? "YES" : "NO") . "<br>";
    
    // Show all tables
    echo "<h3>All Tables:</h3>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<pre>";
    print_r($tables);
    echo "</pre>";
    
    // Check for events with event_date column
    echo "<h3>Sample Events Data:</h3>";
    try {
        $stmt = $pdo->query("SELECT * FROM events LIMIT 3");
        $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<pre>";
        print_r($events);
        echo "</pre>";
    } catch (PDOException $e) {
        echo "Error accessing events data: " . $e->getMessage() . "<br>";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
