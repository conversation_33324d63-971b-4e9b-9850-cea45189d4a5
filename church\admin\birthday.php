<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include language system
require_once 'includes/language.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Get the current month
$current_month = date('m');
$current_month_name = date('F');

// Enhanced error handling for database operations
try {
    // Get members with birthdays this month
    $birthdays = [];
    $stmt = $conn->prepare("SELECT id, full_name, birth_date, email, phone_number, image_path FROM members WHERE MONTH(birth_date) = ? ORDER BY DAY(birth_date)");
    $stmt->execute([$current_month]);
    $birthdays = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add day of the month to each member
    $processed_birthdays = [];
    foreach ($birthdays as $member) {
        $member_copy = $member;
        $member_copy['day'] = date('j', strtotime($member['birth_date']));
        $processed_birthdays[] = $member_copy;
    }

    // Get birthday email templates
    $email_templates = [];
    $stmt = $conn->prepare("SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1");
    $stmt->execute();
    $email_templates = $stmt->fetchAll();
} catch (PDOException $e) {
    // Log error and display user-friendly message
    error_log("Error in birthday.php: " . $e->getMessage());
    $_SESSION['error'] = "A database error occurred while retrieving birthday information. Please try again or contact support.";
}

// Get default email template ID for quick actions
$default_email_template_id = 0;

// Try to get default email template
try {
    $stmt = $conn->prepare("SELECT id FROM email_templates WHERE is_birthday_template = 1 ORDER BY id LIMIT 1");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        $default_email_template_id = $stmt->fetchColumn();
    }
} catch (PDOException $e) {
    // Log error but continue
    error_log("Error getting default email template: " . $e->getMessage());
}

// Get birthday email templates
$email_templates = [];
$stmt = $conn->prepare("SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1");
$stmt->execute();
$email_templates = $stmt->fetchAll();

// Get WhatsApp templates for birthdays
$whatsapp_templates = [];
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'whatsapp_templates'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->prepare("SELECT id, template_name, message_content FROM whatsapp_templates WHERE is_birthday = 1");
        $stmt->execute();
        $whatsapp_templates = $stmt->fetchAll();
    }
} catch (PDOException $e) {
    // Table doesn't exist yet, ignore
}

// Get WhatsApp settings
$whatsapp_settings = [
    'sender_number' => '',
    'sender_name' => 'Freedom Assembly Church'
];

try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'whatsapp_settings'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->prepare("SELECT sender_number, sender_name FROM whatsapp_settings WHERE id = 1");
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $settings = $stmt->fetch();
            $whatsapp_settings['sender_number'] = $settings['sender_number'];
            $whatsapp_settings['sender_name'] = $settings['sender_name'];
        }
    }
} catch (PDOException $e) {
    // Table doesn't exist yet, ignore
}

// Close connection
$conn = null;

// Set page variables
$page_title = __('birthday_management');
$page_header = __('birthday_management');
$page_description = __('manage_birthday_communications_description');

// Include header
include 'includes/header.php';

// Display success message if it exists in the session
if (isset($_SESSION['message']) && !empty($_SESSION['message'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($_SESSION['message']) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
    // Clear the message after displaying it
    unset($_SESSION['message']);
}

// Display error message if it exists in the session
if (isset($_SESSION['error']) && !empty($_SESSION['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($_SESSION['error']) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
    // Clear the error after displaying it
    unset($_SESSION['error']);
}
?>

<!-- Instructions panel -->
<div class="alert alert-info mb-4 instruction-panel">
    <div class="d-flex justify-content-between align-items-start">
        <h5><i class="bi bi-info-circle-fill me-2"></i><?php _e('birthday_management_guide'); ?></h5>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <p class="mb-2">This page helps you manage birthday communications for your church members.</p>
    <ul class="mb-0">
        <li><strong>Current Month:</strong> Shows members with birthdays in the current month</li>
        <li><strong>Upcoming Birthdays:</strong> Shows members with birthdays in the next 30 days</li>
        <li><strong>Send Birthday Wishes:</strong> Send personalized birthday messages via email</li>
        <li><strong>WhatsApp Messages:</strong> Send birthday greetings via WhatsApp</li>
    </ul>
    <div class="mt-2">
        <strong>Tip:</strong> You can customize birthday email templates in the Email Templates section.
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo sprintf(__('birthday_celebrants_month'), $current_month_name); ?></h5>
                <div>
                    <a href="whatsapp_templates.php" class="btn btn-success btn-sm me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Create and manage WhatsApp message templates">
                        <i class="bi bi-whatsapp"></i> Manage Templates
                    </a>
                    <a href="whatsapp_messages.php" class="btn btn-success btn-sm me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="View sent WhatsApp messages and analytics">
                        <i class="bi bi-whatsapp"></i> WhatsApp Messages
                    </a>
                    <a href="send_birthday_emails.php" class="btn btn-primary btn-sm">
                        <i class="bi bi-envelope"></i> Send Birthday Emails
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (count($processed_birthdays) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Photo</th>
                                <th>Name</th>
                                <th>Birth Date</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            // Loop through all birthday members
                            foreach ($processed_birthdays as $member): 
                            ?>
                            <tr>
                                <td>
                                    <?php if (!empty($member['image_path'])): ?>
                                        <img src="../<?php echo htmlspecialchars($member['image_path']); ?>" 
                                             alt="<?php echo htmlspecialchars($member['full_name']); ?>" 
                                             class="rounded-circle member-photo"
                                             style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <img src="../assets/img/default-profile.jpg" 
                                             alt="Default Profile" 
                                             class="rounded-circle member-photo"
                                             style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($member['full_name']); ?></td>
                                <td><?php echo date('d M', strtotime($member['birth_date'])); ?></td>
                                <td><?php echo htmlspecialchars($member['email']); ?></td>
                                <td><?php echo htmlspecialchars($member['phone_number'] ?? 'N/A'); ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-primary send-birthday-btn" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#sendBirthdayModal" 
                                                data-id="<?php echo $member['id']; ?>" 
                                                data-name="<?php echo htmlspecialchars($member['full_name']); ?>" 
                                                data-email="<?php echo htmlspecialchars($member['email']); ?>">
                                            <i class="bi bi-envelope"></i> Email
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <p class="text-muted">No birthday celebrants for <?php echo $current_month_name; ?>.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Send Birthday Modal -->
<div class="modal fade" id="sendBirthdayModal" tabindex="-1" aria-labelledby="sendBirthdayModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendBirthdayModalLabel"><?php _e('send_birthday_message'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Modal Instructions -->
                <div class="alert alert-info mb-4 instruction-panel">
                    <div class="d-flex justify-content-between align-items-start">
                        <h5><i class="bi bi-info-circle-fill me-2"></i>Sending a Birthday Message</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <p class="mb-0">Select a birthday template to send a personalized birthday message to this member. The template will automatically include the member's name and other details.</p>
                </div>
                
                <form id="sendBirthdayForm" action="send_birthday_message_fixed.php" method="post">
                    <input type="hidden" name="member_id" id="member_id">
                    
                    <div class="mb-3">
                        <label for="recipient_name" class="form-label">Recipient Name</label>
                        <input type="text" class="form-control" id="recipient_name" name="recipient_name" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="recipient_email" class="form-label">Recipient Email</label>
                        <input type="email" class="form-control" id="recipient_email" name="recipient_email" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="template_id" class="form-label">Email Template</label>
                        <select class="form-select" id="template_id" name="template_id" required data-bs-toggle="tooltip" data-bs-placement="top" title="Choose a birthday email template. The template will be personalized with the member's information.">
                            <option value="">Select a template</option>
                            <?php foreach ($email_templates as $template): ?>
                            <option value="<?php echo $template['id']; ?>"><?php echo htmlspecialchars($template['template_name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subject" class="form-label">Email Subject</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="subject" name="subject" required>
                            <button class="btn btn-outline-secondary" type="button" id="show_template_subject" data-bs-toggle="tooltip" title="Use Template Subject">
                                <i class="bi bi-arrow-repeat"></i>
                            </button>
                        </div>
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="use_template_subject" name="use_template_subject" value="1">
                            <label class="form-check-label" for="use_template_subject">
                                Use Template Subject (supports {full_name} and other placeholders)
                            </label>
                        </div>
                        <small class="text-muted">You can use placeholders like {full_name}, {member_name} in the subject</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="custom_message" class="form-label">Custom Message (Optional)</label>
                        <textarea class="form-control" id="custom_message" name="custom_message" rows="3"></textarea>
                        <small class="text-muted">This will be added to the template.</small>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Send Birthday Message</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Send WhatsApp Modal -->
<div class="modal fade" id="sendWhatsAppModal" tabindex="-1" aria-labelledby="sendWhatsAppModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="sendWhatsAppModalLabel"><i class="bi bi-whatsapp me-2"></i>Send WhatsApp Birthday Message</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php if (empty($whatsapp_settings['sender_number'])): ?>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>WhatsApp Sender Not Configured</strong>
                    <p class="mb-0">Please set up your WhatsApp sender number in <a href="whatsapp_templates.php" class="alert-link">WhatsApp Templates</a> before sending messages.</p>
                </div>
                <?php endif; ?>
                
                <form id="sendWhatsAppForm" action="send_whatsapp_message.php" method="post">
                    <input type="hidden" name="member_id" id="whatsapp_member_id">
                    <input type="hidden" name="recipient_phone" id="recipient_phone">
                    
                    <div class="mb-3">
                        <label for="whatsapp_recipient_name" class="form-label">Recipient Name</label>
                        <input type="text" class="form-control" id="whatsapp_recipient_name" name="recipient_name" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="whatsapp_template_id" class="form-label">WhatsApp Template</label>
                        <select class="form-select" id="whatsapp_template_id" name="template_id" required>
                            <option value="">Select a template</option>
                            <?php foreach ($whatsapp_templates as $template): ?>
                            <option value="<?php echo $template['id']; ?>" data-content="<?php echo htmlspecialchars($template['message_content']); ?>"><?php echo htmlspecialchars($template['template_name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="whatsapp_message" class="form-label">Custom Message (Optional)</label>
                        <textarea class="form-control" id="whatsapp_message" name="custom_message" rows="5" placeholder="Enter any additional personal message to add to the template..."></textarea>
                        <small class="text-muted">This will be added to the template message. Leave empty to use just the template.</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="whatsapp-preview-container p-3 bg-light rounded">
                            <h6><i class="bi bi-eye me-2"></i>Message Preview</h6>
                            <div class="whatsapp-preview mt-2">
                                <div class="preview-content" id="whatsapp_preview_content">
                                    <p class="text-muted">Select a template to see preview</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" id="sendWhatsAppSubmitBtn" class="btn btn-success" <?php echo empty($whatsapp_settings['sender_number']) ? 'disabled' : ''; ?>>
                            <i class="bi bi-whatsapp me-1"></i> Send WhatsApp Message
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    /* Member photo styling */
    .member-photo {
        transition: transform 0.2s;
    }
    .member-photo:hover {
        transform: scale(1.2);
        cursor: pointer;
    }
    .table td {
        vertical-align: middle;
    }

    /* WhatsApp styling */
    .whatsapp-preview {
        background-color: #e5ddd5;
        border-radius: 8px;
        padding: 20px;
        max-height: 200px;
        overflow-y: auto;
    }
    
    .preview-content {
        background-color: #fff;
        border-radius: 7.5px;
        padding: 10px 15px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        position: relative;
        max-width: 80%;
        margin-left: auto;
    }
    
    .preview-content:after {
        content: "";
        position: absolute;
        top: 0;
        right: -10px;
        width: 0;
        height: 0;
        border-top: 10px solid #fff;
        border-right: 10px solid transparent;
    }
    
    .btn-whatsapp {
        background-color: #25D366;
        border-color: #25D366;
        color: white;
    }
    
    .btn-whatsapp:hover {
        background-color: #128C7E;
        border-color: #128C7E;
        color: white;
    }
</style>

<?php 
// Add extra JavaScript for the page
$extra_js = '<script>
    document.addEventListener("DOMContentLoaded", function() {
        try {
            // Add error logging
            window.onerror = function(message, source, lineno, colno, error) {
                console.error("JavaScript Error:", message, "at", source, "line:", lineno, "column:", colno, "error:", error);
                return false;
            };
            
            // Store template data for easy access
            const templateData = {};';
            
            // Build template data outside of the JavaScript string
            if (!empty($email_templates)) {
                foreach ($email_templates as $t) {
                    if (isset($t['id']) && isset($t['template_name']) && isset($t['subject'])) {
                        $extra_js .= '
                        templateData["' . $t['id'] . '"] = {
                            "name": ' . json_encode($t['template_name'] ?? '') . ',
                            "subject": ' . json_encode($t['subject'] ?? '') . '
                        };';
                    }
                }
            }
            
            $extra_js .= '
            console.log("Template data loaded:", Object.keys(templateData).length, "templates");
            
            // Handle template selection change
            const templateSelect = document.getElementById("template_id");
            const subjectField = document.getElementById("subject");
            const useTemplateSubjectCheckbox = document.getElementById("use_template_subject");
            const showTemplateSubjectBtn = document.getElementById("show_template_subject");
            
            if (templateSelect && subjectField) {
                templateSelect.addEventListener("change", function() {
                    const templateId = this.value;
                    
                    // If template is selected and "use template subject" is checked,
                    // update the subject field with the template subject
                    if (templateId && useTemplateSubjectCheckbox && useTemplateSubjectCheckbox.checked) {
                        if (templateData[templateId]) {
                            subjectField.value = templateData[templateId].subject;
                        }
                    }
                });
            } else {
                console.error("Template select or subject field not found");
            }
            
            // Handle use template subject checkbox
            if (useTemplateSubjectCheckbox) {
                useTemplateSubjectCheckbox.addEventListener("change", function() {
                    try {
                        const templateId = templateSelect.value;
                        
                        if (this.checked && templateId && templateData[templateId]) {
                            // Save the current manual subject in a data attribute
                            if (!subjectField.dataset.manualSubject) {
                                subjectField.dataset.manualSubject = subjectField.value;
                            }
                            
                            // Set the template subject
                            subjectField.value = templateData[templateId].subject;
                            subjectField.readOnly = true;
                        } else {
                            // Restore manual subject if available
                            if (subjectField.dataset.manualSubject) {
                                subjectField.value = subjectField.dataset.manualSubject;
                            }
                            subjectField.readOnly = false;
                        }
                    } catch (error) {
                        console.error("Error in template subject checkbox handler:", error);
                    }
                });
            }
            
            // Handle show template subject button
            if (showTemplateSubjectBtn) {
                showTemplateSubjectBtn.addEventListener("click", function() {
                    try {
                        const templateId = templateSelect.value;
                        
                        if (templateId && templateData[templateId]) {
                            // Save the current subject in a data attribute if not using template subject
                            if (!useTemplateSubjectCheckbox.checked) {
                                subjectField.dataset.manualSubject = subjectField.value;
                            }
                            
                            // Toggle between manual and template subject
                            if (subjectField.value === templateData[templateId].subject) {
                                // Switch back to manual subject
                                if (subjectField.dataset.manualSubject) {
                                    subjectField.value = subjectField.dataset.manualSubject;
                                }
                                useTemplateSubjectCheckbox.checked = false;
                                subjectField.readOnly = false;
                            } else {
                                // Switch to template subject
                                subjectField.value = templateData[templateId].subject;
                                useTemplateSubjectCheckbox.checked = true;
                                subjectField.readOnly = true;
                            }
                        }
                    } catch (error) {
                        console.error("Error in template subject button handler:", error);
                    }
                });
            }
            
            // Handle birthday button click
            const sendBirthdayBtns = document.querySelectorAll(".send-birthday-btn");
            sendBirthdayBtns.forEach(btn => {
                btn.addEventListener("click", function() {
                    try {
                        const id = this.getAttribute("data-id");
                        const name = this.getAttribute("data-name");
                        const email = this.getAttribute("data-email");
                        
                        console.log("Button clicked for:", id, name, email);
                        
                        document.getElementById("member_id").value = id;
                        document.getElementById("recipient_name").value = name;
                        document.getElementById("recipient_email").value = email;
                        
                        // Set default birthday subject
                        const firstName = name.split(" ")[0];
                        const defaultSubject = "Happy Birthday, " + firstName + "!";
                        document.getElementById("subject").value = defaultSubject;
                        document.getElementById("subject").dataset.manualSubject = defaultSubject;
                        
                        // Reset form fields
                        document.getElementById("template_id").value = "";
                        document.getElementById("custom_message").value = "";
                        document.getElementById("use_template_subject").checked = false;
                        document.getElementById("subject").readOnly = false;
                        
                    } catch (e) {
                        console.error("Error in button click handler:", e);
                        alert("Error: " + e.message);
                    }
                });
            });

            // Ensure form submission works properly
            const birthdayForm = document.getElementById("sendBirthdayForm");
            if (birthdayForm) {
                birthdayForm.addEventListener("submit", function(e) {
                    try {
                        console.log("Form submission started");
                        
                        // Prevent default only to validate
                        e.preventDefault();
                        
                        // Log form data
                        const formData = new FormData(birthdayForm);
                        console.log("Form data:");
                        for (let pair of formData.entries()) {
                            console.log(pair[0] + ": " + pair[1]);
                        }
                        
                        // Validate form
                        if (!birthdayForm.checkValidity()) {
                            console.log("Form validation failed");
                            e.stopPropagation();
                            birthdayForm.classList.add("was-validated");
                            return;
                        }
                        
                        console.log("Form is valid, submitting...");
                        
                        // Show sending indicator
                        const submitBtn = birthdayForm.querySelector("button[type=\'submit\']");
                        const originalBtnText = submitBtn.innerHTML;
                        submitBtn.innerHTML = \'<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...\';
                        submitBtn.disabled = true;
                        
                        // Create a temporary success message
                        const recipientName = document.getElementById("recipient_name").value;
                        const tempMessage = document.createElement("div");
                        tempMessage.className = "alert alert-info mt-3";
                        tempMessage.innerHTML = \'<i class="bi bi-info-circle-fill me-2"></i>Sending birthday message to \' + recipientName + "...";
                        birthdayForm.appendChild(tempMessage);
                        
                        // Submit the form
                        birthdayForm.submit();
                    } catch (e) {
                        console.error("Error in form submission:", e);
                        alert("Error: " + e.message);
                    }
                });
            }
            
        } catch (error) {
            console.error("Error in DOMContentLoaded:", error);
            alert("Error: " + error.message);
        }
    });
</script>';

// Include footer
include 'includes/footer.php';
?> 