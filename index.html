<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Freedom Assembly Church International - Men's Visionaire</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Load static theme CSS to prevent blue flash -->
    <link rel="stylesheet" href="church/admin/css/custom-theme.css">

    <!-- Fallback CSS for default theme if custom theme file doesn't exist -->
    <style>
        :root {
            --bs-primary: #495057;
            --bs-secondary: #6c757d;
            --bs-font-sans-serif: 'Inter', system-ui, -apple-system, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%) !important;
        }

        #hero {
            background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%) !important;
        }

        body {
            font-family: var(--bs-font-sans-serif) !important;
        }
    </style>

    <!-- Enhanced Visual Styles -->
    <style>
        /* Enhanced Navigation Styles */
        .nav-link-enhanced {
            display: flex !important;
            align-items: center;
            gap: 8px;
            padding: 12px 16px !important;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .nav-link-enhanced:hover::before {
            left: 100%;
        }

        .nav-link-enhanced:hover {
            background-color: rgba(255,255,255,0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .nav-link-enhanced i {
            font-size: 1.1rem;
            transition: transform 0.3s ease;
        }

        .nav-link-enhanced:hover i {
            transform: scale(1.2);
        }

        /* Enhanced Button Styles */
        .btn-enhanced {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border-radius: 25px !important;
            padding: 12px 24px !important;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-enhanced::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s ease, height 0.6s ease;
        }

        .btn-enhanced:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn-enhanced:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        /* Floating Animation for Decorative Elements */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Hero Section */
        .hero-section {
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
            animation: pulse 4s ease-in-out infinite;
        }

        /* Floating Decorative Elements */
        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
            pointer-events: none;
        }

        .floating-element:nth-child(1) {
            top: 10%;
            right: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            bottom: 20%;
            left: 5%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            top: 60%;
            right: 20%;
            animation-delay: 4s;
        }

        /* Enhanced Card Hover Effects */
        .card {
            transition: all 0.3s ease;
            border: none !important;
            overflow: hidden;
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.8s ease;
        }

        .card:hover::before {
            left: 100%;
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }

        /* Icon Enhancement */
        .card .bi {
            transition: all 0.3s ease;
        }

        .card:hover .bi {
            transform: scale(1.1) rotate(5deg);
        }

        /* Smooth Scroll Enhancement */
        html {
            scroll-behavior: smooth;
        }

        /* Section Entrance Animation */
        .section-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .section-animate.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced Social Media Buttons */
        .btn-outline-light.rounded-circle {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-outline-light.rounded-circle::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.4s ease, height 0.4s ease;
        }

        .btn-outline-light.rounded-circle:hover::before {
            width: 100px;
            height: 100px;
        }

        .btn-outline-light.rounded-circle:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        /* Navbar Enhancement */
        .navbar {
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(0,0,0,0.9) !important;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background: linear-gradient(135deg, var(--primary-color, var(--bs-primary, #007bff)) 0%, var(--secondary-color, var(--bs-secondary, #6c757d)) 100%);">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="#" id="navbar-brand">
                <img id="navbar-logo" src="" alt="Logo" class="me-2" style="height: 40px; display: none;">
                <span id="navbar-title">Freedom Assembly Church</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link nav-link-enhanced" href="#hero">
                            <i class="bi bi-house-door"></i>
                            <span>Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-enhanced" href="#mission">
                            <i class="bi bi-bullseye"></i>
                            <span>Mission</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-enhanced" href="#about">
                            <i class="bi bi-info-circle"></i>
                            <span>About</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-enhanced" href="#events">
                            <i class="bi bi-calendar-event"></i>
                            <span>Events</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-enhanced" href="#contact">
                            <i class="bi bi-envelope"></i>
                            <span>Contact</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-light btn-enhanced ms-2" href="church/user/login.php">
                            <i class="bi bi-box-arrow-in-right me-2"></i>Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-light btn-enhanced ms-2" href="church/index.php">
                            <i class="bi bi-people me-2"></i>Join Us
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="hero-section" style="background: linear-gradient(135deg, var(--primary-color, var(--bs-primary, #007bff)) 0%, var(--secondary-color, var(--bs-secondary, #6c757d)) 100%); min-height: 100vh; display: flex; align-items: center; color: white; position: relative; overflow: hidden;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-3 fw-bold mb-4" style="font-family: var(--font-family, var(--bs-font-sans-serif, 'Inter', sans-serif));">
                        Welcome to Freedom Assembly Church International
                    </h1>
                    <h2 class="h3 mb-4" style="opacity: 0.9;">Men's Visionaire and Billionaire Platform</h2>
                    <p class="lead mb-5" style="opacity: 0.8;">
                        Vision: Raising Men To Stand Tall Within The Family And Community.
                    </p>
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="church/index.php" class="btn btn-light btn-lg px-4 py-3">
                            <i class="bi bi-people-fill me-2"></i>Join Our Brotherhood
                        </a>
                        <a href="#about" class="btn btn-outline-light btn-lg px-4 py-3">
                            <i class="bi bi-info-circle me-2"></i>Learn More
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="hero-image-placeholder" style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 3rem; margin-top: 2rem;">
                        <i class="bi bi-church" style="font-size: 8rem; opacity: 0.7;"></i>
                        <p class="mt-3" style="opacity: 0.8;">Building Strong Christian Leaders</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Enhanced Floating Decorative Elements -->
        <div class="floating-element" style="top: 10%; right: 10%; font-size: 8rem;">
            <i class="bi bi-star-fill"></i>
        </div>
        <div class="floating-element" style="bottom: 20%; left: 5%; font-size: 6rem;">
            <i class="bi bi-heart-fill"></i>
        </div>
        <div class="floating-element" style="top: 30%; left: 15%; font-size: 4rem;">
            <i class="bi bi-award"></i>
        </div>
        <div class="floating-element" style="bottom: 40%; right: 25%; font-size: 5rem;">
            <i class="bi bi-gem"></i>
        </div>
        <div class="floating-element" style="top: 70%; right: 5%; font-size: 3rem;">
            <i class="bi bi-lightning"></i>
        </div>
    </section>

    <!-- Mission Section -->
    <section id="mission" class="py-5 section-animate" style="background-color: #f8f9fa;">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold mb-4" style="color: #212529; font-family: var(--font-family, 'Inter', sans-serif);">Our Mission</h2>
                    <p class="lead" style="color: #6c757d;">
                        Empowering men to become godly leaders in their families, church, and communities through spiritual growth, leadership development, and financial empowerment.
                    </p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm" style="background: white; border-radius: 15px;">
                        <div class="card-body text-center p-4">
                            <div class="mb-4">
                                <i class="bi bi-book" style="font-size: 3rem; color: var(--primary-color, var(--bs-primary, #007bff));"></i>
                            </div>
                            <h4 class="card-title mb-3" style="color: #212529; font-family: var(--font-family, 'Inter', sans-serif);">Spiritual Growth</h4>
                            <p class="card-text" style="color: #6c757d;">
                                Empowering men through spiritual development and biblical teachings that transform lives and strengthen faith.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm" style="background: white; border-radius: 15px;">
                        <div class="card-body text-center p-4">
                            <div class="mb-4">
                                <i class="bi bi-people" style="font-size: 3rem; color: var(--primary-color, var(--bs-primary, #007bff));"></i>
                            </div>
                            <h4 class="card-title mb-3" style="color: #212529; font-family: var(--font-family, 'Inter', sans-serif);">Leadership Development</h4>
                            <p class="card-text" style="color: #6c757d;">
                                Building strong male leaders for families, church, and community through mentorship and practical training.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm" style="background: white; border-radius: 15px;">
                        <div class="card-body text-center p-4">
                            <div class="mb-4">
                                <i class="bi bi-graph-up-arrow" style="font-size: 3rem; color: var(--primary-color, var(--bs-primary, #007bff));"></i>
                            </div>
                            <h4 class="card-title mb-3" style="color: #212529; font-family: var(--font-family, 'Inter', sans-serif);">Financial Empowerment</h4>
                            <p class="card-text" style="color: #6c757d;">
                                Equipping men with skills and knowledge to achieve financial prosperity and stewardship excellence.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5 section-animate" style="background: linear-gradient(135deg, var(--bs-primary, #007bff) 0%, var(--bs-secondary, #6c757d) 100%); color: white;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="display-5 fw-bold mb-4">About Our Ministry</h2>
                    <p class="lead mb-4" style="opacity: 0.9;" id="about-description">
                        The Men's Visionaire and Billionaire platform at Freedom Assembly Church International is dedicated to raising godly men who stand tall in their families and communities.
                    </p>
                    <p class="mb-4" style="opacity: 0.8;">
                        We focus on spiritual growth, leadership development, and financial empowerment to help men fulfill their God-given purpose and potential. Our ministry provides a supportive brotherhood where men can grow together in faith, wisdom, and prosperity.
                    </p>
                    <div class="d-flex gap-4 mb-4">
                        <div class="text-center">
                            <h3 class="fw-bold">500+</h3>
                            <p class="mb-0" style="opacity: 0.8;">Members</p>
                        </div>
                        <div class="text-center">
                            <h3 class="fw-bold">10+</h3>
                            <p class="mb-0" style="opacity: 0.8;">Years</p>
                        </div>
                        <div class="text-center">
                            <h3 class="fw-bold">50+</h3>
                            <p class="mb-0" style="opacity: 0.8;">Leaders</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <div style="background: rgba(255,255,255,0.1); border-radius: var(--bs-border-radius, 15px); padding: 3rem;">
                            <i class="bi bi-award" style="font-size: 6rem; opacity: 0.8;"></i>
                            <h4 class="mt-3">Excellence in Ministry</h4>
                            <p style="opacity: 0.8;">Committed to developing men of character, integrity, and purpose.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Events Section -->
    <section id="events" class="py-5 section-animate" style="background-color: var(--bs-body-bg, #f8f9fa);">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold mb-4" style="color: var(--bs-body-color, #212529);">Upcoming Events</h2>
                    <p class="lead" style="color: var(--bs-body-color, #6c757d);">
                        Join us for inspiring events, workshops, and gatherings designed to strengthen our brotherhood and faith.
                    </p>
                </div>
            </div>
            <div class="row" id="events-container">
                <div class="col-12 text-center">
                    <div class="spinner-border" role="status" style="color: var(--bs-primary, #007bff);">
                        <span class="visually-hidden">Loading events...</span>
                    </div>
                    <p class="mt-3" style="color: var(--bs-body-color, #6c757d);">Loading upcoming events...</p>
                </div>
            </div>
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <a href="church/user/events.php" class="btn btn-primary btn-lg">
                        <i class="bi bi-calendar-event me-2"></i>View All Events
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5 section-animate" style="background: linear-gradient(135deg, var(--bs-primary, #007bff) 0%, var(--bs-secondary, #6c757d) 100%); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-5 fw-bold mb-4">Connect With Us</h2>
                    <p class="lead mb-5" style="opacity: 0.9;">
                        Join our brotherhood and grow spiritually, personally, and financially. Take the first step towards becoming the man God called you to be.
                    </p>
                    <div class="row g-4 mb-5">
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-envelope" style="font-size: 2rem; opacity: 0.8;"></i>
                                <h5 class="mt-3">Email Us</h5>
                                <p id="contact-email" style="opacity: 0.8;"><EMAIL></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-telephone" style="font-size: 2rem; opacity: 0.8;"></i>
                                <h5 class="mt-3">Call Us</h5>
                                <p id="contact-phone" style="opacity: 0.8;">+****************</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-geo-alt" style="font-size: 2rem; opacity: 0.8;"></i>
                                <h5 class="mt-3">Visit Us</h5>
                                <p id="contact-address" style="opacity: 0.8;">123 Church Street<br>City, State 12345</p>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-center gap-3 mb-4">
                        <a href="#" class="btn btn-outline-light btn-lg rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-facebook" style="font-size: 1.5rem;"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-lg rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-instagram" style="font-size: 1.5rem;"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-lg rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-twitter" style="font-size: 1.5rem;"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-lg rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-youtube" style="font-size: 1.5rem;"></i>
                        </a>
                    </div>
                    <a href="church/index.php" class="btn btn-light btn-lg px-5 py-3">
                        <i class="bi bi-arrow-right me-2"></i>Get Started Today
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-4" style="background-color: var(--secondary-color, #212529); color: white;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0" style="font-family: var(--font-family, 'Inter', sans-serif);">&copy; 2025 Freedom Assembly Church International. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0" style="font-family: var(--font-family, 'Inter', sans-serif);">Men's Visionaire and Billionaire Platform</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Load admin appearance settings and apply them
        async function loadAppearanceSettings() {
            try {
                const response = await fetch('church/api/get_appearance_settings.php');
                const settings = await response.json();

                if (settings.success) {
                    console.log('Appearance settings loaded:', settings);

                    // Apply CSS variables from appearance settings
                    const root = document.documentElement;
                    const cssVars = settings.css_variables || {};

                    // Apply all CSS variables
                    Object.keys(cssVars).forEach(key => {
                        root.style.setProperty(key, cssVars[key]);
                        console.log(`Applied CSS variable: ${key} = ${cssVars[key]}`);
                    });

                    // Create dynamic CSS to override hardcoded styles
                    const primaryColor = cssVars['--bs-primary'] || '#495057';
                    const secondaryColor = cssVars['--bs-secondary'] || '#6c757d';
                    const fontFamily = cssVars['--font-family'] || cssVars['--bs-font-sans-serif'] || 'Inter, sans-serif';
                    const fontSize = cssVars['--bs-body-font-size'] || '16px';
                    const lineHeight = cssVars['--bs-body-line-height'] || '1.5';
                    const borderRadius = cssVars['--bs-border-radius'] || '0.375rem';
                    const headingFontFamily = cssVars['--heading-font-family'] || fontFamily;
                    const headingFontWeight = cssVars['--heading-font-weight'] || '600';

                    const dynamicCSS = `
                        /* Typography Settings */
                        body {
                            font-family: ${fontFamily} !important;
                            font-size: ${fontSize} !important;
                            line-height: ${lineHeight} !important;
                        }

                        h1, h2, h3, h4, h5, h6 {
                            font-family: ${headingFontFamily} !important;
                            font-weight: ${headingFontWeight} !important;
                        }

                        /* Layout Settings */
                        .btn, .card, .badge, .alert, .modal-content {
                            border-radius: ${borderRadius} !important;
                        }

                        /* Color Settings */
                        .navbar {
                            background: linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%) !important;
                        }

                        #hero {
                            background: linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%) !important;
                        }

                        .card .bi {
                            color: ${primaryColor} !important;
                        }

                        .btn-primary, .badge {
                            background-color: ${primaryColor} !important;
                            border-color: ${primaryColor} !important;
                        }

                        .btn-primary:hover, .btn-primary:focus {
                            background-color: ${primaryColor} !important;
                            border-color: ${primaryColor} !important;
                            opacity: 0.9;
                        }

                        footer {
                            background-color: ${secondaryColor} !important;
                        }

                        /* Links */
                        a {
                            color: ${primaryColor} !important;
                        }

                        a:hover {
                            color: ${primaryColor} !important;
                            opacity: 0.8;
                        }

                        /* Event cards and other elements */
                        .text-primary {
                            color: ${primaryColor} !important;
                        }

                        .bg-primary {
                            background-color: ${primaryColor} !important;
                        }

                        .border-primary {
                            border-color: ${primaryColor} !important;
                        }
                    `;

                    // Apply dynamic CSS
                    const styleElement = document.getElementById('dynamic-theme-styles');
                    if (styleElement) {
                        styleElement.textContent = dynamicCSS;
                    }

                    // Load logo if available
                    if (settings.logo_url) {
                        const logoImg = document.getElementById('navbar-logo');
                        logoImg.src = settings.logo_url;
                        logoImg.style.display = 'block';
                    }

                    // Update organization name and content
                    if (settings.organization_name) {
                        document.getElementById('navbar-title').textContent = settings.organization_name;
                        document.title = settings.organization_name + ' - Men\'s Visionaire';

                        // Update hero section
                        const heroTitle = document.querySelector('#hero h1');
                        if (heroTitle) {
                            heroTitle.textContent = `Welcome to ${settings.organization_name}`;
                        }

                        // Update footer
                        const footerText = document.querySelector('footer .container .row .col-md-6 p');
                        if (footerText) {
                            footerText.textContent = `© 2025 ${settings.organization_name}. All rights reserved.`;
                        }
                    }

                    // Update mission and vision content
                    if (settings.organization_mission) {
                        const missionText = document.querySelector('#mission .lead');
                        if (missionText) {
                            missionText.textContent = settings.organization_mission;
                        }
                    }

                    // Update about section description
                    if (settings.organization_name) {
                        const aboutDescription = document.getElementById('about-description');
                        if (aboutDescription) {
                            const currentText = aboutDescription.textContent;
                            const updatedText = currentText.replace('Freedom Assembly Church International', settings.organization_name);
                            aboutDescription.textContent = updatedText;
                        }
                    }

                    if (settings.organization_vision) {
                        const aboutText = document.querySelector('#about .lead');
                        if (aboutText) {
                            aboutText.textContent = settings.organization_vision;
                        }
                    }

                    // Update contact information
                    if (settings.contact_info) {
                        const contact = settings.contact_info;

                        // Update email
                        if (contact.email) {
                            const emailElement = document.getElementById('contact-email');
                            if (emailElement) {
                                emailElement.textContent = contact.email;
                            }
                        }

                        // Update phone
                        if (contact.phone) {
                            const phoneElement = document.getElementById('contact-phone');
                            if (phoneElement) {
                                phoneElement.textContent = contact.phone;
                            }
                        }

                        // Update address
                        if (contact.address || contact.city || contact.state) {
                            const addressElement = document.getElementById('contact-address');
                            if (addressElement) {
                                let addressText = '';
                                if (contact.address) addressText += contact.address;
                                if (contact.city) addressText += (addressText ? '<br>' : '') + contact.city;
                                if (contact.state) addressText += (contact.city ? ', ' : (addressText ? '<br>' : '')) + contact.state;
                                if (contact.zip) addressText += ' ' + contact.zip;

                                if (addressText) {
                                    addressElement.innerHTML = addressText;
                                }
                            }
                        }
                    }

                    // Update social media links
                    if (settings.social_media) {
                        const social = settings.social_media;
                        const socialLinks = document.querySelectorAll('#contact .btn-outline-light');

                        if (social.facebook && socialLinks[0]) {
                            socialLinks[0].href = social.facebook;
                        }
                        if (social.instagram && socialLinks[1]) {
                            socialLinks[1].href = social.instagram;
                        }
                        if (social.twitter && socialLinks[2]) {
                            socialLinks[2].href = social.twitter;
                        }
                        if (social.youtube && socialLinks[3]) {
                            socialLinks[3].href = social.youtube;
                        }
                    }
                }
            } catch (error) {
                console.log('Could not load appearance settings:', error);
            }
        }

        // Load events from admin
        async function loadEvents() {
            try {
                const response = await fetch('church/api/get_public_events.php');
                const data = await response.json();

                const container = document.getElementById('events-container');

                if (data.success && data.events && data.events.length > 0) {
                    container.innerHTML = '';

                    data.events.slice(0, 3).forEach(event => {
                        const eventDate = new Date(event.event_date);
                        const eventCard = `
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="card h-100 border-0 shadow-sm" style="background: var(--bs-body-bg, white); border-radius: var(--bs-border-radius, 15px);">
                                    <div class="card-body p-4">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="badge" style="background-color: var(--bs-primary, #007bff); color: white; padding: 0.5rem 1rem; border-radius: var(--bs-border-radius, 10px);">
                                                ${eventDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                                            </div>
                                            <small style="color: var(--bs-body-color, #6c757d);">
                                                ${eventDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}
                                            </small>
                                        </div>
                                        <h5 class="card-title mb-3" style="color: var(--bs-body-color, #212529);">${event.title}</h5>
                                        <p class="card-text mb-3" style="color: var(--bs-body-color, #6c757d);">
                                            <i class="bi bi-geo-alt me-2"></i>${event.location || 'Location TBD'}
                                        </p>
                                        <p class="card-text" style="color: var(--bs-body-color, #6c757d);">
                                            ${event.description ? event.description.substring(0, 100) + '...' : 'Join us for this exciting event!'}
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center mt-3">
                                            <small style="color: var(--bs-body-color, #6c757d);">
                                                <i class="bi bi-people me-1"></i>
                                                ${event.rsvp_count || 0} attending
                                            </small>
                                            <a href="church/user/events.php" class="btn btn-sm" style="background-color: var(--bs-primary, #007bff); color: white;">
                                                Learn More
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        container.innerHTML += eventCard;
                    });
                } else {
                    container.innerHTML = `
                        <div class="col-12 text-center">
                            <div class="card border-0 shadow-sm" style="background: var(--bs-body-bg, white); border-radius: var(--bs-border-radius, 15px);">
                                <div class="card-body p-5">
                                    <i class="bi bi-calendar-x" style="font-size: 3rem; color: var(--bs-primary, #007bff); opacity: 0.5;"></i>
                                    <h5 class="mt-3" style="color: var(--bs-body-color, #212529);">No Upcoming Events</h5>
                                    <p style="color: var(--bs-body-color, #6c757d);">Check back soon for exciting events and activities!</p>
                                    <a href="church/user/events.php" class="btn" style="background-color: var(--bs-primary, #007bff); color: white;">
                                        View All Events
                                    </a>
                                </div>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.log('Could not load events:', error);
                document.getElementById('events-container').innerHTML = `
                    <div class="col-12 text-center">
                        <p style="color: var(--bs-body-color, #6c757d);">Unable to load events at this time.</p>
                    </div>
                `;
            }
        }

        // Enhanced smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                smoothScrollTo(targetId);

                // Update active nav link
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                this.classList.add('active');
            });
        });

        // Enhanced navbar on scroll with smooth transitions
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Scroll animations for sections
        function animateOnScroll() {
            const sections = document.querySelectorAll('.section-animate');
            const windowHeight = window.innerHeight;

            sections.forEach(section => {
                const sectionTop = section.getBoundingClientRect().top;
                const sectionVisible = sectionTop < windowHeight - 100;

                if (sectionVisible) {
                    section.classList.add('visible');
                }
            });
        }

        // Enhanced smooth scrolling with easing
        function smoothScrollTo(target) {
            const targetElement = document.querySelector(target);
            if (targetElement) {
                const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - 80;
                const startPosition = window.pageYOffset;
                const distance = targetPosition - startPosition;
                const duration = 1000;
                let start = null;

                function animation(currentTime) {
                    if (start === null) start = currentTime;
                    const timeElapsed = currentTime - start;
                    const run = easeInOutQuad(timeElapsed, startPosition, distance, duration);
                    window.scrollTo(0, run);
                    if (timeElapsed < duration) requestAnimationFrame(animation);
                }

                function easeInOutQuad(t, b, c, d) {
                    t /= d / 2;
                    if (t < 1) return c / 2 * t * t + b;
                    t--;
                    return -c / 2 * (t * (t - 2) - 1) + b;
                }

                requestAnimationFrame(animation);
            }
        }

        // Load everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadAppearanceSettings();
            loadEvents();

            // Initialize scroll animations
            animateOnScroll();
            window.addEventListener('scroll', animateOnScroll);

            // Add entrance animation to hero section
            setTimeout(() => {
                const heroContent = document.querySelector('#hero .col-lg-6:first-child');
                if (heroContent) {
                    heroContent.style.opacity = '0';
                    heroContent.style.transform = 'translateY(30px)';
                    heroContent.style.transition = 'all 1s ease';

                    setTimeout(() => {
                        heroContent.style.opacity = '1';
                        heroContent.style.transform = 'translateY(0)';
                    }, 300);
                }
            }, 100);

            // Add stagger animation to mission cards
            setTimeout(() => {
                const missionCards = document.querySelectorAll('#mission .card');
                missionCards.forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.8s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 500 + (index * 200));
                });
            }, 1000);
        });
    </script>
</body>
</html>