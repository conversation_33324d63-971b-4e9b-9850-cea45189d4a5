<?php
require_once '../config.php';

echo "<h2>Appearance Settings Debug</h2>";

try {
    $stmt = $pdo->prepare("SELECT setting_name, setting_value, setting_type FROM appearance_settings ORDER BY setting_name");
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Current appearance settings:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Setting Name</th><th>Setting Value</th><th>Setting Type</th></tr>";
    foreach ($settings as $setting) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($setting['setting_name']) . "</td>";
        echo "<td>" . htmlspecialchars($setting['setting_value']) . "</td>";
        echo "<td>" . htmlspecialchars($setting['setting_type']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check custom CSS file
    $cssFile = __DIR__ . '/css/custom-theme.css';
    echo "<h3>Custom CSS File:</h3>";
    if (file_exists($cssFile)) {
        echo "<pre>" . htmlspecialchars(file_get_contents($cssFile)) . "</pre>";
    } else {
        echo "<p>CSS file does not exist</p>";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
