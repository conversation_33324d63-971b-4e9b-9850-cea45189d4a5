{"config": {"configFile": "C:\\xampp\\htdocs\\campaign\\playwright.config.js", "rootDir": "C:/xampp/htdocs/campaign", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 600000, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/xampp/htdocs/campaign/test-results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/xampp/htdocs/campaign", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 120000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 1, "webServer": null}, "suites": [], "errors": [{"message": "Error: No tests found", "stack": "Error: No tests found"}], "stats": {"startTime": "2025-06-26T16:00:59.304Z", "duration": 177.02300000000002, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}