<?php
// Comprehensive demo of the attendance tracking workflow
require_once 'config.php';

echo "<h1>Event Attendance Tracking System Demo</h1>";

try {
    // Step 1: Show events with RSVP data
    echo "<h2>Step 1: Events with RSVP Data</h2>";
    
    $events_query = "
        SELECT 
            e.id,
            e.title,
            e.event_date,
            e.location,
            COUNT(CASE WHEN er.status = 'attending' THEN 1 END) as attending_count,
            COUNT(CASE WHEN er.status = 'maybe' THEN 1 END) as maybe_count,
            COUNT(CASE WHEN er.status = 'not_attending' THEN 1 END) as not_attending_count,
            COUNT(CASE WHEN er.actually_attended = 1 THEN 1 END) as actually_attended_count,
            COUNT(CASE WHEN er.status = 'attending' AND er.actually_attended IS NOT NULL THEN 1 END) as attendance_marked_count
        FROM events e
        LEFT JOIN event_rsvps er ON e.id = er.event_id
        WHERE e.is_active = 1
        GROUP BY e.id, e.title, e.event_date, e.location
        HAVING attending_count > 0
        ORDER BY e.event_date DESC
    ";
    
    $events = $pdo->query($events_query)->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='8' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>Event</th><th>Date</th><th>Location</th><th>Attending</th><th>Maybe</th><th>Not Attending</th><th>Actually Attended</th><th>Attendance Progress</th>";
    echo "</tr>";
    
    foreach ($events as $event) {
        $attendance_percentage = $event['attending_count'] > 0 
            ? round(($event['attendance_marked_count'] / $event['attending_count']) * 100) 
            : 0;
            
        echo "<tr>";
        echo "<td><strong>" . htmlspecialchars($event['title']) . "</strong></td>";
        echo "<td>" . date('M j, Y g:i A', strtotime($event['event_date'])) . "</td>";
        echo "<td>" . htmlspecialchars($event['location'] ?? 'TBD') . "</td>";
        echo "<td style='text-align: center; background-color: #d4edda;'>" . $event['attending_count'] . "</td>";
        echo "<td style='text-align: center; background-color: #fff3cd;'>" . $event['maybe_count'] . "</td>";
        echo "<td style='text-align: center; background-color: #f8d7da;'>" . $event['not_attending_count'] . "</td>";
        echo "<td style='text-align: center; background-color: #d1ecf1;'>" . $event['actually_attended_count'] . "</td>";
        echo "<td>" . $event['attendance_marked_count'] . "/" . $event['attending_count'] . " marked (" . $attendance_percentage . "%)</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    // Step 2: Show detailed attendee list for first event
    if (!empty($events)) {
        $sample_event = $events[0];
        echo "<h2>Step 2: Detailed Attendee List for '" . htmlspecialchars($sample_event['title']) . "'</h2>";
        
        $attendees_query = "
            SELECT 
                er.id as rsvp_id,
                er.user_id,
                er.status,
                er.notes,
                er.actually_attended,
                er.created_at as rsvp_date,
                m.full_name,
                m.email,
                m.phone_number
            FROM event_rsvps er
            JOIN members m ON er.user_id = m.id
            WHERE er.event_id = ? AND er.status = 'attending'
            ORDER BY m.full_name ASC
        ";
        
        $stmt = $pdo->prepare($attendees_query);
        $stmt->execute([$sample_event['id']]);
        $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='8' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th>Name</th><th>Email</th><th>Phone</th><th>RSVP Date</th><th>Notes</th><th>Actually Attended</th>";
        echo "</tr>";
        
        foreach ($attendees as $attendee) {
            $attendance_status = '';
            $bg_color = '';
            if ($attendee['actually_attended'] === null) {
                $attendance_status = 'Not marked';
                $bg_color = '#f8f9fa';
            } elseif ($attendee['actually_attended'] == 1) {
                $attendance_status = '✓ Yes';
                $bg_color = '#d4edda';
            } else {
                $attendance_status = '✗ No';
                $bg_color = '#f8d7da';
            }
            
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($attendee['full_name']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($attendee['email']) . "</td>";
            echo "<td>" . htmlspecialchars($attendee['phone_number'] ?? '-') . "</td>";
            echo "<td>" . date('M j, Y', strtotime($attendee['rsvp_date'])) . "</td>";
            echo "<td>" . htmlspecialchars($attendee['notes'] ?? '-') . "</td>";
            echo "<td style='background-color: " . $bg_color . "; text-align: center;'>" . $attendance_status . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
        
        // Step 3: Simulate marking attendance
        echo "<h2>Step 3: Simulating Attendance Marking</h2>";
        
        if (!empty($attendees)) {
            // Mark some attendees as attended
            $updates_made = 0;
            foreach ($attendees as $index => $attendee) {
                if ($attendee['actually_attended'] === null) {
                    // Mark every other person as attended for demo
                    $attended = ($index % 2 == 0) ? 1 : 0;
                    
                    $stmt = $pdo->prepare("
                        UPDATE event_rsvps 
                        SET actually_attended = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$attended, $attendee['rsvp_id']]);
                    
                    $status = $attended ? 'attended' : 'did not attend';
                    echo "✓ Marked " . htmlspecialchars($attendee['full_name']) . " as " . $status . "<br>";
                    $updates_made++;
                }
            }
            
            if ($updates_made == 0) {
                echo "All attendees already have attendance marked.<br>";
            }
            echo "<br>";
        }
        
        // Step 4: Show updated user stats
        echo "<h2>Step 4: Updated User Statistics</h2>";
        
        $user_stats_query = "
            SELECT 
                m.id,
                m.full_name,
                m.email,
                COUNT(CASE WHEN er.status = 'attending' THEN 1 END) as total_rsvps,
                COUNT(CASE WHEN er.actually_attended = 1 THEN 1 END) as events_actually_attended
            FROM members m
            LEFT JOIN event_rsvps er ON m.id = er.user_id
            WHERE m.status = 'active'
            GROUP BY m.id, m.full_name, m.email
            HAVING total_rsvps > 0
            ORDER BY events_actually_attended DESC, m.full_name ASC
        ";
        
        $user_stats = $pdo->query($user_stats_query)->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='8' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th>Member</th><th>Email</th><th>Total RSVPs (Attending)</th><th>Events Actually Attended</th><th>Attendance Rate</th>";
        echo "</tr>";
        
        foreach ($user_stats as $user) {
            $attendance_rate = $user['total_rsvps'] > 0 
                ? round(($user['events_actually_attended'] / $user['total_rsvps']) * 100) 
                : 0;
                
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($user['full_name']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td style='text-align: center;'>" . $user['total_rsvps'] . "</td>";
            echo "<td style='text-align: center; background-color: #d1ecf1;'>" . $user['events_actually_attended'] . "</td>";
            echo "<td style='text-align: center;'>" . $attendance_rate . "%</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    }
    
    echo "<h2>System Features Summary</h2>";
    echo "<div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px;'>";
    echo "<h3>✅ Completed Features:</h3>";
    echo "<ul>";
    echo "<li><strong>Database Schema:</strong> Added 'actually_attended' column to event_rsvps table</li>";
    echo "<li><strong>Admin Event Management:</strong> Enhanced events overview with attendance tracking</li>";
    echo "<li><strong>Admin Attendance Interface:</strong> Detailed page for marking individual attendance</li>";
    echo "<li><strong>User Stats Integration:</strong> Updated 'Events Attended' count to reflect actual attendance</li>";
    echo "<li><strong>Access Control:</strong> Admin-only access with proper authentication checks</li>";
    echo "<li><strong>Visual Indicators:</strong> Progress bars and badges showing attendance status</li>";
    echo "<li><strong>Bulk Operations:</strong> Mark all as attended/not attended functionality</li>";
    echo "</ul>";
    
    echo "<h3>🔗 Navigation Links:</h3>";
    echo "<ul>";
    echo "<li><a href='admin/event_attendance.php'>Admin Event Attendance Tracking</a> (requires admin login)</li>";
    echo "<li><a href='admin/events.php'>Admin Event Management</a> (requires admin login)</li>";
    echo "<li><a href='user/events.php'>User Events Page</a> (requires user login)</li>";
    echo "<li><a href='user/dashboard.php'>User Dashboard</a> (requires user login)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
}
