# User Authentication System Testing Guide

This guide will help you test all the functionality of the new user authentication and profile management system.

## Prerequisites

1. **Database Setup**: Ensure the database schema has been applied
   - Run: `http://localhost/campaign/church/admin/setup_user_authentication.php`
   - Verify all tables are created successfully

2. **Test Environment**: Make sure your XAMPP/WAMP server is running
   - Apache and MySQL services should be active
   - PHP extensions enabled: PDO, GD (for image processing)

## Automated Testing

### Step 1: Run Comprehensive Test Script
1. Navigate to: `http://localhost/campaign/church/test_user_system.php`
2. Review the test results:
   - ✅ **Green**: All tests should pass
   - ❌ **Red**: Any failures need to be addressed
   - ⚠️ **Yellow**: Warnings or errors

### Expected Test Results:
- **Database Schema Verification**: All required tables exist
- **Members Table Authentication Columns**: All auth columns added
- **UserAuthManager Class Methods**: All required methods available
- **Test User Creation**: Can create new user accounts
- **User Authentication**: Login system works correctly
- **Profile Update**: Profile editing functionality works
- **User Preferences**: Settings system operational
- **Password Reset Token**: Password reset system functional
- **User Interface Files**: All UI files exist
- **Security Features**: CSRF, validation, password hashing work

## Manual Testing Workflow

### Phase 1: User Registration
1. **Navigate to Registration**: `http://localhost/campaign/church/index.php`
2. **Test Registration Form**:
   - Fill out all required fields
   - Upload a profile photo (optional)
   - Use test email: `<EMAIL>` (as per your preference)
   - Set a strong password
   - Submit form
3. **Verify Success**: Should redirect with success message

### Phase 2: User Authentication
1. **Navigate to Login**: `http://localhost/campaign/church/user/login.php`
2. **Test Login Scenarios**:
   - ✅ **Valid Email + Password**: Should login successfully
   - ✅ **Valid Phone + Password**: Should login successfully  
   - ❌ **Invalid Credentials**: Should show error message
   - ❌ **Empty Fields**: Should show validation errors

### Phase 3: Dashboard Functionality
1. **Access Dashboard**: Should redirect to `user/dashboard.php` after login
2. **Verify Dashboard Elements**:
   - Welcome message with user name
   - Profile photo display (or default icon)
   - Navigation menu (Dashboard, Profile, Events, Settings)
   - User dropdown menu
   - Quick action buttons
   - Account information display

### Phase 4: Profile Management
1. **Navigate to Profile**: Click "Profile" in navigation or user dropdown
2. **Test Profile Features**:
   - **View Current Profile**: All information should display correctly
   - **Edit Profile Information**: 
     - Update name, phone, address, occupation
     - Change birth date
     - Save changes and verify updates
   - **Profile Photo Management**:
     - Upload new photo (test with JPG, PNG, GIF)
     - Preview functionality should work
     - Remove current photo option
     - Verify file size limits (5MB max)
     - Test invalid file types (should be rejected)

### Phase 5: Settings & Preferences
1. **Navigate to Settings**: Click "Settings" in navigation
2. **Test Notification Preferences**:
   - Toggle email notifications on/off
   - Toggle SMS notifications on/off
   - Toggle birthday reminders on/off
   - Toggle event notifications on/off
   - Toggle newsletter subscription on/off
   - Save settings and verify persistence
3. **Test Privacy Settings**:
   - Change profile visibility (Public, Members Only, Private)
   - Verify badge updates in real-time
4. **Test Display Preferences**:
   - Change timezone
   - Change language (if multiple available)
   - Change dashboard layout
   - Save and verify changes

### Phase 6: Password Management
1. **Change Password** (while logged in):
   - Navigate to: `user/change_password.php`
   - Enter current password
   - Enter new password (test password requirements)
   - Confirm new password
   - Verify password change success
2. **Forgot Password** (while logged out):
   - Navigate to: `user/forgot_password.php`
   - Enter email address
   - Check for password reset email (if email configured)
   - Test reset token functionality

### Phase 7: Security Testing
1. **Session Management**:
   - Login and verify session persistence
   - Test logout functionality
   - Verify session cleanup after logout
2. **Access Control**:
   - Try accessing user pages without login (should redirect)
   - Test CSRF protection on forms
3. **Input Validation**:
   - Test form validation with invalid data
   - Test file upload restrictions
   - Test SQL injection prevention (try malicious inputs)

## Test Data Suggestions

### Test User Accounts
```
Email: <EMAIL>
Phone: ******-0123
Name: Test User
Password: TestPassword123!
Address: 123 Test Street, Test City, TC 12345
Occupation: Software Tester
Birth Date: 1990-01-15
```

### Test Profile Photos
- **Valid**: JPG, PNG, GIF files under 5MB
- **Invalid**: PDF, TXT, EXE files
- **Large Files**: Test with files over 5MB (should be rejected)

## Common Issues & Solutions

### Database Issues
- **Missing Tables**: Run setup script again
- **Permission Errors**: Check MySQL user permissions
- **Connection Errors**: Verify config.php database settings

### File Upload Issues
- **Upload Fails**: Check PHP upload_max_filesize and post_max_size
- **Permission Errors**: Verify uploads/ directory permissions (755)
- **Path Issues**: Ensure uploads/ directory exists

### Authentication Issues
- **Login Fails**: Check password hashing compatibility
- **Session Issues**: Verify PHP session configuration
- **Redirect Loops**: Check authentication logic

## Success Criteria

✅ **All automated tests pass**
✅ **User registration works with email confirmation**
✅ **Login works with both email and phone**
✅ **Dashboard displays correctly with user data**
✅ **Profile editing saves changes successfully**
✅ **Photo upload/removal works properly**
✅ **Settings persist across sessions**
✅ **Password change/reset functions correctly**
✅ **Security measures prevent unauthorized access**
✅ **All forms validate input properly**

## Next Steps After Testing

Once testing is complete and all functionality works:

1. **Review Test Results**: Address any failed tests
2. **Security Audit**: Ensure all security measures are working
3. **Performance Check**: Test with multiple users if possible
4. **Integration Verification**: Ensure compatibility with existing admin system
5. **Documentation**: Update any configuration or deployment notes

## Support

If you encounter issues during testing:
1. Check the automated test results for specific error messages
2. Review PHP error logs for detailed error information
3. Verify database schema and data integrity
4. Test individual components in isolation

The system is designed to be robust and user-friendly. Most issues are typically related to configuration or environment setup rather than code functionality.
