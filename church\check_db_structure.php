<?php
// Check database structure
try {
    $pdo = new PDO('mysql:host=localhost;dbname=campaign', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Members table structure:\n";
    $stmt = $pdo->query('DESCRIBE members');
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo $row['Field'] . ' - ' . $row['Type'] . ' - Null: ' . $row['Null'] . ' - Default: ' . $row['Default'] . "\n";
    }
    
    echo "\n\nChecking for specific columns:\n";
    $columns = ['password_hash', 'temp_password', 'must_change_password', 'email_verified'];
    foreach ($columns as $column) {
        $stmt = $pdo->query("SHOW COLUMNS FROM members LIKE '$column'");
        if ($stmt->rowCount() > 0) {
            echo "✓ Column '$column' exists\n";
        } else {
            echo "✗ Column '$column' missing\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>