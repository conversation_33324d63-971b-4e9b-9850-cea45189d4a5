<?php
require_once __DIR__ . '/config.php';

echo "<h1>Testing Fixed System</h1>";

// Test 1: Check events with correct fields
echo "<h2>Test 1: Events Query</h2>";
try {
    $stmt = $pdo->prepare("
        SELECT e.*,
               COUNT(CASE WHEN er.status = 'attending' THEN 1 END) as attending_count,
               COUNT(CASE WHEN er.status = 'maybe' THEN 1 END) as maybe_count
        FROM events e
        LEFT JOIN event_rsvps er ON e.id = er.event_id
        WHERE e.is_active = 1
        GROUP BY e.id
        ORDER BY e.event_date ASC
    ");
    $stmt->execute();
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($events) {
        echo "✅ Events query successful<br>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Date/Time</th><th>Location</th><th>Attending</th><th>Maybe</th><th>Future?</th></tr>";
        foreach ($events as $event) {
            $isFuture = strtotime($event['event_date']) > time() ? 'Yes' : 'No';
            echo "<tr>";
            echo "<td>{$event['id']}</td>";
            echo "<td>{$event['title']}</td>";
            echo "<td>{$event['event_date']}</td>";
            echo "<td>{$event['location']}</td>";
            echo "<td>{$event['attending_count']}</td>";
            echo "<td>{$event['maybe_count']}</td>";
            echo "<td>$isFuture</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No events found<br>";
    }
} catch (Exception $e) {
    echo "❌ Events query error: " . $e->getMessage() . "<br>";
}

// Test 2: Check members table
echo "<h2>Test 2: Members Table</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM members");
    $result = $stmt->fetch();
    echo "✅ Members table accessible: {$result['count']} members<br>";
    
    // Get a sample member
    $stmt = $pdo->query("SELECT id, first_name, last_name, email FROM members LIMIT 1");
    $member = $stmt->fetch();
    if ($member) {
        echo "Sample member: {$member['first_name']} {$member['last_name']} (ID: {$member['id']})<br>";
    }
} catch (Exception $e) {
    echo "❌ Members table error: " . $e->getMessage() . "<br>";
}

// Test 3: Test RSVP functionality
echo "<h2>Test 3: RSVP Test</h2>";
try {
    // Get a future event
    $stmt = $pdo->prepare("SELECT * FROM events WHERE is_active = 1 AND event_date > NOW() LIMIT 1");
    $stmt->execute();
    $futureEvent = $stmt->fetch();
    
    if ($futureEvent) {
        echo "✅ Future event found: {$futureEvent['title']} on {$futureEvent['event_date']}<br>";
        
        // Check if we can query RSVPs for this event
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM event_rsvps WHERE event_id = ?");
        $stmt->execute([$futureEvent['id']]);
        $rsvpCount = $stmt->fetch()['count'];
        echo "✅ Current RSVPs for this event: $rsvpCount<br>";
        
    } else {
        echo "❌ No future events found<br>";
    }
} catch (Exception $e) {
    echo "❌ RSVP test error: " . $e->getMessage() . "<br>";
}

// Test 4: Test date/time formatting
echo "<h2>Test 4: Date/Time Formatting</h2>";
try {
    $stmt = $pdo->query("SELECT event_date FROM events LIMIT 1");
    $event = $stmt->fetch();
    if ($event) {
        $eventDate = $event['event_date'];
        echo "Raw event_date: $eventDate<br>";
        echo "Formatted date: " . date('l, F j, Y', strtotime($eventDate)) . "<br>";
        echo "Formatted time: " . date('g:i A', strtotime($eventDate)) . "<br>";
        echo "✅ Date/time formatting works<br>";
    }
} catch (Exception $e) {
    echo "❌ Date formatting error: " . $e->getMessage() . "<br>";
}

echo "<h2>Navigation Links</h2>";
echo "<a href='user/login.php'>Login Page</a><br>";
echo "<a href='user/events.php'>Events Page (requires login)</a><br>";
echo "<a href='user/dashboard.php'>Dashboard (requires login)</a><br>";
?>
