<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$success = '';
$error = '';

// Handle SMS sending
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_sms'])) {
    $recipient_type = $_POST['recipient_type'] ?? '';
    $recipient_id = $_POST['recipient_id'] ?? '';
    $custom_phone = $_POST['custom_phone'] ?? '';
    $custom_name = $_POST['custom_name'] ?? '';
    $message = trim($_POST['message'] ?? '');
    
    if (empty($message)) {
        $error = 'Message is required.';
    } else {
        try {
            $phone_number = '';
            $recipient_name = '';
            $member_id = null;
            
            if ($recipient_type === 'member' && !empty($recipient_id)) {
                // Get member details
                $stmt = $pdo->prepare("SELECT id, full_name, phone_number FROM members WHERE id = ? AND status = 'active'");
                $stmt->execute([$recipient_id]);
                $member = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($member) {
                    $phone_number = $member['phone_number'];
                    $recipient_name = $member['full_name'];
                    $member_id = $member['id'];
                } else {
                    $error = 'Member not found or inactive.';
                }
            } elseif ($recipient_type === 'contact' && !empty($recipient_id)) {
                // Get contact details
                $stmt = $pdo->prepare("SELECT id, name, phone_number FROM contacts WHERE id = ? AND status = 'active'");
                $stmt->execute([$recipient_id]);
                $contact = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($contact) {
                    $phone_number = $contact['phone_number'];
                    $recipient_name = $contact['name'];
                } else {
                    $error = 'Contact not found or inactive.';
                }
            } elseif ($recipient_type === 'custom') {
                $phone_number = $custom_phone;
                $recipient_name = $custom_name ?: 'Custom Recipient';
            } else {
                $error = 'Please select a valid recipient.';
            }
            
            if (!$error && !empty($phone_number)) {
                // Create SMS campaign for single SMS
                $stmt = $pdo->prepare("INSERT INTO sms_campaigns (name, message, status, total_recipients, created_at) VALUES (?, ?, 'completed', 1, NOW())");
                $campaignName = "Single SMS to " . $recipient_name . " - " . date('Y-m-d H:i:s');
                $stmt->execute([$campaignName, $message]);
                $campaign_id = $pdo->lastInsertId();
                
                // Add recipient to campaign
                $stmt = $pdo->prepare("INSERT INTO sms_campaign_recipients (campaign_id, member_id, phone_number, status, sent_at) VALUES (?, ?, ?, 'sent', NOW())");
                $stmt->execute([$campaign_id, $member_id, $phone_number]);
                
                // Log the SMS
                $stmt = $pdo->prepare("INSERT INTO sms_logs (campaign_id, member_id, phone_number, message, provider, status, sent_at) VALUES (?, ?, ?, ?, 'manual', 'sent', NOW())");
                $stmt->execute([$campaign_id, $member_id, $phone_number, $message]);
                
                $success = "SMS sent successfully to " . htmlspecialchars($recipient_name) . " (" . htmlspecialchars($phone_number) . ")";
                
                // Clear form
                $_POST = [];
            } elseif (!$error) {
                $error = 'Invalid phone number.';
            }
            
        } catch (PDOException $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}

// Get SMS templates
$templates = [];
try {
    $stmt = $pdo->prepare("SELECT id, name, content FROM sms_templates WHERE status = 'active' ORDER BY name");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Templates table might not exist yet
}

// Get members with phone numbers
$members = [];
try {
    $stmt = $pdo->prepare("SELECT id, full_name, phone_number FROM members WHERE status = 'active' AND phone_number IS NOT NULL AND phone_number != '' ORDER BY full_name");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Handle error
}

// Get contacts with phone numbers
$contacts = [];
try {
    $stmt = $pdo->prepare("SELECT id, name, phone_number FROM contacts WHERE status = 'active' AND phone_number IS NOT NULL AND phone_number != '' ORDER BY name");
    $stmt->execute();
    $contacts = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Handle error
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="bi bi-chat-text me-2"></i>Single SMS
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="bulk_sms.php" class="btn btn-outline-primary">
                        <i class="bi bi-broadcast me-1"></i>Bulk SMS
                    </a>
                </div>
            </div>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-person-plus me-2"></i>Send Single SMS
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" id="singleSmsForm">
                                <!-- Recipient Selection -->
                                <div class="mb-3">
                                    <label class="form-label">Recipient Type *</label>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="recipient_type" id="type_member" value="member" onchange="toggleRecipientFields()">
                                                <label class="form-check-label" for="type_member">
                                                    <i class="bi bi-people me-1"></i>Church Member
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="recipient_type" id="type_contact" value="contact" onchange="toggleRecipientFields()">
                                                <label class="form-check-label" for="type_contact">
                                                    <i class="bi bi-person-rolodex me-1"></i>External Contact
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="recipient_type" id="type_custom" value="custom" onchange="toggleRecipientFields()">
                                                <label class="form-check-label" for="type_custom">
                                                    <i class="bi bi-telephone me-1"></i>Custom Number
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Member Selection -->
                                <div class="mb-3" id="member_selection" style="display: none;">
                                    <label for="member_id" class="form-label">Select Member *</label>
                                    <select class="form-select" id="member_id" name="recipient_id">
                                        <option value="">Choose a member...</option>
                                        <?php foreach ($members as $member): ?>
                                            <option value="<?php echo $member['id']; ?>" data-phone="<?php echo htmlspecialchars($member['phone_number']); ?>">
                                                <?php echo htmlspecialchars($member['full_name']); ?> (<?php echo htmlspecialchars($member['phone_number']); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- Contact Selection -->
                                <div class="mb-3" id="contact_selection" style="display: none;">
                                    <label for="contact_id" class="form-label">Select Contact *</label>
                                    <select class="form-select" id="contact_id" name="recipient_id">
                                        <option value="">Choose a contact...</option>
                                        <?php foreach ($contacts as $contact): ?>
                                            <option value="<?php echo $contact['id']; ?>" data-phone="<?php echo htmlspecialchars($contact['phone_number']); ?>">
                                                <?php echo htmlspecialchars($contact['name']); ?> (<?php echo htmlspecialchars($contact['phone_number']); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- Custom Number -->
                                <div id="custom_selection" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="custom_phone" class="form-label">Phone Number *</label>
                                                <input type="tel" class="form-control" id="custom_phone" name="custom_phone" placeholder="+1234567890">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="custom_name" class="form-label">Name (Optional)</label>
                                                <input type="text" class="form-control" id="custom_name" name="custom_name" placeholder="Recipient name">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Template Selection -->
                                <?php if (!empty($templates)): ?>
                                <div class="mb-3">
                                    <label for="template" class="form-label">Use Template (Optional)</label>
                                    <select class="form-select" id="template" name="template" onchange="loadTemplate()">
                                        <option value="">Select a template...</option>
                                        <?php foreach ($templates as $template): ?>
                                            <option value="<?php echo $template['id']; ?>" data-content="<?php echo htmlspecialchars($template['content']); ?>">
                                                <?php echo htmlspecialchars($template['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php endif; ?>

                                <!-- Message -->
                                <div class="mb-3">
                                    <label for="message" class="form-label">Message *</label>
                                    <textarea class="form-control" id="message" name="message" rows="4" placeholder="Type your SMS message here..." required oninput="updateCharacterCount()"><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                                    <div class="form-text">
                                        <span id="charCount">0</span>/160 characters
                                        <span id="smsCount" class="ms-3">1 SMS</span>
                                    </div>
                                </div>

                                <!-- Send Button -->
                                <div class="d-grid">
                                    <button type="submit" name="send_sms" class="btn btn-primary btn-lg">
                                        <i class="bi bi-send me-2"></i>Send SMS
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Quick Stats -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-graph-up me-2"></i>Quick Stats
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary mb-1"><?php echo number_format(count($members)); ?></h4>
                                        <small class="text-muted">Members</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success mb-1"><?php echo number_format(count($contacts)); ?></h4>
                                    <small class="text-muted">Contacts</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent SMS -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-clock-history me-2"></i>Recent SMS
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php
                            try {
                                $stmt = $pdo->prepare("SELECT sl.phone_number, sl.message, sl.sent_at, m.full_name 
                                                     FROM sms_logs sl 
                                                     LEFT JOIN members m ON sl.member_id = m.id 
                                                     ORDER BY sl.sent_at DESC 
                                                     LIMIT 5");
                                $stmt->execute();
                                $recentSms = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                
                                if (!empty($recentSms)):
                                    foreach ($recentSms as $sms):
                            ?>
                                <div class="d-flex align-items-start mb-3">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-chat-text text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <div class="fw-bold small"><?php echo htmlspecialchars($sms['full_name'] ?: 'Unknown'); ?></div>
                                        <div class="text-muted small"><?php echo htmlspecialchars($sms['phone_number']); ?></div>
                                        <div class="small"><?php echo htmlspecialchars(substr($sms['message'], 0, 50)) . (strlen($sms['message']) > 50 ? '...' : ''); ?></div>
                                        <div class="text-muted small"><?php echo date('M j, g:i A', strtotime($sms['sent_at'])); ?></div>
                                    </div>
                                </div>
                            <?php 
                                    endforeach;
                                else:
                            ?>
                                <p class="text-muted small mb-0">No recent SMS found.</p>
                            <?php 
                                endif;
                            } catch (PDOException $e) {
                                echo '<p class="text-muted small mb-0">Unable to load recent SMS.</p>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function toggleRecipientFields() {
    const memberRadio = document.getElementById('type_member');
    const contactRadio = document.getElementById('type_contact');
    const customRadio = document.getElementById('type_custom');
    
    const memberSelection = document.getElementById('member_selection');
    const contactSelection = document.getElementById('contact_selection');
    const customSelection = document.getElementById('custom_selection');
    
    // Hide all selections
    memberSelection.style.display = 'none';
    contactSelection.style.display = 'none';
    customSelection.style.display = 'none';
    
    // Clear required attributes
    document.getElementById('member_id').removeAttribute('required');
    document.getElementById('contact_id').removeAttribute('required');
    document.getElementById('custom_phone').removeAttribute('required');
    
    // Show appropriate selection
    if (memberRadio.checked) {
        memberSelection.style.display = 'block';
        document.getElementById('member_id').setAttribute('required', 'required');
    } else if (contactRadio.checked) {
        contactSelection.style.display = 'block';
        document.getElementById('contact_id').setAttribute('required', 'required');
    } else if (customRadio.checked) {
        customSelection.style.display = 'block';
        document.getElementById('custom_phone').setAttribute('required', 'required');
    }
}

function loadTemplate() {
    const select = document.getElementById('template');
    const messageTextarea = document.getElementById('message');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value && selectedOption.dataset.content) {
        messageTextarea.value = selectedOption.dataset.content;
        updateCharacterCount();
    }
}

function updateCharacterCount() {
    const message = document.getElementById('message').value;
    const charCount = message.length;
    const smsCount = Math.ceil(charCount / 160) || 1;
    
    document.getElementById('charCount').textContent = charCount;
    document.getElementById('smsCount').textContent = smsCount + ' SMS';
    
    // Update color based on length
    const charCountElement = document.getElementById('charCount');
    if (charCount > 160) {
        charCountElement.className = 'text-warning';
    } else if (charCount > 140) {
        charCountElement.className = 'text-info';
    } else {
        charCountElement.className = '';
    }
}

// Initialize character count
document.addEventListener('DOMContentLoaded', function() {
    updateCharacterCount();
});
</script>

<?php include 'includes/footer.php'; ?>
