<?php
require_once 'config.php';

echo "<h2>Birthday Templates Debug</h2>";

try {
    // Check if email_templates table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'email_templates'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ email_templates table does not exist!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ email_templates table exists</p>";
    
    // Get all templates
    echo "<h3>All Email Templates:</h3>";
    $stmt = $pdo->query("SELECT id, template_name, subject, is_birthday_template, template_category FROM email_templates ORDER BY id");
    $allTemplates = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Subject</th><th>Is Birthday</th><th>Category</th></tr>";
    foreach ($allTemplates as $template) {
        echo "<tr>";
        echo "<td>" . $template['id'] . "</td>";
        echo "<td>" . htmlspecialchars($template['template_name']) . "</td>";
        echo "<td>" . htmlspecialchars($template['subject']) . "</td>";
        echo "<td>" . ($template['is_birthday_template'] ? 'YES' : 'NO') . "</td>";
        echo "<td>" . htmlspecialchars($template['template_category'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Get birthday templates specifically
    echo "<h3>Birthday Templates Only:</h3>";
    $stmt = $pdo->query("SELECT * FROM email_templates WHERE is_birthday_template = 1 ORDER BY template_name");
    $birthdayTemplates = $stmt->fetchAll();
    
    echo "<p><strong>Count:</strong> " . count($birthdayTemplates) . " birthday templates found</p>";
    
    if (count($birthdayTemplates) > 0) {
        foreach ($birthdayTemplates as $template) {
            echo "<div style='border: 1px solid #ccc; margin: 10px 0; padding: 10px;'>";
            echo "<h4>" . htmlspecialchars($template['template_name']) . "</h4>";
            echo "<p><strong>Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";
            echo "<p><strong>Category:</strong> " . htmlspecialchars($template['template_category'] ?? 'N/A') . "</p>";
            echo "<p><strong>Content Preview:</strong></p>";
            echo "<div style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
            echo htmlspecialchars(substr($template['content'], 0, 500)) . (strlen($template['content']) > 500 ? '...' : '');
            echo "</div>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: red;'>No birthday templates found in database!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
