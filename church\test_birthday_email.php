<?php
require_once 'config.php';

try {
    // Test the sendEmail function
    $to = "<EMAIL>"; // Replace with a test email
    $toName = "Test User";
    $subject = "Test Birthday Email";
    $body = "<h1>This is a test email</h1><p>Testing the birthday email functionality.</p>";
    
    echo "<h3>Testing Email Sending:</h3>";
    
    if (sendEmail($to, $toName, $subject, $body)) {
        echo "<p style='color: green;'>Email sent successfully!</p>";
    } else {
        global $last_email_error;
        echo "<p style='color: red;'>Failed to send email. Error: " . ($last_email_error ?? 'Unknown error') . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?> 