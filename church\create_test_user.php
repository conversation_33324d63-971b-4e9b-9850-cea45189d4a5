<?php
require_once 'config.php';

try {
    // Check if user already exists
    $stmt = $pdo->prepare("SELECT id FROM members WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    
    if ($stmt->fetch()) {
        echo "Test user already exists!<br>";
    } else {
        // Create test user
        $hashedPassword = password_hash('password123', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("
            INSERT INTO members (full_name, email, phone_number, home_address, birth_date, occupation, password_hash, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW())
        ");

        $stmt->execute([
            'Test User',
            '<EMAIL>',
            '+1234567890',
            '123 Test Street',
            '1990-01-01',
            'Tester',
            $hashedPassword
        ]);
        
        echo "Test user created successfully!<br>";
        echo "Email: <EMAIL><br>";
        echo "Password: password123<br>";
    }
    
    // Also check events table
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM events WHERE is_active = 1");
    $stmt->execute();
    $eventCount = $stmt->fetch()['count'];
    echo "Active events in database: " . $eventCount . "<br>";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
