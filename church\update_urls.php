<?php
/**
 * URL Update Script
 * 
 * This script updates hardcoded URLs in the database to use placeholder variables
 * that will be replaced dynamically based on the environment configuration.
 */

// Include the configuration file
require_once 'config.php';

// Define SQL statements to update URLs
$sqlStatements = [
    // Update Church Logo URLs
    "UPDATE email_templates 
     SET content = REPLACE(
         content, 
         'https://freedomassemblydb.online/church/assets/images/banner.jpg', 
         '{church_logo}'
     )",
    
    // Update other hardcoded freedomassemblydb.online URLs
    "UPDATE email_templates 
     SET content = REPLACE(
         content, 
         'https://freedomassemblydb.online/church/', 
         '{site_url}/'
     )",
    
    // Update Church Logo URLs from freedomassemblychurch.org
    "UPDATE email_templates 
     SET content = REPLACE(
         content, 
         'https://freedomassemblychurch.org/wp-content/uploads/2023/10/church-logo.png', 
         '{church_logo}'
     )",
    
    // Update any other hardcoded URLs in template subject lines
    "UPDATE email_templates 
     SET subject = REPLACE(
         subject, 
         'https://freedomassemblydb.online/church/', 
         '{site_url}/'
     )",
    
    // Also update the whatsapp_templates table if it exists
    "UPDATE whatsapp_templates 
     SET content = REPLACE(
         content, 
         'https://freedomassemblydb.online/church/', 
         '{site_url}/'
     )
     WHERE content LIKE '%freedomassemblydb.online%'",
     
    // Update church email references
    "UPDATE settings 
     SET setting_value = 'church@' || SUBSTRING_INDEX(SITE_URL, '//', -1) 
     WHERE setting_key IN ('email_smtp_username', 'email_sender_email', 'admin_email') 
     AND setting_value LIKE '%freedomassemblydb.online%'"
];

echo "Starting URL update process...\n";

try {
    // Connect to database (using connection from config.php)
    $conn = $pdo;
    
    // Execute each SQL statement
    foreach ($sqlStatements as $index => $sql) {
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute();
        $count = $stmt->rowCount();
        
        echo "Query " . ($index + 1) . " executed. Rows affected: $count\n";
    }
    
    echo "\nURL update process completed successfully!\n";
    echo "All hardcoded URLs have been updated to use placeholder variables.\n";
    echo "These placeholders will be dynamically replaced based on your environment settings in environment.php.\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
} 