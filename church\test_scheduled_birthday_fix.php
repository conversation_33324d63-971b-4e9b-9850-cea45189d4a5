<?php
/**
 * Test Script for Scheduled Birthday Email Fix
 * 
 * This script tests the scheduled birthday email functionality with the fix for proper name handling.
 */

// Include necessary files
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/email_functions.php';

// Set up logging
$logFile = __DIR__ . '/logs/test_scheduled_birthday_fix.log';
file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test scheduled birthday email fix script started.' . PHP_EOL);

try {
    // Get a test member with a birthday
    $stmt = $pdo->prepare("SELECT id, full_name, email, birth_date FROM members WHERE email IS NOT NULL AND birth_date IS NOT NULL LIMIT 1");
    $stmt->execute();
    $member = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$member) {
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] No members found with valid email addresses and birth dates.' . PHP_EOL, FILE_APPEND);
        echo "No members found with valid email addresses and birth dates.\n";
        exit;
    }
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test member: ' . $member['full_name'] . ' <' . $member['email'] . '>' . PHP_EOL, FILE_APPEND);
    
    // Get a birthday template
    $stmt = $pdo->prepare("
        SELECT id, template_name, subject, content 
        FROM email_templates 
        WHERE template_category = 'birthday' 
        LIMIT 1
    ");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] No birthday template found.' . PHP_EOL, FILE_APPEND);
        echo "No birthday template found. Please create a birthday template first.\n";
        exit;
    }
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Using template: ' . $template['template_name'] . PHP_EOL, FILE_APPEND);
    
    // Create a test schedule in the database
    $stmt = $pdo->prepare("
        INSERT INTO email_schedules 
        (name, description, frequency, status, next_run, created_at, updated_at)
        VALUES 
        ('Test Birthday Name Fix', 'Testing birthday name placeholder replacement', 'once', 'active', NOW(), NOW(), NOW())
    ");
    $stmt->execute();
    $scheduleId = $pdo->lastInsertId();
    
    // Add schedule settings with the birthday template
    $stmt = $pdo->prepare("
        INSERT INTO email_schedule_settings
        (schedule_id, template_id, custom_subject, custom_content, track_opens, track_clicks)
        VALUES
        (?, ?, ?, ?, 1, 1)
    ");
    $stmt->execute([$scheduleId, $template['id'], $template['subject'], $template['content']]);
    
    // Add recipient to schedule
    $stmt = $pdo->prepare("
        INSERT INTO email_schedule_recipients
        (schedule_id, recipient_id, recipient_type, status)
        VALUES
        (?, ?, 'member', 'pending')
    ");
    $stmt->execute([$scheduleId, $member['id']]);
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Created test schedule with ID: ' . $scheduleId . PHP_EOL, FILE_APPEND);
    
    // Log the member data
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Member data: ' . print_r($member, true) . PHP_EOL, FILE_APPEND);
    
    // Create member data for placeholder replacement
    $memberData = [
        'id' => $member['id'],
        'full_name' => $member['full_name'],
        'first_name' => explode(' ', $member['full_name'])[0] ?? '',
        'last_name' => (count(explode(' ', $member['full_name'])) > 1) ? explode(' ', $member['full_name'], 2)[1] : '',
        'email' => $member['email'],
        'birth_date' => $member['birth_date'],
        'recipient_full_name' => $member['full_name'],
        'recipient_first_name' => explode(' ', $member['full_name'])[0] ?? '',
        'recipient_email' => $member['email']
    ];
    
    // Calculate birthday-related fields
    if (!empty($member['birth_date'])) {
        $birth_month = date('m', strtotime($member['birth_date']));
        $birth_day = date('d', strtotime($member['birth_date']));
        $birth_year = date('Y', strtotime($member['birth_date']));
        $current_year = date('Y');
        $next_year = $current_year + 1;
        
        // Determine if birthday has passed this year already
        $this_year_birthday = $current_year . '-' . $birth_month . '-' . $birth_day;
        $next_year_birthday = $next_year . '-' . $birth_month . '-' . $birth_day;
        
        // Use next year's date if this year's birthday has already passed
        $upcoming_date = strtotime($this_year_birthday) < time() ? $next_year_birthday : $this_year_birthday;
        
        // Calculate age on upcoming birthday
        $age = strtotime($this_year_birthday) < time() ? 
              ($next_year - $birth_year) : 
              ($current_year - $birth_year);
        
        // Add days until birthday
        $days_until = ceil((strtotime($upcoming_date) - time()) / 86400);
        
        // Add critical birthday values to the data array
        $memberData['upcoming_birthday_date'] = date('F j, Y', strtotime($upcoming_date));
        $memberData['upcoming_birthday_day'] = date('l', strtotime($upcoming_date));
        $memberData['upcoming_birthday_formatted'] = date('l, F j, Y', strtotime($upcoming_date));
        $memberData['days_until_birthday'] = $days_until;
        $memberData['days_text'] = $days_until == 0 ? 'today' : 
                                  ($days_until == 1 ? 'tomorrow' : 
                                  "in $days_until days");
        $memberData['age'] = $age;
        $memberData['birthday_member_age'] = $age;
        $memberData['birthday_member_name'] = $memberData['first_name'];
        $memberData['birthday_member_full_name'] = $memberData['full_name'];
    }
    
    // Get the schedule data
    $stmt = $pdo->prepare("
        SELECT s.id, s.name, s.status, ss.template_id, ss.custom_subject, ss.custom_content, ss.track_opens, ss.track_clicks
        FROM email_schedules s
        JOIN email_schedule_settings ss ON s.id = ss.schedule_id
        WHERE s.id = ?
    ");
    $stmt->execute([$scheduleId]);
    $schedule = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Prepare schedule data for the function
    $scheduleData = [
        'id' => $schedule['id'],
        'name' => $schedule['name'],
        'track_opens' => $schedule['track_opens'],
        'track_clicks' => $schedule['track_clicks']
    ];
    
    // Test the direct send using our updated function
    $subject = !empty($schedule['custom_subject']) ? $schedule['custom_subject'] : $template['subject'];
    $content = !empty($schedule['custom_content']) ? $schedule['custom_content'] : $template['content'];
    
    // Log the subject and content before sending
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Subject before sending: ' . $subject . PHP_EOL, FILE_APPEND);
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Content before sending (first 200 chars): ' . substr($content, 0, 200) . PHP_EOL, FILE_APPEND);
    
    // Send the test email
    $result = sendScheduledEmail(
        $member['email'],
        $member['full_name'],
        $subject,
        $content,
        $scheduleData,
        $memberData
    );
    
    if ($result) {
        echo "Test scheduled birthday email sent successfully to " . $member['full_name'] . " <" . $member['email'] . ">\n";
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test scheduled birthday email sent successfully.' . PHP_EOL, FILE_APPEND);
    } else {
        echo "Failed to send test scheduled birthday email to " . $member['full_name'] . " <" . $member['email'] . ">\n";
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Failed to send test scheduled birthday email.' . PHP_EOL, FILE_APPEND);
    }
    
    // Now run the scheduled email processor to test the full system
    echo "Running scheduled email processor...\n";
    require_once __DIR__ . '/cron/process_scheduled_emails.php';
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test scheduled birthday email fix script completed.' . PHP_EOL, FILE_APPEND);
    echo "Test completed. Check logs for details.\n";
    
} catch (Exception $e) {
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
    echo "Error: " . $e->getMessage() . "\n";
}
