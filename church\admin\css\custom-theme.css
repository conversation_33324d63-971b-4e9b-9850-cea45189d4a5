:root {
  --organization-name: Church Management System;
  --organization-type: church;
  --tagline: Connecting Faith, Building Community;
  --primary-color: #495057;
  --secondary-color: #6c757d;
  --accent-color: #28a745;
  --font-family: <PERSON>o, sans-serif;
  --logo-url: uploads/logos/logo_1751186159_6860faef0f979.png;
  --logo-width: 200px;
  --logo-height: 80;
  --logo-position: left;
  --show-logo-text: 1;
  --logo-text: ;
  --favicon-url: ;
  --hide-powered-by: 0;
  --custom-footer-text: ;
  --support-email: ;
  --support-phone: ;
  --support-url: ;
  --custom-domain: ;
  --ssl-enabled: 1;
  --meta-description: Church Management System for modern congregations;
  --meta-keywords: church, management, members, events, donations;
  --google-analytics-id: ;
  --facebook-pixel-id: ;
  --custom-css: ;
  --custom-head-code: ;
  --custom-body-code: ;
  --term-members: Members;
  --term-member: Member;
  --term-events: Events;
  --term-event: Event;
  --term-campaigns: Campaigns;
  --term-campaign: Campaign;
  --term-groups: Groups;
  --term-group: Group;
  --term-leaders: Leaders;
  --term-leader: Leader;
  --term-volunteers: Volunteers;
  --term-volunteer: Volunteer;
  --term-donations: Donations;
  --term-donation: Donation;
  --term-attendance: Attendance;
  --term-ministry: Ministry;
  --term-ministries: Ministries;
  --sidebar-background: #0066ff;
  --sidebar-text-color: #ffffff;
  --sidebar-hover-color: #000000;

  /* Sidebar-specific variables */
  --sidebar-bg-color: #0066ff;
  --sidebar-text-color: #ffffff;
  --sidebar-hover-color: #000000;
  --sidebar-width: 280px;
  --content-spacing: 30px;

  /* Bootstrap color overrides */
  --bs-primary: #495057;
  --bs-secondary: #6c757d;
}

/* Sidebar theming */
.sidebar.themed-sidebar {
  background-color: var(--sidebar-bg-color) !important;
  color: var(--sidebar-text-color) !important;
}

.sidebar.themed-sidebar .nav-link {
  color: var(--sidebar-text-color) !important;
}

.sidebar.themed-sidebar .nav-link:hover {
  background-color: var(--sidebar-hover-color) !important;
  color: var(--sidebar-text-color) !important;
}

.sidebar.themed-sidebar .sidebar-heading {
  color: var(--sidebar-text-color) !important;
}

.sidebar.themed-sidebar .sidebar-toggle-btn {
  color: var(--sidebar-text-color) !important;
}

/* Navbar theming for user pages */
.navbar {
  background: linear-gradient(135deg, var(--bs-primary, #495057) 0%, var(--bs-secondary, #6c757d) 100%) !important;
}

.navbar-brand {
  color: white !important;
}

.navbar-nav .nav-link {
  color: rgba(255,255,255,0.9) !important;
}

.navbar-nav .nav-link:hover {
  color: white !important;
}

.navbar-nav .nav-link.active {
  color: white !important;
  font-weight: 600;
}

