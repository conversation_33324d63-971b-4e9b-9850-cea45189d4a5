:root {
  --organization-name: Church Management System;
  --organization-type: church;
  --tagline: Connecting Faith, Building Community;
  --primary-color: #495057;
  --secondary-color: #007bff;
  --accent-color: #28a745;
  --font-family: Arial, sans-serif;
  --logo-url: uploads/logos/logo_1751186159_6860faef0f979.png;
  --logo-width: 200px;
  --logo-height: 80;
  --logo-position: left;
  --show-logo-text: 1;
  --logo-text: ;
  --favicon-url: ;
  --hide-powered-by: 0;
  --custom-footer-text: ;
  --support-email: ;
  --support-phone: ;
  --support-url: ;
  --custom-domain: ;
  --ssl-enabled: 1;
  --meta-description: Church Management System for modern congregations;
  --meta-keywords: church, management, members, events, donations;
  --google-analytics-id: ;
  --facebook-pixel-id: ;
  --custom-css: ;
  --custom-head-code: ;
  --custom-body-code: ;
  --term-members: Members;
  --term-member: Member;
  --term-events: Events;
  --term-event: Event;
  --term-campaigns: Campaigns;
  --term-campaign: Campaign;
  --term-groups: Groups;
  --term-group: Group;
  --term-leaders: Leaders;
  --term-leader: Leader;
  --term-volunteers: Volunteers;
  --term-volunteer: Volunteer;
  --term-donations: Donations;
  --term-donation: Donation;
  --term-attendance: Attendance;
  --term-ministry: Ministry;
  --term-ministries: Ministries;
  --sidebar-bg-color: #e67f0a;
  --sidebar-text-color: #000000;
  --sidebar-hover-color: #dc3545;

  /* Sidebar-specific variables */
  --sidebar-bg-color: #e67f0a;
  --sidebar-text-color: #000000;
  --sidebar-hover-color: #dc3545;
  --sidebar-width: 280px;
  --content-spacing: 30px;
}

/* Sidebar theming */
.sidebar.themed-sidebar {
  background-color: var(--sidebar-bg-color) !important;
  color: var(--sidebar-text-color) !important;
}

.sidebar.themed-sidebar .nav-link {
  color: var(--sidebar-text-color) !important;
}

.sidebar.themed-sidebar .nav-link:hover {
  background-color: var(--sidebar-hover-color) !important;
  color: var(--sidebar-text-color) !important;
}

.sidebar.themed-sidebar .sidebar-heading {
  color: var(--sidebar-text-color) !important;
}

.sidebar.themed-sidebar .sidebar-toggle-btn {
  color: var(--sidebar-text-color) !important;
}
