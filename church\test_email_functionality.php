<?php
/**
 * Email Functionality Test Script
 * Tests actual email sending <NAME_EMAIL>
 */

require_once 'config.php';

$TEST_EMAIL = '<EMAIL>';
$results = [];

// HTML Header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Functionality Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">📧 Email Functionality Test</h1>
        
        <div class="alert alert-info">
            <strong>Test Email:</strong> <?php echo $TEST_EMAIL; ?><br>
            <strong>Test Time:</strong> <?php echo date('Y-m-d H:i:s'); ?>
        </div>

        <?php
        // Test 1: Check if sendEmail function exists
        if (function_exists('sendEmail')) {
            echo "<div class='alert alert-success'>✅ sendEmail function is available</div>";
            
            // Test 2: Send welcome email
            try {
                $subject = "Church System Test - Welcome Email";
                $message = "
                <h2>Welcome to Freedom Assembly Church International</h2>
                <p>Dear Test Member,</p>
                <p>This is a test email from the Church Management System to verify email functionality.</p>
                <p>If you receive this email, the system is working correctly!</p>
                <p>Test Details:</p>
                <ul>
                    <li>Test Time: " . date('Y-m-d H:i:s') . "</li>
                    <li>Test Email: $TEST_EMAIL</li>
                    <li>System: Church Management System</li>
                </ul>
                <p>Blessings,<br>Church Management System</p>
                ";
                
                $emailSent = sendEmail($TEST_EMAIL, $subject, $message, 'Test Member');
                
                if ($emailSent) {
                    echo "<div class='alert alert-success'>✅ Welcome email sent successfully to $TEST_EMAIL</div>";
                } else {
                    echo "<div class='alert alert-danger'>❌ Failed to send welcome email</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Email sending error: " . $e->getMessage() . "</div>";
            }
            
            // Test 3: Send birthday email if it's the member's birthday
            try {
                $stmt = $pdo->prepare("SELECT * FROM members WHERE email = ?");
                $stmt->execute([$TEST_EMAIL]);
                $member = $stmt->fetch();
                
                if ($member) {
                    $today = date('m-d');
                    $memberBirthday = date('m-d', strtotime($member['birth_date']));
                    
                    if ($today === $memberBirthday) {
                        echo "<div class='alert alert-info'>🎂 It's the test member's birthday today! Sending birthday email...</div>";
                        
                        $birthdaySubject = "🎉 Happy Birthday from Freedom Assembly Church!";
                        $birthdayMessage = "
                        <h2>🎂 Happy Birthday, {$member['full_name']}!</h2>
                        <p>Dear {$member['full_name']},</p>
                        <p>On this special day, we want to celebrate you and thank God for your life!</p>
                        <p>May this new year of your life be filled with God's blessings, joy, and prosperity.</p>
                        <p>We are grateful to have you as part of our church family.</p>
                        <p>Birthday Blessings:</p>
                        <ul>
                            <li>🙏 May God's favor be upon you</li>
                            <li>❤️ May you experience His love in new ways</li>
                            <li>🌟 May your dreams and aspirations come to pass</li>
                            <li>🎁 May this year bring you closer to your purpose</li>
                        </ul>
                        <p>Have a wonderful birthday celebration!</p>
                        <p>With love and prayers,<br>Freedom Assembly Church International<br>Men's Visionaire and Billionaire Ministry</p>
                        ";
                        
                        $birthdayEmailSent = sendEmail($TEST_EMAIL, $birthdaySubject, $birthdayMessage, $member['full_name']);
                        
                        if ($birthdayEmailSent) {
                            echo "<div class='alert alert-success'>✅ Birthday email sent successfully!</div>";
                        } else {
                            echo "<div class='alert alert-danger'>❌ Failed to send birthday email</div>";
                        }
                    } else {
                        echo "<div class='alert alert-warning'>⚠️ Not the test member's birthday today (Birthday: $memberBirthday, Today: $today)</div>";
                    }
                } else {
                    echo "<div class='alert alert-warning'>⚠️ Test member not found in database</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Birthday email test error: " . $e->getMessage() . "</div>";
            }
            
            // Test 4: Test template system
            try {
                $stmt = $pdo->query("SELECT * FROM email_templates LIMIT 1");
                $template = $stmt->fetch();
                
                if ($template) {
                    echo "<div class='alert alert-info'>📧 Testing email template: {$template['template_name']}</div>";
                    
                    $templateSubject = "Template Test - " . $template['template_name'];
                    $templateMessage = $template['template_content'];
                    
                    // Replace placeholders if function exists
                    if (function_exists('replaceTemplatePlaceholders')) {
                        $memberData = [
                            'full_name' => 'Test Member',
                            'email' => $TEST_EMAIL,
                            'member_image' => 'default.jpg'
                        ];
                        $templateMessage = replaceTemplatePlaceholders($templateMessage, $memberData);
                    }
                    
                    $templateEmailSent = sendEmail($TEST_EMAIL, $templateSubject, $templateMessage, 'Test Member');
                    
                    if ($templateEmailSent) {
                        echo "<div class='alert alert-success'>✅ Template email sent successfully!</div>";
                    } else {
                        echo "<div class='alert alert-danger'>❌ Failed to send template email</div>";
                    }
                } else {
                    echo "<div class='alert alert-warning'>⚠️ No email templates found in database</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Template test error: " . $e->getMessage() . "</div>";
            }
            
        } else {
            echo "<div class='alert alert-danger'>❌ sendEmail function not found</div>";
        }
        
        // Test 5: Check email logs
        try {
            $stmt = $pdo->prepare("SELECT * FROM email_logs WHERE recipient_email = ? ORDER BY sent_at DESC LIMIT 5");
            $stmt->execute([$TEST_EMAIL]);
            $emailLogs = $stmt->fetchAll();
            
            if ($emailLogs) {
                echo "<div class='alert alert-info'>📊 Recent email logs for $TEST_EMAIL:</div>";
                echo "<div class='table-responsive'>";
                echo "<table class='table table-striped'>";
                echo "<thead><tr><th>Date</th><th>Subject</th><th>Status</th><th>Template</th></tr></thead>";
                echo "<tbody>";
                foreach ($emailLogs as $log) {
                    $statusClass = $log['status'] === 'sent' ? 'success' : 'danger';
                    echo "<tr>";
                    echo "<td>" . date('Y-m-d H:i', strtotime($log['sent_at'])) . "</td>";
                    echo "<td>" . htmlspecialchars($log['subject']) . "</td>";
                    echo "<td><span class='badge bg-$statusClass'>" . $log['status'] . "</span></td>";
                    echo "<td>" . ($log['template_used'] ?: 'N/A') . "</td>";
                    echo "</tr>";
                }
                echo "</tbody></table></div>";
            } else {
                echo "<div class='alert alert-warning'>⚠️ No email logs found for $TEST_EMAIL</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>❌ Email logs check error: " . $e->getMessage() . "</div>";
        }
        
        // Test 6: SMTP Configuration Check
        try {
            $stmt = $pdo->query("SELECT * FROM settings WHERE setting_key LIKE 'smtp_%'");
            $smtpSettings = $stmt->fetchAll();
            
            if ($smtpSettings) {
                echo "<div class='alert alert-info'>⚙️ SMTP Configuration:</div>";
                echo "<div class='table-responsive'>";
                echo "<table class='table table-sm'>";
                foreach ($smtpSettings as $setting) {
                    $value = $setting['setting_key'] === 'smtp_password' ? '***hidden***' : $setting['setting_value'];
                    echo "<tr><td>" . $setting['setting_key'] . "</td><td>" . $value . "</td></tr>";
                }
                echo "</table></div>";
            } else {
                echo "<div class='alert alert-warning'>⚠️ No SMTP settings found</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>❌ SMTP settings check error: " . $e->getMessage() . "</div>";
        }
        ?>
        
        <div class="mt-4 text-center">
            <a href="comprehensive_test.php" class="btn btn-primary">Run Full Test Suite</a>
            <a href="admin/index.php" class="btn btn-secondary">Admin Panel</a>
            <button onclick="window.location.reload()" class="btn btn-success">Rerun Email Tests</button>
        </div>
        
        <div class="alert alert-info mt-4">
            <h5>📋 Email Test Instructions:</h5>
            <ol>
                <li>Check your email inbox at <strong><?php echo $TEST_EMAIL; ?></strong></li>
                <li>Look for test emails sent from this system</li>
                <li>Verify that emails are properly formatted and contain correct information</li>
                <li>Check spam/junk folder if emails are not in inbox</li>
                <li>If no emails received, check SMTP configuration in admin panel</li>
            </ol>
        </div>
    </div>
</body>
</html>
