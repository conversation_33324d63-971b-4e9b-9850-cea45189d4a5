<?php
require_once 'config.php';

// Get recent members
$stmt = $pdo->prepare("SELECT id, full_name, email, created_at, image_path FROM members ORDER BY created_at DESC LIMIT 5");
$stmt->execute();
$recentMembers = $stmt->fetchAll();

echo "<h1>Recent Members Image Debug</h1>";

foreach ($recentMembers as $member) {
    echo "<div style='margin-bottom: 20px; padding: 10px; border: 1px solid #ccc;'>";
    echo "<h3>" . htmlspecialchars($member['full_name']) . "</h3>";
    
    $imagePath = $member['image_path'] ?? '';
    echo "Original image path: " . htmlspecialchars($imagePath) . "<br>";
    
    // Remove any domain prefix if it exists
    $imagePath = preg_replace('/^https?:\/\/[^\/]+\//', '', $imagePath);
    echo "After domain removal: " . htmlspecialchars($imagePath) . "<br>";
    
    $fullPath = "../" . $imagePath;
    echo "Full path to check: " . htmlspecialchars($fullPath) . "<br>";
    echo "File exists check: " . (file_exists($fullPath) ? "Yes" : "No") . "<br>";
    
    if (!empty($imagePath) && file_exists($fullPath)) {
        echo "<img src='../" . htmlspecialchars($imagePath) . "' alt='" . htmlspecialchars($member['full_name']) . "' style='max-width: 100px;'><br>";
        echo "Image displayed using path: ../" . htmlspecialchars($imagePath);
    } else {
        echo "<img src='../assets/img/default-profile.jpg' alt='" . htmlspecialchars($member['full_name']) . "' style='max-width: 100px;'><br>";
        echo "Using default image";
    }
    
    echo "</div>";
}
?> 