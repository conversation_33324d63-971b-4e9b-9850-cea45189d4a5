<?php
/**
 * Comprehensive Test Script for User Authentication System
 * 
 * Tests all user authentication, profile management, and settings functionality
 */

// Include configuration and classes
require_once 'config.php';
require_once 'classes/SecurityManager.php';
require_once 'classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Test results array
$testResults = [];
$testsPassed = 0;
$testsFailed = 0;

function runTest($testName, $testFunction) {
    global $testResults, $testsPassed, $testsFailed;
    
    try {
        $result = $testFunction();
        if ($result['success']) {
            $testResults[] = [
                'name' => $testName,
                'status' => 'PASS',
                'message' => $result['message'],
                'details' => $result['details'] ?? ''
            ];
            $testsPassed++;
        } else {
            $testResults[] = [
                'name' => $testName,
                'status' => 'FAIL',
                'message' => $result['message'],
                'details' => $result['details'] ?? ''
            ];
            $testsFailed++;
        }
    } catch (Exception $e) {
        $testResults[] = [
            'name' => $testName,
            'status' => 'ERROR',
            'message' => $e->getMessage(),
            'details' => ''
        ];
        $testsFailed++;
    }
}

// Test 1: Database Schema Verification
runTest("Database Schema Verification", function() use ($pdo) {
    $requiredTables = ['members', 'user_sessions', 'user_activity_log', 'user_preferences', 'events', 'event_rsvps', 'birthday_templates'];
    $missingTables = [];
    
    foreach ($requiredTables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if (!$stmt->fetch()) {
            $missingTables[] = $table;
        }
    }
    
    if (empty($missingTables)) {
        return ['success' => true, 'message' => 'All required tables exist', 'details' => implode(', ', $requiredTables)];
    } else {
        return ['success' => false, 'message' => 'Missing tables: ' . implode(', ', $missingTables)];
    }
});

// Test 2: Members Table Authentication Columns
runTest("Members Table Authentication Columns", function() use ($pdo) {
    $requiredColumns = ['password_hash', 'last_login_at', 'failed_login_attempts', 'account_locked_until', 'must_change_password', 'password_reset_token', 'password_reset_expires', 'is_active'];
    $missingColumns = [];
    
    $stmt = $pdo->prepare("DESCRIBE members");
    $stmt->execute();
    $existingColumns = array_column($stmt->fetchAll(), 'Field');
    
    foreach ($requiredColumns as $column) {
        if (!in_array($column, $existingColumns)) {
            $missingColumns[] = $column;
        }
    }
    
    if (empty($missingColumns)) {
        return ['success' => true, 'message' => 'All authentication columns exist', 'details' => implode(', ', $requiredColumns)];
    } else {
        return ['success' => false, 'message' => 'Missing columns: ' . implode(', ', $missingColumns)];
    }
});

// Test 3: UserAuthManager Class Methods
runTest("UserAuthManager Class Methods", function() use ($userAuth) {
    $requiredMethods = ['authenticateUser', 'findUserByIdentifier', 'resetPassword', 'changePassword', 'createUserAccount', 'updateProfile', 'getUserPreferences', 'setUserPreference', 'getUserById', 'isAuthenticated'];
    $missingMethods = [];
    
    $reflection = new ReflectionClass($userAuth);
    $existingMethods = array_map(function($method) { return $method->getName(); }, $reflection->getMethods());
    
    foreach ($requiredMethods as $method) {
        if (!in_array($method, $existingMethods)) {
            $missingMethods[] = $method;
        }
    }
    
    if (empty($missingMethods)) {
        return ['success' => true, 'message' => 'All required methods exist', 'details' => implode(', ', $requiredMethods)];
    } else {
        return ['success' => false, 'message' => 'Missing methods: ' . implode(', ', $missingMethods)];
    }
});

// Test 4: Test User Creation
runTest("Test User Creation", function() use ($userAuth) {
    $testEmail = 'test_user_' . time() . '@example.com';
    $testData = [
        'full_name' => 'Test User',
        'email' => $testEmail,
        'phone_number' => '555-0123',
        'password' => 'TestPassword123!',
        'birth_date' => '1990-01-01',
        'home_address' => '123 Test St',
        'occupation' => 'Tester'
    ];
    
    $result = $userAuth->createUserAccount($testData);
    
    if ($result['success']) {
        // Store test user ID for cleanup
        $GLOBALS['testUserId'] = $result['user_id'];
        return ['success' => true, 'message' => 'Test user created successfully', 'details' => "User ID: {$result['user_id']}, Email: $testEmail"];
    } else {
        return ['success' => false, 'message' => 'Failed to create test user: ' . $result['message']];
    }
});

// Test 5: User Authentication
runTest("User Authentication", function() use ($userAuth) {
    if (!isset($GLOBALS['testUserId'])) {
        return ['success' => false, 'message' => 'No test user available for authentication test'];
    }
    
    // Get test user data
    $testUser = $userAuth->getUserById($GLOBALS['testUserId']);
    if (!$testUser) {
        return ['success' => false, 'message' => 'Could not retrieve test user data'];
    }
    
    // Test authentication with email
    $authResult = $userAuth->authenticateUser($testUser['email'], 'TestPassword123!');
    
    if ($authResult['success']) {
        return ['success' => true, 'message' => 'User authentication successful', 'details' => "Authenticated user: {$testUser['email']}"];
    } else {
        return ['success' => false, 'message' => 'Authentication failed: ' . $authResult['message']];
    }
});

// Test 6: Profile Update
runTest("Profile Update", function() use ($userAuth) {
    if (!isset($GLOBALS['testUserId'])) {
        return ['success' => false, 'message' => 'No test user available for profile update test'];
    }
    
    $updateData = [
        'full_name' => 'Updated Test User',
        'occupation' => 'Senior Tester',
        'home_address' => '456 Updated Ave'
    ];
    
    $result = $userAuth->updateProfile($GLOBALS['testUserId'], $updateData);
    
    if ($result['success']) {
        // Verify the update
        $updatedUser = $userAuth->getUserById($GLOBALS['testUserId']);
        if ($updatedUser['full_name'] === 'Updated Test User') {
            return ['success' => true, 'message' => 'Profile update successful', 'details' => 'Name updated to: ' . $updatedUser['full_name']];
        } else {
            return ['success' => false, 'message' => 'Profile update verification failed'];
        }
    } else {
        return ['success' => false, 'message' => 'Profile update failed: ' . $result['message']];
    }
});

// Test 7: User Preferences
runTest("User Preferences", function() use ($userAuth) {
    if (!isset($GLOBALS['testUserId'])) {
        return ['success' => false, 'message' => 'No test user available for preferences test'];
    }
    
    // Set a preference
    $setPref = $userAuth->setUserPreference($GLOBALS['testUserId'], 'email_notifications', true, 'boolean');
    if (!$setPref) {
        return ['success' => false, 'message' => 'Failed to set user preference'];
    }
    
    // Get preferences
    $preferences = $userAuth->getUserPreferences($GLOBALS['testUserId']);
    
    if (isset($preferences['email_notifications']) && $preferences['email_notifications'] === true) {
        return ['success' => true, 'message' => 'User preferences working correctly', 'details' => 'email_notifications set to true'];
    } else {
        return ['success' => false, 'message' => 'User preferences not working correctly'];
    }
});

// Test 8: Password Reset Token Generation
runTest("Password Reset Token", function() use ($userAuth) {
    if (!isset($GLOBALS['testUserId'])) {
        return ['success' => false, 'message' => 'No test user available for password reset test'];
    }
    
    $testUser = $userAuth->getUserById($GLOBALS['testUserId']);
    $result = $userAuth->resetPassword($testUser['email']);
    
    if ($result['success']) {
        return ['success' => true, 'message' => 'Password reset token generated', 'details' => 'Reset process initiated for: ' . $testUser['email']];
    } else {
        return ['success' => false, 'message' => 'Password reset failed: ' . $result['message']];
    }
});

// Test 9: File Structure Verification
runTest("User Interface Files", function() {
    $requiredFiles = [
        'user/login.php',
        'user/dashboard.php',
        'user/profile.php',
        'user/settings.php',
        'user/change_password.php',
        'user/forgot_password.php',
        'user/reset_password.php',
        'user/logout.php',
        'user/events.php'
    ];
    
    $missingFiles = [];
    foreach ($requiredFiles as $file) {
        if (!file_exists($file)) {
            $missingFiles[] = $file;
        }
    }
    
    if (empty($missingFiles)) {
        return ['success' => true, 'message' => 'All user interface files exist', 'details' => count($requiredFiles) . ' files verified'];
    } else {
        return ['success' => false, 'message' => 'Missing files: ' . implode(', ', $missingFiles)];
    }
});

// Test 10: Security Features
runTest("Security Features", function() use ($security) {
    $securityTests = [];
    
    // Test CSRF token generation
    $csrfToken = $security->getCSRFToken();
    if (!empty($csrfToken)) {
        $securityTests[] = 'CSRF token generation: PASS';
    } else {
        $securityTests[] = 'CSRF token generation: FAIL';
    }
    
    // Test input validation
    $validEmail = $security->validateInput('<EMAIL>', 'email');
    if ($validEmail) {
        $securityTests[] = 'Email validation: PASS';
    } else {
        $securityTests[] = 'Email validation: FAIL';
    }
    
    // Test password hashing
    $hashedPassword = $security->hashPassword('TestPassword123!');
    if (!empty($hashedPassword) && password_verify('TestPassword123!', $hashedPassword)) {
        $securityTests[] = 'Password hashing: PASS';
    } else {
        $securityTests[] = 'Password hashing: FAIL';
    }
    
    $failedTests = array_filter($securityTests, function($test) {
        return strpos($test, 'FAIL') !== false;
    });
    
    if (empty($failedTests)) {
        return ['success' => true, 'message' => 'All security features working', 'details' => implode(', ', $securityTests)];
    } else {
        return ['success' => false, 'message' => 'Some security tests failed', 'details' => implode(', ', $failedTests)];
    }
});

// Run all tests
echo "<!DOCTYPE html>\n";
echo "<html lang='en'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "    <title>User System Test Results</title>\n";
echo "    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>\n";
echo "    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css' rel='stylesheet'>\n";
echo "</head>\n";
echo "<body class='bg-light'>\n";
echo "    <div class='container mt-4'>\n";
echo "        <div class='row'>\n";
echo "            <div class='col-12'>\n";
echo "                <div class='card'>\n";
echo "                    <div class='card-header bg-primary text-white'>\n";
echo "                        <h2><i class='bi bi-check-circle'></i> User Authentication System Test Results</h2>\n";
echo "                    </div>\n";
echo "                    <div class='card-body'>\n";

// Display summary
echo "                        <div class='row mb-4'>\n";
echo "                            <div class='col-md-4'>\n";
echo "                                <div class='card bg-success text-white'>\n";
echo "                                    <div class='card-body text-center'>\n";
echo "                                        <h3>$testsPassed</h3>\n";
echo "                                        <p>Tests Passed</p>\n";
echo "                                    </div>\n";
echo "                                </div>\n";
echo "                            </div>\n";
echo "                            <div class='col-md-4'>\n";
echo "                                <div class='card bg-danger text-white'>\n";
echo "                                    <div class='card-body text-center'>\n";
echo "                                        <h3>$testsFailed</h3>\n";
echo "                                        <p>Tests Failed</p>\n";
echo "                                    </div>\n";
echo "                                </div>\n";
echo "                            </div>\n";
echo "                            <div class='col-md-4'>\n";
echo "                                <div class='card bg-info text-white'>\n";
echo "                                    <div class='card-body text-center'>\n";
echo "                                        <h3>" . ($testsPassed + $testsFailed) . "</h3>\n";
echo "                                        <p>Total Tests</p>\n";
echo "                                    </div>\n";
echo "                                </div>\n";
echo "                            </div>\n";
echo "                        </div>\n";

// Display detailed results
echo "                        <h4>Detailed Test Results</h4>\n";
echo "                        <div class='table-responsive'>\n";
echo "                            <table class='table table-striped'>\n";
echo "                                <thead>\n";
echo "                                    <tr>\n";
echo "                                        <th>Test Name</th>\n";
echo "                                        <th>Status</th>\n";
echo "                                        <th>Message</th>\n";
echo "                                        <th>Details</th>\n";
echo "                                    </tr>\n";
echo "                                </thead>\n";
echo "                                <tbody>\n";

foreach ($testResults as $result) {
    $statusClass = '';
    $statusIcon = '';
    
    switch ($result['status']) {
        case 'PASS':
            $statusClass = 'text-success';
            $statusIcon = 'bi-check-circle';
            break;
        case 'FAIL':
            $statusClass = 'text-danger';
            $statusIcon = 'bi-x-circle';
            break;
        case 'ERROR':
            $statusClass = 'text-warning';
            $statusIcon = 'bi-exclamation-triangle';
            break;
    }
    
    echo "                                    <tr>\n";
    echo "                                        <td>" . htmlspecialchars($result['name']) . "</td>\n";
    echo "                                        <td class='$statusClass'><i class='bi $statusIcon'></i> " . htmlspecialchars($result['status']) . "</td>\n";
    echo "                                        <td>" . htmlspecialchars($result['message']) . "</td>\n";
    echo "                                        <td><small class='text-muted'>" . htmlspecialchars($result['details']) . "</small></td>\n";
    echo "                                    </tr>\n";
}

echo "                                </tbody>\n";
echo "                            </table>\n";
echo "                        </div>\n";

// Cleanup test user
if (isset($GLOBALS['testUserId'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM members WHERE id = ?");
        $stmt->execute([$GLOBALS['testUserId']]);
        
        $stmt = $pdo->prepare("DELETE FROM user_preferences WHERE user_id = ?");
        $stmt->execute([$GLOBALS['testUserId']]);
        
        echo "                        <div class='alert alert-info mt-3'>\n";
        echo "                            <i class='bi bi-info-circle'></i> Test user (ID: {$GLOBALS['testUserId']}) has been cleaned up.\n";
        echo "                        </div>\n";
    } catch (Exception $e) {
        echo "                        <div class='alert alert-warning mt-3'>\n";
        echo "                            <i class='bi bi-exclamation-triangle'></i> Warning: Could not clean up test user: " . htmlspecialchars($e->getMessage()) . "\n";
        echo "                        </div>\n";
    }
}

echo "                        <div class='mt-4'>\n";
echo "                            <a href='admin/' class='btn btn-primary'><i class='bi bi-gear'></i> Go to Admin Panel</a>\n";
echo "                            <a href='user/login.php' class='btn btn-success'><i class='bi bi-person'></i> Test User Login</a>\n";
echo "                            <a href='index.php' class='btn btn-info'><i class='bi bi-house'></i> Registration Page</a>\n";
echo "                        </div>\n";

echo "                    </div>\n";
echo "                </div>\n";
echo "            </div>\n";
echo "        </div>\n";
echo "    </div>\n";
echo "</body>\n";
echo "</html>\n";
?>
