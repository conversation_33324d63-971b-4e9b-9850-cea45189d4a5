<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

echo "<h2>SMS Database Migration</h2>";
echo "<p>Running SMS database migration...</p>";

// Read the migration file
$migrationFile = __DIR__ . '/migrations/create_ab_tests_table.sql';
if (!file_exists($migrationFile)) {
    die("Migration file not found: $migrationFile");
}

$sql = file_get_contents($migrationFile);

// Split the SQL into individual statements
$statements = array_filter(array_map('trim', explode(';', $sql)));

$successCount = 0;
$errorCount = 0;
$errors = [];

echo "<div style='font-family: monospace; background: #f5f5f5; padding: 10px; margin: 10px 0;'>";

foreach ($statements as $statement) {
    if (empty($statement) || strpos($statement, '--') === 0) {
        continue;
    }
    
    try {
        $pdo->exec($statement);
        echo "<span style='color: green;'>✓</span> Executed: " . substr($statement, 0, 50) . "...<br>";
        $successCount++;
    } catch (PDOException $e) {
        echo "<span style='color: red;'>✗</span> Error: " . $e->getMessage() . "<br>";
        echo "   Statement: " . substr($statement, 0, 100) . "...<br>";
        $errorCount++;
        $errors[] = $e->getMessage();
    }
}

echo "</div>";

echo "<h3>Migration Results:</h3>";
echo "<p><strong>Successful statements:</strong> $successCount</p>";
echo "<p><strong>Failed statements:</strong> $errorCount</p>";

if ($errorCount > 0) {
    echo "<h4>Errors:</h4>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    echo "</ul>";
}

// Test if SMS tables were created
echo "<h3>Verifying SMS Tables:</h3>";
$tables = ['sms_templates', 'sms_campaigns', 'sms_campaign_recipients', 'sms_logs'];

foreach ($tables as $table) {
    try {
        $stmt = $pdo->query("DESCRIBE $table");
        echo "<span style='color: green;'>✓</span> Table '$table' exists<br>";
    } catch (PDOException $e) {
        echo "<span style='color: red;'>✗</span> Table '$table' missing: " . $e->getMessage() . "<br>";
    }
}

echo "<br><a href='bulk_sms.php'>Go to Bulk SMS</a> | <a href='sms_templates.php'>Go to SMS Templates</a> | <a href='sms_campaigns.php'>Go to SMS Campaigns</a>";
?>
