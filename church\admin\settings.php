<?php
session_start();
require_once "../config.php";

// Include language system
require_once 'includes/language.php';

// Check if admin is logged in
if (!isset($_SESSION["admin_id"])) {
    header("Location: login.php");
    exit();
}

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST["action"])) {
    try {
        if ($_POST["action"] === "update_setting") {
            $stmt = $pdo->prepare("
                INSERT INTO settings (setting_key, setting_value) 
                VALUES (?, ?) 
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
            ");
            $stmt->execute([$_POST["setting_key"], $_POST["setting_value"]]);
            $success = __('setting_updated_successfully');
        }
    } catch (Exception $e) {
        $error = __('error') . ": " . $e->getMessage();
    }
}

// Get current settings
try {
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings ORDER BY setting_key");
    $stmt->execute();
    $allSettings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
} catch (Exception $e) {
    $error = __('error_loading_settings') . ": " . $e->getMessage();
    $allSettings = [];
}

// Organize settings by category
$settingsCategories = [
    'organization' => [
        'site_title' => __('site_title'),
        'organization_name' => __('organization_name'),
        'organization_type' => __('organization_type'),
        'admin_title' => __('admin_panel_title'),
        'member_term' => __('member_term'),
        'leader_term' => __('leader_term'),
        'group_term' => __('group_term'),
        'event_term' => __('event_term'),
        'donation_term' => __('donation_term')
    ],
    'system' => [
        'timezone' => __('timezone'),
        'date_format' => __('date_format'),
        'time_format' => __('time_format'),
        'currency_symbol' => __('currency_symbol'),
        'currency_code' => __('currency_code'),
        'language' => __('language'),
        'items_per_page' => __('items_per_page'),
        'session_timeout' => __('session_timeout_seconds'),
        'max_upload_size' => __('max_upload_size_mb'),
        'backup_retention_days' => __('backup_retention_days')
    ],
    'notifications' => [
        'birthday_notification_days' => __('birthday_notification_days'),
        'event_reminder_days' => __('event_reminder_days'),
        'membership_expiry_days' => __('membership_expiry_days'),
        'notification_frequency' => __('notification_frequency')
    ],
    'integrations' => [
        'payment_gateway' => __('payment_gateway'),
        'google_analytics_id' => __('google_analytics_id'),
        'facebook_pixel_id' => __('facebook_pixel_id')
    ]
];

$pageTitle = __('general_settings');
include "includes/header.php";
?>

        <div class="container-fluid">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="bi bi-gear"></i> <?php _e('general_settings'); ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="security_settings.php" class="btn btn-outline-secondary">
                            <i class="bi bi-shield-lock"></i> <?php _e('security_settings'); ?>
                        </a>
                        <a href="automated_email_templates.php" class="btn btn-outline-secondary">
                            <i class="bi bi-envelope-gear"></i> <?php _e('email_templates'); ?>
                        </a>
                    </div>
                </div>
            </div>

            <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="organization-tab" data-bs-toggle="tab" data-bs-target="#organization" type="button" role="tab">
                        <i class="bi bi-building"></i> <?php _e('organization'); ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                        <i class="bi bi-gear"></i> <?php _e('system'); ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                        <i class="bi bi-bell"></i> <?php _e('notifications'); ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="integrations-tab" data-bs-toggle="tab" data-bs-target="#integrations" type="button" role="tab">
                        <i class="bi bi-plug"></i> <?php _e('integrations'); ?>
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="settingsTabContent">
                <?php foreach ($settingsCategories as $categoryKey => $categorySettings): ?>
                    <div class="tab-pane fade <?php echo $categoryKey === 'organization' ? 'show active' : ''; ?>" 
                         id="<?php echo $categoryKey; ?>" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5><i class="bi bi-<?php echo $categoryKey === 'organization' ? 'building' : ($categoryKey === 'system' ? 'gear' : ($categoryKey === 'notifications' ? 'bell' : 'plug')); ?>"></i>
                                    <?php _e($categoryKey); ?> <?php _e('settings'); ?></h5>
                            </div>
                            <div class="card-body">
                                <?php foreach ($categorySettings as $settingKey => $settingLabel): ?>
                                    <form method="post" class="mb-4">
                                        <input type="hidden" name="action" value="update_setting">
                                        <input type="hidden" name="setting_key" value="<?php echo htmlspecialchars($settingKey); ?>">
                                        
                                        <div class="row align-items-end">
                                            <div class="col-md-3">
                                                <label class="form-label">
                                                    <strong><?php echo htmlspecialchars($settingLabel); ?></strong>
                                                </label>
                                            </div>
                                            <div class="col-md-6">
                                                <?php 
                                                $currentValue = $allSettings[$settingKey] ?? '';
                                                if ($settingKey === 'organization_type'): ?>
                                                    <select name="setting_value" class="form-select">
                                                        <option value="church" <?php echo $currentValue === 'church' ? 'selected' : ''; ?>><?php _e('church'); ?></option>
                                                        <option value="school" <?php echo $currentValue === 'school' ? 'selected' : ''; ?>><?php _e('school'); ?></option>
                                                        <option value="business" <?php echo $currentValue === 'business' ? 'selected' : ''; ?>><?php _e('business'); ?></option>
                                                        <option value="nonprofit" <?php echo $currentValue === 'nonprofit' ? 'selected' : ''; ?>><?php _e('nonprofit'); ?></option>
                                                        <option value="organization" <?php echo $currentValue === 'organization' ? 'selected' : ''; ?>><?php _e('organization_option'); ?></option>
                                                    </select>
                                                <?php elseif ($settingKey === 'language'): ?>
                                                    <select name="setting_value" class="form-select">
                                                        <option value="en" <?php echo $currentValue === 'en' ? 'selected' : ''; ?>><?php _e('english'); ?></option>
                                                        <option value="es" <?php echo $currentValue === 'es' ? 'selected' : ''; ?>><?php _e('spanish'); ?></option>
                                                    </select>
                                                <?php elseif (in_array($settingKey, ['items_per_page', 'session_timeout', 'max_upload_size', 'backup_retention_days', 'birthday_notification_days', 'event_reminder_days', 'membership_expiry_days'])): ?>
                                                    <input type="number" name="setting_value" class="form-control"
                                                           value="<?php echo htmlspecialchars($currentValue); ?>"
                                                           placeholder="<?php _e('enter'); ?> <?php echo strtolower($settingLabel); ?>">
                                                <?php else: ?>
                                                    <input type="text" name="setting_value" class="form-control"
                                                           value="<?php echo htmlspecialchars($currentValue); ?>"
                                                           placeholder="<?php _e('enter'); ?> <?php echo strtolower($settingLabel); ?>">
                                                <?php endif; ?>
                                            </div>
                                            <div class="col-md-3">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="bi bi-check-lg"></i> <?php _e('update'); ?>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                    <hr>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Links to related settings -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-link-45deg"></i> <?php _e('related_settings'); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="security_settings.php" class="btn btn-outline-primary">
                                    <i class="bi bi-shield-lock"></i> <?php _e('security_settings'); ?>
                                    <small class="d-block text-muted"><?php _e('security_settings_description'); ?></small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="appearance_settings.php" class="btn btn-outline-primary">
                                    <i class="bi bi-palette"></i> <?php _e('appearance_settings'); ?>
                                    <small class="d-block text-muted"><?php _e('appearance_settings_description'); ?></small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="email_settings.php" class="btn btn-outline-primary">
                                    <i class="bi bi-envelope-gear"></i> <?php _e('email_settings'); ?>
                                    <small class="d-block text-muted"><?php _e('email_settings_description'); ?></small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include "includes/footer.php"; ?>
