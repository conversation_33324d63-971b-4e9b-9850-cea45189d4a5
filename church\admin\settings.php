<?php
session_start();
require_once "../config.php";

// Check if admin is logged in
if (!isset($_SESSION["admin_id"])) {
    header("Location: login.php");
    exit();
}

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST["action"])) {
    try {
        if ($_POST["action"] === "update_setting") {
            $stmt = $pdo->prepare("
                INSERT INTO settings (setting_key, setting_value) 
                VALUES (?, ?) 
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
            ");
            $stmt->execute([$_POST["setting_key"], $_POST["setting_value"]]);
            $success = "Setting updated successfully!";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get current settings
try {
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings ORDER BY setting_key");
    $stmt->execute();
    $allSettings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
} catch (Exception $e) {
    $error = "Error loading settings: " . $e->getMessage();
    $allSettings = [];
}

// Organize settings by category
$settingsCategories = [
    'organization' => [
        'site_title' => 'Site Title',
        'organization_name' => 'Organization Name',
        'organization_type' => 'Organization Type',
        'admin_title' => 'Admin Panel Title',
        'member_term' => 'Member Term',
        'leader_term' => 'Leader Term',
        'group_term' => 'Group Term',
        'event_term' => 'Event Term',
        'donation_term' => 'Donation Term'
    ],
    'system' => [
        'timezone' => 'Timezone',
        'date_format' => 'Date Format',
        'time_format' => 'Time Format',
        'currency_symbol' => 'Currency Symbol',
        'currency_code' => 'Currency Code',
        'language' => 'Language',
        'items_per_page' => 'Items Per Page',
        'session_timeout' => 'Session Timeout (seconds)',
        'max_upload_size' => 'Max Upload Size (MB)',
        'backup_retention_days' => 'Backup Retention (days)'
    ],
    'notifications' => [
        'birthday_notification_days' => 'Birthday Notification Days',
        'event_reminder_days' => 'Event Reminder Days',
        'membership_expiry_days' => 'Membership Expiry Days',
        'notification_frequency' => 'Notification Frequency'
    ],
    'integrations' => [
        'payment_gateway' => 'Payment Gateway',
        'google_analytics_id' => 'Google Analytics ID',
        'facebook_pixel_id' => 'Facebook Pixel ID'
    ]
];

$pageTitle = "General Settings";
include "includes/header.php";
?>

<div class="container-fluid">
    <div class="row">
        <?php include "includes/sidebar.php"; ?>
        
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="bi bi-gear"></i> General Settings</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="security_settings.php" class="btn btn-outline-secondary">
                            <i class="bi bi-shield-lock"></i> Security Settings
                        </a>
                        <a href="automated_email_templates.php" class="btn btn-outline-secondary">
                            <i class="bi bi-envelope-gear"></i> Email Templates
                        </a>
                    </div>
                </div>
            </div>

            <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="organization-tab" data-bs-toggle="tab" data-bs-target="#organization" type="button" role="tab">
                        <i class="bi bi-building"></i> Organization
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                        <i class="bi bi-gear"></i> System
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                        <i class="bi bi-bell"></i> Notifications
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="integrations-tab" data-bs-toggle="tab" data-bs-target="#integrations" type="button" role="tab">
                        <i class="bi bi-plug"></i> Integrations
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="settingsTabContent">
                <?php foreach ($settingsCategories as $categoryKey => $categorySettings): ?>
                    <div class="tab-pane fade <?php echo $categoryKey === 'organization' ? 'show active' : ''; ?>" 
                         id="<?php echo $categoryKey; ?>" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5><i class="bi bi-<?php echo $categoryKey === 'organization' ? 'building' : ($categoryKey === 'system' ? 'gear' : ($categoryKey === 'notifications' ? 'bell' : 'plug')); ?>"></i> 
                                    <?php echo ucfirst($categoryKey); ?> Settings</h5>
                            </div>
                            <div class="card-body">
                                <?php foreach ($categorySettings as $settingKey => $settingLabel): ?>
                                    <form method="post" class="mb-4">
                                        <input type="hidden" name="action" value="update_setting">
                                        <input type="hidden" name="setting_key" value="<?php echo htmlspecialchars($settingKey); ?>">
                                        
                                        <div class="row align-items-end">
                                            <div class="col-md-3">
                                                <label class="form-label">
                                                    <strong><?php echo htmlspecialchars($settingLabel); ?></strong>
                                                </label>
                                            </div>
                                            <div class="col-md-6">
                                                <?php 
                                                $currentValue = $allSettings[$settingKey] ?? '';
                                                if ($settingKey === 'organization_type'): ?>
                                                    <select name="setting_value" class="form-select">
                                                        <option value="church" <?php echo $currentValue === 'church' ? 'selected' : ''; ?>>Church</option>
                                                        <option value="school" <?php echo $currentValue === 'school' ? 'selected' : ''; ?>>School</option>
                                                        <option value="business" <?php echo $currentValue === 'business' ? 'selected' : ''; ?>>Business</option>
                                                        <option value="nonprofit" <?php echo $currentValue === 'nonprofit' ? 'selected' : ''; ?>>Non-Profit</option>
                                                        <option value="organization" <?php echo $currentValue === 'organization' ? 'selected' : ''; ?>>Organization</option>
                                                    </select>
                                                <?php elseif ($settingKey === 'language'): ?>
                                                    <select name="setting_value" class="form-select">
                                                        <option value="en" <?php echo $currentValue === 'en' ? 'selected' : ''; ?>>English</option>
                                                        <option value="es" <?php echo $currentValue === 'es' ? 'selected' : ''; ?>>Spanish</option>
                                                    </select>
                                                <?php elseif (in_array($settingKey, ['items_per_page', 'session_timeout', 'max_upload_size', 'backup_retention_days', 'birthday_notification_days', 'event_reminder_days', 'membership_expiry_days'])): ?>
                                                    <input type="number" name="setting_value" class="form-control" 
                                                           value="<?php echo htmlspecialchars($currentValue); ?>"
                                                           placeholder="Enter <?php echo strtolower($settingLabel); ?>">
                                                <?php else: ?>
                                                    <input type="text" name="setting_value" class="form-control" 
                                                           value="<?php echo htmlspecialchars($currentValue); ?>"
                                                           placeholder="Enter <?php echo strtolower($settingLabel); ?>">
                                                <?php endif; ?>
                                            </div>
                                            <div class="col-md-3">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="bi bi-check-lg"></i> Update
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                    <hr>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Links to related settings -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-link-45deg"></i> Related Settings</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="security_settings.php" class="btn btn-outline-primary">
                                    <i class="bi bi-shield-lock"></i> Security Settings
                                    <small class="d-block text-muted">Password policies, login security, 2FA</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="appearance_settings.php" class="btn btn-outline-primary">
                                    <i class="bi bi-palette"></i> Appearance Settings
                                    <small class="d-block text-muted">Colors, themes, branding</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="email_settings.php" class="btn btn-outline-primary">
                                    <i class="bi bi-envelope-gear"></i> Email Settings
                                    <small class="d-block text-muted">SMTP configuration, email templates</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include "includes/footer.php"; ?>
