<?php
require_once 'config.php';

echo "<h1>Reset Admin Password</h1>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_password'])) {
    $newPassword = 'admin123';
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    try {
        $stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE username = 'admin'");
        $stmt->execute([$hashedPassword]);
        
        echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
        echo "<h2>✅ Password Reset Successful!</h2>";
        echo "<p>Username: <strong>admin</strong></p>";
        echo "<p>Password: <strong>$newPassword</strong></p>";
        echo "<p>New hash: $hashedPassword</p>";
        echo "</div>";
        
        // Verify the password works
        $stmt = $pdo->prepare("SELECT password FROM admins WHERE username = 'admin'");
        $stmt->execute();
        $storedHash = $stmt->fetchColumn();
        
        echo "<h3>Verification:</h3>";
        echo "<p>Password verification: " . (password_verify($newPassword, $storedHash) ? '<span style="color: green;">SUCCESS</span>' : '<span style="color: red;">FAILED</span>') . "</p>";
        
    } catch (PDOException $e) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
        echo "<h2>❌ Error:</h2>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
} else {
    echo "<p>This will reset the admin password to 'admin123'</p>";
    echo "<form method='POST'>";
    echo "<button type='submit' name='reset_password' value='1' style='padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 5px;'>Reset Admin Password</button>";
    echo "</form>";
}
?>
