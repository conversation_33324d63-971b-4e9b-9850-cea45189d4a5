<?php
require_once __DIR__ . '/config.php';

echo "<h2>Fixing Event RSVP Table Structure</h2>";

try {
    // Check if notes column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM event_rsvps LIKE 'notes'");
    $notesExists = $stmt->rowCount() > 0;
    
    if (!$notesExists) {
        echo "Adding 'notes' column to event_rsvps table...<br>";
        $pdo->exec("ALTER TABLE event_rsvps ADD COLUMN notes TEXT NULL AFTER status");
        echo "✓ Notes column added successfully!<br>";
    } else {
        echo "✓ Notes column already exists.<br>";
    }
    
    // Check current table structure
    echo "<h3>Current event_rsvps table structure:</h3>";
    $stmt = $pdo->query("DESCRIBE event_rsvps");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    foreach ($columns as $column) {
        echo $column['Field'] . " - " . $column['Type'] . " - Null: " . $column['Null'] . " - Default: " . $column['Default'] . "\n";
    }
    echo "</pre>";
    
    echo "<h3>Testing RSVP functionality...</h3>";
    
    // Test if we can insert a sample RSVP
    $testEventId = 1; // Assuming event ID 1 exists
    $testUserId = 1;  // Assuming user ID 1 exists
    
    // Check if test RSVP already exists
    $stmt = $pdo->prepare("SELECT id FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$testEventId, $testUserId]);
    $existingRsvp = $stmt->fetch();
    
    if ($existingRsvp) {
        echo "Test RSVP already exists for event $testEventId and user $testUserId<br>";
    } else {
        // Try to insert a test RSVP
        try {
            $stmt = $pdo->prepare("
                INSERT INTO event_rsvps (event_id, user_id, status, notes, created_at, updated_at)
                VALUES (?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([$testEventId, $testUserId, 'attending', 'Test RSVP note']);
            echo "✓ Test RSVP inserted successfully!<br>";
            
            // Clean up test data
            $pdo->prepare("DELETE FROM event_rsvps WHERE event_id = ? AND user_id = ? AND notes = 'Test RSVP note'")->execute([$testEventId, $testUserId]);
            echo "✓ Test data cleaned up.<br>";
        } catch (PDOException $e) {
            echo "❌ Error inserting test RSVP: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<br><strong>RSVP table structure has been fixed!</strong>";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
