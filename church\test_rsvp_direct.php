<?php
// Test RSVP handler directly
session_start();

// Simulate logged in user
$_SESSION['user_id'] = 29; // God<PERSON>'s ID

// Simulate POST data
$_POST = [
    'event_id' => 3,
    'status' => 'maybe',
    'notes' => 'Testing the updated RSVP system - looks great!',
    'action' => 'rsvp'
];

echo "<h1>Testing RSVP Handler Directly</h1>";
echo "<h2>POST Data:</h2>";
echo "<pre>";
print_r($_POST);
echo "</pre>";

echo "<h2>Session Data:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>RSVP Handler Response:</h2>";

// Capture the output from rsvp_handler.php
ob_start();
include 'user/rsvp_handler.php';
$output = ob_get_clean();

echo "<pre>$output</pre>";
?>
