<?php
// Simple RSVP test without authentication
require_once __DIR__ . '/config.php';

echo "<h1>Simple RSVP Test</h1>";

// Test parameters
$eventId = 3;
$userId = 29; // God<PERSON>'s ID
$status = 'maybe';
$notes = 'Testing the updated RSVP system - looks great!';

echo "<h2>Test Parameters:</h2>";
echo "Event ID: $eventId<br>";
echo "User ID: $userId<br>";
echo "Status: $status<br>";
echo "Notes: $notes<br>";

try {
    // Validate input
    if (!$eventId || !$status) {
        throw new Exception('Missing required fields');
    }
    
    // Validate status
    $validStatuses = ['attending', 'maybe', 'not_attending'];
    if (!in_array($status, $validStatuses)) {
        throw new Exception('Invalid RSVP status');
    }
    
    // Check if event exists and is active
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND is_active = 1");
    $stmt->execute([$eventId]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        throw new Exception('Event not found or inactive');
    }
    
    echo "<h3>Event Found:</h3>";
    echo "Title: {$event['title']}<br>";
    echo "Date: {$event['event_date']}<br>";
    
    // Check if event is in the future
    if (strtotime($event['event_date']) < time()) {
        throw new Exception('Cannot RSVP to past events');
    }
    
    echo "✅ Event is in the future<br>";
    
    // Check if user already has an RSVP for this event
    $stmt = $pdo->prepare("SELECT id FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$eventId, $userId]);
    $existingRsvp = $stmt->fetch();
    
    if ($existingRsvp) {
        echo "ℹ️ Updating existing RSVP<br>";
        
        // Update existing RSVP
        $stmt = $pdo->prepare("
            UPDATE event_rsvps 
            SET status = ?, notes = ?, updated_at = NOW()
            WHERE event_id = ? AND user_id = ?
        ");
        $result = $stmt->execute([$status, $notes, $eventId, $userId]);
        
        if (!$result) {
            throw new Exception('Failed to update RSVP');
        }
        
        $message = 'RSVP updated successfully!';
    } else {
        echo "ℹ️ Creating new RSVP<br>";
        
        // Create new RSVP
        $stmt = $pdo->prepare("
            INSERT INTO event_rsvps (event_id, user_id, status, notes, created_at, updated_at)
            VALUES (?, ?, ?, ?, NOW(), NOW())
        ");
        $result = $stmt->execute([$eventId, $userId, $status, $notes]);
        
        if (!$result) {
            throw new Exception('Failed to create RSVP');
        }
        
        $message = 'RSVP submitted successfully!';
    }
    
    // Get updated counts
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(CASE WHEN status = 'attending' THEN 1 END) as attending_count,
            COUNT(CASE WHEN status = 'maybe' THEN 1 END) as maybe_count,
            COUNT(CASE WHEN status = 'not_attending' THEN 1 END) as not_attending_count
        FROM event_rsvps 
        WHERE event_id = ?
    ");
    $stmt->execute([$eventId]);
    $counts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>✅ Success!</h3>";
    echo "Message: $message<br>";
    echo "<h4>Updated Counts:</h4>";
    echo "Attending: {$counts['attending_count']}<br>";
    echo "Maybe: {$counts['maybe_count']}<br>";
    echo "Not Attending: {$counts['not_attending_count']}<br>";
    
    $response = [
        'success' => true,
        'message' => $message,
        'status' => $status,
        'counts' => $counts
    ];
    
    echo "<h4>JSON Response:</h4>";
    echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Database Error:</h3>";
    echo $e->getMessage() . "<br>";
    echo "Error Code: " . $e->getCode() . "<br>";
} catch (Exception $e) {
    echo "<h3>❌ General Error:</h3>";
    echo $e->getMessage() . "<br>";
}
?>
