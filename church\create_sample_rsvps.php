<?php
// Create sample RSVP data for testing attendance tracking
require_once 'config.php';

echo "<h2>Creating Sample RSVP Data</h2>";

try {
    // Get active events
    $stmt = $pdo->query("SELECT id, title FROM events WHERE is_active = 1 ORDER BY event_date DESC LIMIT 3");
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get active members
    $stmt = $pdo->query("SELECT id, full_name, email FROM members WHERE status = 'active' LIMIT 10");
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($events) || empty($members)) {
        echo "Need at least 1 event and 1 member to create sample data<br>";
        exit;
    }
    
    echo "Found " . count($events) . " events and " . count($members) . " members<br><br>";
    
    $rsvp_statuses = ['attending', 'maybe', 'not_attending'];
    $created_count = 0;
    
    foreach ($events as $event) {
        echo "<h3>Creating RSVPs for: " . htmlspecialchars($event['title']) . "</h3>";
        
        // Create RSVPs for random members
        $members_for_event = array_slice($members, 0, rand(3, min(7, count($members))));
        
        foreach ($members_for_event as $member) {
            $status = $rsvp_statuses[array_rand($rsvp_statuses)];
            
            // Check if RSVP already exists
            $stmt = $pdo->prepare("SELECT id FROM event_rsvps WHERE event_id = ? AND user_id = ?");
            $stmt->execute([$event['id'], $member['id']]);
            
            if (!$stmt->fetch()) {
                // Create new RSVP
                $stmt = $pdo->prepare("
                    INSERT INTO event_rsvps (event_id, user_id, status, notes, created_at)
                    VALUES (?, ?, ?, ?, NOW())
                ");
                
                $notes = ($status === 'attending') ? 'Looking forward to this event!' : 
                        (($status === 'maybe') ? 'Will try to make it' : 'Unfortunately cannot attend');
                
                $stmt->execute([$event['id'], $member['id'], $status, $notes]);
                
                echo "✓ " . htmlspecialchars($member['full_name']) . " - " . $status . "<br>";
                $created_count++;
            } else {
                echo "- " . htmlspecialchars($member['full_name']) . " - already has RSVP<br>";
            }
        }
        echo "<br>";
    }
    
    echo "<h3>Summary</h3>";
    echo "Created " . $created_count . " new RSVPs<br><br>";
    
    // Show current RSVP summary
    $stmt = $pdo->query("
        SELECT 
            e.title,
            COUNT(CASE WHEN er.status = 'attending' THEN 1 END) as attending,
            COUNT(CASE WHEN er.status = 'maybe' THEN 1 END) as maybe,
            COUNT(CASE WHEN er.status = 'not_attending' THEN 1 END) as not_attending
        FROM events e
        LEFT JOIN event_rsvps er ON e.id = er.event_id
        WHERE e.is_active = 1
        GROUP BY e.id, e.title
        ORDER BY e.event_date DESC
    ");
    
    $summary = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Current RSVP Summary</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Event</th><th>Attending</th><th>Maybe</th><th>Not Attending</th></tr>";
    
    foreach ($summary as $row) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['title']) . "</td>";
        echo "<td>" . $row['attending'] . "</td>";
        echo "<td>" . $row['maybe'] . "</td>";
        echo "<td>" . $row['not_attending'] . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    echo "<a href='admin/event_attendance.php'>Go to Admin Attendance Tracking</a> (requires admin login)<br>";
    echo "<a href='test_attendance_tracking.php'>Test Attendance System</a><br>";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
