<?php
// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Connect to the database
    $pdo = new PDO('mysql:host=localhost;dbname=churchdb', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n";
    
    // Define the new template
    $templateName = 'Birthday Reminder Template';
    $subject = '{full_name}\'s birthday is coming up in {days_text}!';
    $content = '<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px; border-radius: 5px;">
    <div style="text-align: center; margin-bottom: 20px;">
        <img src="https://freedomassemblychurch.org/wp-content/uploads/2023/10/church-logo.png" alt="Church Logo" style="max-width: 200px;">
    </div>
    
    <div style="background-color: #fff; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
        <h2 style="color: #3498db; text-align: center;">Birthday Reminder</h2>
        
        <p>Dear {full_name},</p>
        
        <p>We\'re excited to remind you that your birthday is coming up in <strong>{days_text}</strong> on <strong>{upcoming_birthday_formatted}</strong>!</p>
        
        <p>At Freedom Assembly Church International, we value each member of our church family and want to make your special day memorable.</p>
        
        <p>We\'re looking forward to celebrating with you on {upcoming_birthday_day}, {upcoming_birthday_date}!</p>
        
        <div style="text-align: center; margin: 30px 0;">
            <div style="display: inline-block; background-color: #3498db; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;">Happy Early Birthday!</div>
        </div>
        
        <p>Many blessings,<br>
        Freedom Assembly Church International Team</p>
    </div>
    
    <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
        <p>Freedom Assembly Church International<br>
        123 Church Street, Cityville, ST 12345<br>
        <EMAIL> | (555) 123-4567</p>
    </div>
</div>';
    $isBirthdayTemplate = 0; // 0 means it's a reminder template
    
    // Check if the template already exists
    $checkStmt = $pdo->prepare("SELECT id FROM email_templates WHERE template_name = ? AND is_birthday_template = ?");
    $checkStmt->execute([$templateName, $isBirthdayTemplate]);
    $existingTemplate = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existingTemplate) {
        // Update existing template
        $updateStmt = $pdo->prepare("UPDATE email_templates SET subject = ?, content = ? WHERE id = ?");
        $result = $updateStmt->execute([$subject, $content, $existingTemplate['id']]);
        
        if ($result) {
            echo "Template updated successfully with ID: " . $existingTemplate['id'] . "\n";
        } else {
            echo "Failed to update template.\n";
        }
    } else {
        // Insert new template
        $insertStmt = $pdo->prepare("INSERT INTO email_templates (template_name, subject, content, is_birthday_template) VALUES (?, ?, ?, ?)");
        $result = $insertStmt->execute([$templateName, $subject, $content, $isBirthdayTemplate]);
        
        if ($result) {
            echo "Template inserted successfully with ID: " . $pdo->lastInsertId() . "\n";
        } else {
            echo "Failed to insert template.\n";
        }
    }
    
    // Verify templates in the database
    $listStmt = $pdo->query("SELECT id, template_name, subject, LEFT(content, 100) as preview, is_birthday_template FROM email_templates WHERE is_birthday_template = 0");
    $templates = $listStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nCurrent reminder templates in the database:\n";
    echo "----------------------------------------\n";
    foreach ($templates as $template) {
        echo "ID: " . $template['id'] . "\n";
        echo "Name: " . $template['template_name'] . "\n";
        echo "Subject: " . $template['subject'] . "\n";
        echo "Preview: " . $template['preview'] . "...\n";
        echo "----------------------------------------\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?> 