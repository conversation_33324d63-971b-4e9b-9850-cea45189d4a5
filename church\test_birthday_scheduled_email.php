<?php
/**
 * Test Script for Birthday Scheduled Email Placeholders
 * 
 * This script tests the birthday email placeholder functionality with the fix.
 */

// Include necessary files
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/email_functions.php';

// Set up logging
$logFile = __DIR__ . '/logs/test_birthday_scheduled_email.log';
file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test birthday scheduled email script started.' . PHP_EOL);

// Get a birthday template
try {
    $stmt = $pdo->prepare("
        SELECT id, template_name, subject, content 
        FROM email_templates 
        WHERE template_category = 'birthday' 
        LIMIT 1
    ");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] No birthday template found.' . <PERSON><PERSON>_EOL, FILE_APPEND);
        echo "No birthday template found. Please create a birthday template first.\n";
        exit;
    }
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Using template: ' . $template['template_name'] . PHP_EOL, FILE_APPEND);
    
    // Get a test recipient
    $stmt = $pdo->prepare("SELECT id, email, full_name, birth_date FROM members WHERE email IS NOT NULL AND email != '' AND birth_date IS NOT NULL LIMIT 1");
    $stmt->execute();
    $recipient = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$recipient) {
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] No recipients found with valid email addresses and birth dates.' . PHP_EOL, FILE_APPEND);
        echo "No recipients found with valid email addresses and birth dates.\n";
        exit;
    }
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test recipient: ' . $recipient['email'] . PHP_EOL, FILE_APPEND);
    
    // Create a test schedule in the database
    $stmt = $pdo->prepare("
        INSERT INTO email_schedules 
        (name, description, frequency, status, next_run, created_at, updated_at)
        VALUES 
        ('Test Birthday Email Fix', 'Testing birthday email placeholder replacement', 'once', 'active', NOW(), NOW(), NOW())
    ");
    $stmt->execute();
    $scheduleId = $pdo->lastInsertId();
    
    // Add schedule settings with the birthday template
    $stmt = $pdo->prepare("
        INSERT INTO email_schedule_settings
        (schedule_id, template_id, custom_subject, custom_content, track_opens, track_clicks)
        VALUES
        (?, ?, ?, ?, 1, 1)
    ");
    $stmt->execute([$scheduleId, $template['id'], $template['subject'], $template['content']]);
    
    // Add recipient to schedule
    $stmt = $pdo->prepare("
        INSERT INTO email_schedule_recipients
        (schedule_id, recipient_id, recipient_type, status)
        VALUES
        (?, ?, 'member', 'pending')
    ");
    $stmt->execute([$scheduleId, $recipient['id']]);
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Created test schedule with ID: ' . $scheduleId . PHP_EOL, FILE_APPEND);
    
    // Now test sending the email directly using our updated function
    echo "Created test schedule with ID: $scheduleId\n";
    echo "Testing direct email send with birthday template...\n";
    
    // Get the schedule data
    $stmt = $pdo->prepare("
        SELECT s.id, s.name, s.status, ss.template_id, ss.custom_subject, ss.custom_content, ss.track_opens, ss.track_clicks
        FROM email_schedules s
        JOIN email_schedule_settings ss ON s.id = ss.schedule_id
        WHERE s.id = ?
    ");
    $stmt->execute([$scheduleId]);
    $schedule = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Create member data for placeholder replacement
    $memberData = [
        'id' => $recipient['id'],
        'full_name' => $recipient['full_name'],
        'first_name' => explode(' ', $recipient['full_name'])[0] ?? '',
        'last_name' => (count(explode(' ', $recipient['full_name'])) > 1) ? explode(' ', $recipient['full_name'], 2)[1] : '',
        'email' => $recipient['email'],
        'birth_date' => $recipient['birth_date'],
        'recipient_full_name' => $recipient['full_name'],
        'recipient_first_name' => explode(' ', $recipient['full_name'])[0] ?? '',
        'recipient_email' => $recipient['email']
    ];
    
    // Calculate birthday-related fields
    if (!empty($recipient['birth_date'])) {
        $birth_month = date('m', strtotime($recipient['birth_date']));
        $birth_day = date('d', strtotime($recipient['birth_date']));
        $birth_year = date('Y', strtotime($recipient['birth_date']));
        $current_year = date('Y');
        $next_year = $current_year + 1;
        
        // Determine if birthday has passed this year already
        $this_year_birthday = $current_year . '-' . $birth_month . '-' . $birth_day;
        $next_year_birthday = $next_year . '-' . $birth_month . '-' . $birth_day;
        
        // Use next year's date if this year's birthday has already passed
        $upcoming_date = strtotime($this_year_birthday) < time() ? $next_year_birthday : $this_year_birthday;
        
        // Calculate age on upcoming birthday
        $age = strtotime($this_year_birthday) < time() ? 
              ($next_year - $birth_year) : 
              ($current_year - $birth_year);
        
        // Add days until birthday
        $days_until = ceil((strtotime($upcoming_date) - time()) / 86400);
        
        // Add critical birthday values to the data array
        $memberData['upcoming_birthday_date'] = date('F j, Y', strtotime($upcoming_date));
        $memberData['upcoming_birthday_day'] = date('l', strtotime($upcoming_date));
        $memberData['upcoming_birthday_formatted'] = date('l, F j, Y', strtotime($upcoming_date));
        $memberData['days_until_birthday'] = $days_until;
        $memberData['days_text'] = $days_until == 0 ? 'today' : 
                                  ($days_until == 1 ? 'tomorrow' : 
                                  "in $days_until days");
        $memberData['age'] = $age;
        $memberData['birthday_member_age'] = $age;
        $memberData['birthday_member_name'] = $memberData['first_name'];
        $memberData['birthday_member_full_name'] = $memberData['full_name'];
    }
    
    // Prepare schedule data for the function
    $scheduleData = [
        'id' => $schedule['id'],
        'name' => $schedule['name'],
        'track_opens' => $schedule['track_opens'],
        'track_clicks' => $schedule['track_clicks']
    ];
    
    // Test the direct send using our updated function
    $subject = !empty($schedule['custom_subject']) ? $schedule['custom_subject'] : $template['subject'];
    $content = !empty($schedule['custom_content']) ? $schedule['custom_content'] : $template['content'];
    
    // Log the subject and content before sending
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Subject before sending: ' . $subject . PHP_EOL, FILE_APPEND);
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Content before sending (first 200 chars): ' . substr($content, 0, 200) . PHP_EOL, FILE_APPEND);
    
    // Send the test email
    $result = sendScheduledEmail(
        $recipient['email'],
        $recipient['full_name'],
        $subject,
        $content,
        $scheduleData,
        $memberData
    );
    
    if ($result) {
        echo "Test birthday email sent successfully to " . $recipient['email'] . "\n";
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test birthday email sent successfully.' . PHP_EOL, FILE_APPEND);
    } else {
        echo "Failed to send test birthday email to " . $recipient['email'] . "\n";
        file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Failed to send test birthday email.' . PHP_EOL, FILE_APPEND);
    }
    
    // Now run the scheduled email processor to test the full system
    echo "Running scheduled email processor...\n";
    require_once __DIR__ . '/cron/process_scheduled_emails.php';
    
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Test birthday scheduled email script completed.' . PHP_EOL, FILE_APPEND);
    echo "Test completed. Check logs for details.\n";
    
} catch (Exception $e) {
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
    echo "Error: " . $e->getMessage() . "\n";
}
