<?php
// Set error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Include configuration
require_once 'config.php';
require_once 'send_birthday_reminders.php';

// Initialize BirthdayReminder
$birthdayReminder = new BirthdayReminder($pdo);

// Get template 14
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = 14");
$stmt->execute();
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    die("Could not find template 14");
}

// Create test data for birthday member (the one having the birthday)
$birthdayMember = [
    'id' => 123,
    'full_name' => 'Oneye<PERSON> Johnson',
    'email' => '<EMAIL>',
    'birth_date' => '1950-03-22',
];

// Create test data for recipient member (the one receiving the notification)
$recipientMember = [
    'id' => 456,
    'full_name' => '<PERSON>',
    'email' => '<EMAIL>',
];

// Days until birthday
$daysUntil = 4;

echo "<h1>Testing Template 14 Subject Line Issue</h1>";
echo "<p><strong>Template Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";
echo "<p><strong>Placeholders:</strong> {birthday_member_full_name}, {days_text}</p>";

// Manually construct the expected replacement values
$birthdayMemberFirst = explode(' ', $birthdayMember['full_name'])[0]; // "Oneyeka"
$birthdayMemberFull = $birthdayMember['full_name']; // "Oneyeka Johnson"
$daysText = "in {$daysUntil} days";

echo "<p><strong>Values to insert:</strong><br>";
echo "birthday_member_name = " . htmlspecialchars($birthdayMemberFirst) . "<br>";
echo "birthday_member_full_name = " . htmlspecialchars($birthdayMemberFull) . "<br>";
echo "days_text = " . htmlspecialchars($daysText) . "</p>";

// Process the subject
$processed_subject = $birthdayReminder->processBirthdayMemberTemplate($template['subject'], $recipientMember, $birthdayMember, $daysUntil);

echo "<p><strong>Processed subject:</strong> " . htmlspecialchars($processed_subject) . "</p>";

// Check if the problem is in the usage of full_name instead of first name
$expectedSubject = "Celebrate with us! {$birthdayMemberFirst}'s birthday is coming up {$daysText}!";
$expectedSubjectWithFullName = "Celebrate with us! {$birthdayMemberFull}'s birthday is coming up {$daysText}!";

echo "<p><strong>Testing:</strong><br>";
echo "Expected with first name: " . htmlspecialchars($expectedSubject) . "<br>";
echo "Expected with full name: " . htmlspecialchars($expectedSubjectWithFullName) . "<br>";
echo "Matches first name version? " . ($processed_subject === $expectedSubject ? "YES" : "NO") . "<br>";
echo "Matches full name version? " . ($processed_subject === $expectedSubjectWithFullName ? "YES" : "NO") . "</p>";

// Look for duplication patterns that could be in the email
echo "<p><strong>Checking for duplication patterns:</strong><br>";
$duplicationPatterns = [
    "OneyeaOneyeka",
    "OneyeaOneyeka Johnson",
    "OneyekaOneyeka",
    "Oneyeka JohnsonOneyeka",
    "Oneyeka JohnsonOneyeka Johnson"
];

foreach ($duplicationPatterns as $pattern) {
    $found = strpos($processed_subject, $pattern) !== false;
    echo "Pattern: " . htmlspecialchars($pattern) . " - " . ($found ? "FOUND!" : "Not found") . "<br>";
}
echo "</p>";

// Check how the actual replacement is happening
echo "<h2>Detailed Replacement Steps:</h2>";

// 1. Check subject line detection
$isSubjectLine = (strlen($template['subject']) < 200 && strpos($template['subject'], "Celebrate with us!") !== false);
echo "<p>Is detected as subject line? " . ($isSubjectLine ? "YES" : "NO") . "</p>";

// 2. Create the replacements map for birthdayMember
$birthdayMemberReplacements = [
    '{birthday_member_name}' => $birthdayMemberFirst,
    '{birthday_member_first_name}' => $birthdayMemberFirst,
    '{birthday_member_full_name}' => $birthdayMemberFull,
    '{days_text}' => $daysText
];

// 3. Show how replacement would work
echo "<p><strong>If using regular replacements:</strong><br>";
$regularReplaced = str_replace(
    array_keys($birthdayMemberReplacements),
    array_values($birthdayMemberReplacements),
    $template['subject']
);
echo "Result: " . htmlspecialchars($regularReplaced) . "</p>";

// Propose the fix
echo "<h2>Proposed Fix</h2>";
echo "<p>The issue is that the template uses {birthday_member_full_name} in the subject, but our fix in processBirthdayMemberTemplate method only handles duplications with first names properly.</p>";
echo "<p>We need to modify the code to ensure template 14's subject is properly handled.</p>"; 