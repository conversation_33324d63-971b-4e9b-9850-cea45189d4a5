<?php
require_once __DIR__ . '/config.php';

echo "<h2>Events Database Test</h2>";

try {
    // Check events table structure
    echo "<h3>Events Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "<td>{$col['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Check current events
    echo "<h3>Current Events:</h3>";
    $stmt = $pdo->query("SELECT id, title, event_date, event_time, is_active FROM events ORDER BY event_date");
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($events) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Title</th><th>Date</th><th>Time</th><th>Active</th><th>Timestamp</th><th>Is Future?</th></tr>";
        foreach ($events as $event) {
            $eventTimestamp = strtotime($event['event_date'] . ' ' . $event['event_time']);
            $currentTimestamp = time();
            $isFuture = $eventTimestamp > $currentTimestamp ? 'Yes' : 'No';
            
            echo "<tr>";
            echo "<td>{$event['id']}</td>";
            echo "<td>{$event['title']}</td>";
            echo "<td>{$event['event_date']}</td>";
            echo "<td>{$event['event_time']}</td>";
            echo "<td>{$event['is_active']}</td>";
            echo "<td>" . date('Y-m-d H:i:s', $eventTimestamp) . "</td>";
            echo "<td>$isFuture</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p>Current time: " . date('Y-m-d H:i:s') . " (timestamp: " . time() . ")</p>";
    } else {
        echo "No events found.";
    }

    // Check RSVP table structure
    echo "<h3>Event RSVPs Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE event_rsvps");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "<td>{$col['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Check existing RSVPs
    echo "<h3>Existing RSVPs:</h3>";
    $stmt = $pdo->query("SELECT * FROM event_rsvps ORDER BY created_at DESC LIMIT 10");
    $rsvps = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($rsvps) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Event ID</th><th>User ID</th><th>Status</th><th>Notes</th><th>Created</th></tr>";
        foreach ($rsvps as $rsvp) {
            echo "<tr>";
            echo "<td>{$rsvp['id']}</td>";
            echo "<td>{$rsvp['event_id']}</td>";
            echo "<td>{$rsvp['user_id']}</td>";
            echo "<td>{$rsvp['status']}</td>";
            echo "<td>{$rsvp['notes']}</td>";
            echo "<td>{$rsvp['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No RSVPs found.";
    }

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
}
?>
