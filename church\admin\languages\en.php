<?php
/**
 * English Language File
 * Default language for the Church Campaign Management System
 */

return [
    // Common
    'yes' => 'Yes',
    'no' => 'No',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'edit' => 'Edit',
    'view' => 'View',
    'add' => 'Add',
    'create' => 'Create',
    'update' => 'Update',
    'search' => 'Search',
    'filter' => 'Filter',
    'export' => 'Export',
    'import' => 'Import',
    'print' => 'Print',
    'close' => 'Close',
    'back' => 'Back',
    'check' => 'Check',
    'next' => 'Next',
    'previous' => 'Previous',
    'loading' => 'Loading...',
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'confirm' => 'Confirm',
    'name' => 'Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'address' => 'Address',
    'date' => 'Date',
    'time' => 'Time',
    'status' => 'Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'total' => 'Total',
    'count' => 'Count',
    'actions' => 'Actions',
    'settings' => 'Settings',
    'preferences' => 'Preferences',
    
    // Navigation
    'dashboard' => 'Dashboard',
    'members' => 'Members',
    'events' => 'Events',
    'campaigns' => 'Campaigns',
    'communications' => 'Communications',
    'reports' => 'Reports',
    'settings' => 'Settings',
    'logout' => 'Logout',
    'profile' => 'Profile',
    'home' => 'Home',
    
    // Dashboard
    'welcome_message' => 'Welcome to your Church Management Dashboard',
    'quick_stats' => 'Quick Statistics',
    'recent_activity' => 'Recent Activity',
    'upcoming_events' => 'Upcoming Events',
    'member_count' => 'Total Members',
    'active_campaigns' => 'Active Campaigns',
    'this_month' => 'This Month',
    'this_week' => 'This Week',
    'today' => 'Today',
    'days' => 'Days',
    'birthdays' => 'Birthdays',
    'calendar' => 'Calendar',
    'no_birthdays_this_month' => 'No birthdays this month.',
    'recent_members' => 'Recent Members',
    'new' => 'New',
    
    // Members
    'add_member' => 'Add Member',
    'edit_member' => 'Edit Member',
    'member_details' => 'Member Details',
    'member_list' => 'Member List',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'full_name' => 'Full Name',
    'phone_number' => 'Phone Number',
    'birth_date' => 'Birth Date',
    'join_date' => 'Join Date',
    'member_since' => 'Member Since',
    'contact_info' => 'Contact Information',
    'personal_info' => 'Personal Information',
    
    // Events
    'add_event' => 'Add Event',
    'edit_event' => 'Edit Event',
    'event_details' => 'Event Details',
    'event_list' => 'Event List',
    'event_title' => 'Event Title',
    'event_description' => 'Event Description',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'start_time' => 'Start Time',
    'end_time' => 'End Time',
    'location' => 'Location',
    'organizer' => 'Organizer',
    'attendees' => 'Attendees',
    'capacity' => 'Capacity',
    'registration' => 'Registration',
    
    // Communications
    'send_email' => 'Send Email',
    'send_sms' => 'Send SMS',
    'email_campaigns' => 'Email Campaigns',
    'sms_campaigns' => 'SMS Campaigns',
    'bulk_email' => 'Bulk Email',
    'bulk_sms' => 'Bulk SMS',
    'single_sms' => 'Single SMS',
    'email_templates' => 'Email Templates',
    'sms_templates' => 'SMS Templates',
    'recipients' => 'Recipients',
    'subject' => 'Subject',
    'message' => 'Message',
    'send_now' => 'Send Now',
    'schedule_send' => 'Schedule Send',
    'draft' => 'Draft',
    'sent' => 'Sent',
    'delivered' => 'Delivered',
    'failed' => 'Failed',
    
    // Forms
    'required_field' => 'This field is required',
    'invalid_email' => 'Please enter a valid email address',
    'invalid_phone' => 'Please enter a valid phone number',
    'invalid_date' => 'Please enter a valid date',
    'password_mismatch' => 'Passwords do not match',
    'form_saved' => 'Form saved successfully',
    'form_error' => 'There was an error saving the form',
    'confirm_delete' => 'Are you sure you want to delete this item?',
    'delete_success' => 'Item deleted successfully',
    'delete_error' => 'There was an error deleting the item',
    
    // Pagination
    'showing_results' => 'Showing {start} to {end} of {total} results',
    'no_results' => 'No results found',
    'page' => 'Page',
    'of' => 'of',
    'per_page' => 'per page',
    'first' => 'First',
    'last' => 'Last',
    
    // Accessibility
    'skip_to_content' => 'Skip to main content',
    'toggle_navigation' => 'Toggle navigation',
    'toggle_dark_mode' => 'Toggle dark mode',
    'increase_font_size' => 'Increase font size',
    'decrease_font_size' => 'Decrease font size',
    'high_contrast' => 'High contrast',
    'screen_reader_text' => 'Screen reader text',
    
    // Theme and Appearance
    'appearance' => 'Appearance',
    'theme' => 'Theme',
    'light_mode' => 'Light Mode',
    'dark_mode' => 'Dark Mode',
    'auto_mode' => 'Auto (Follow System)',
    'color_scheme' => 'Color Scheme',
    'font_size' => 'Font Size',
    'small' => 'Small',
    'normal' => 'Normal',
    'large' => 'Large',
    'extra_large' => 'Extra Large',
    
    // Language
    'language' => 'Language',
    'select_language' => 'Select Language',
    'language_changed' => 'Language changed successfully',
    
    // Time and Date
    'january' => 'January',
    'february' => 'February',
    'march' => 'March',
    'april' => 'April',
    'may' => 'May',
    'june' => 'June',
    'july' => 'July',
    'august' => 'August',
    'september' => 'September',
    'october' => 'October',
    'november' => 'November',
    'december' => 'December',
    'monday' => 'Monday',
    'tuesday' => 'Tuesday',
    'wednesday' => 'Wednesday',
    'thursday' => 'Thursday',
    'friday' => 'Friday',
    'saturday' => 'Saturday',
    'sunday' => 'Sunday',
    
    // Notifications
    'notification' => 'Notification',
    'notifications' => 'Notifications',
    'mark_as_read' => 'Mark as read',
    'mark_all_read' => 'Mark all as read',
    'no_notifications' => 'No notifications',
    
    // User Management
    'user' => 'User',
    'users' => 'Users',
    'admin' => 'Admin',
    'administrator' => 'Administrator',
    'role' => 'Role',
    'permissions' => 'Permissions',
    'login' => 'Login',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'change_password' => 'Change Password',
    'forgot_password' => 'Forgot Password',
    'reset_password' => 'Reset Password',
    
    // System
    'system' => 'System',
    'version' => 'Version',
    'database' => 'Database',
    'backup' => 'Backup',
    'maintenance' => 'Maintenance',
    'logs' => 'Logs',
    'cache' => 'Cache',
    'clear_cache' => 'Clear Cache',
    
    // Analytics
    'analytics' => 'Analytics',
    'statistics' => 'Statistics',
    'chart' => 'Chart',
    'graph' => 'Graph',
    'report' => 'Report',
    'data' => 'Data',
    'metrics' => 'Metrics',
    
    // File Management
    'file' => 'File',
    'files' => 'Files',
    'upload' => 'Upload',
    'download' => 'Download',
    'attachment' => 'Attachment',
    'attachments' => 'Attachments',
    'image' => 'Image',
    'document' => 'Document',
    
    // Integration
    'integration' => 'Integration',
    'integrations' => 'Integrations',
    'api' => 'API',
    'webhook' => 'Webhook',
    'sync' => 'Sync',
    'connect' => 'Connect',
    'disconnect' => 'Disconnect',
    
    // Custom Fields
    'custom_field' => 'Custom Field',
    'custom_fields' => 'Custom Fields',
    'field_type' => 'Field Type',
    'field_name' => 'Field Name',
    'field_label' => 'Field Label',
    'field_value' => 'Field Value',
    'text_field' => 'Text Field',
    'number_field' => 'Number Field',
    'date_field' => 'Date Field',
    'dropdown_field' => 'Dropdown Field',
    'checkbox_field' => 'Checkbox Field',
    'textarea_field' => 'Textarea Field',
    
    // Organization
    'organization' => 'Organization',
    'church' => 'Church',
    'ministry' => 'Ministry',
    'group' => 'Group',
    'department' => 'Department',
    'leader' => 'Leader',
    'pastor' => 'Pastor',
    'volunteer' => 'Volunteer',
    
    // Donations
    'donation' => 'Donation',
    'donations' => 'Donations',
    'offering' => 'Offering',
    'tithe' => 'Tithe',
    'amount' => 'Amount',
    'donor' => 'Donor',
    'payment_method' => 'Payment Method',
    'transaction' => 'Transaction',
    
    // Help and Support
    'help' => 'Help',
    'support' => 'Support',
    'documentation' => 'Documentation',
    'tutorial' => 'Tutorial',
    'faq' => 'FAQ',
    'contact_support' => 'Contact Support',
    'user_guide' => 'User Guide',
    
    // Validation Messages
    'validation_required' => 'The {field} field is required.',
    'validation_email' => 'The {field} field must be a valid email address.',
    'validation_min_length' => 'The {field} field must be at least {min} characters.',
    'validation_max_length' => 'The {field} field must not exceed {max} characters.',
    'validation_numeric' => 'The {field} field must be a number.',
    'validation_date' => 'The {field} field must be a valid date.',
    'validation_unique' => 'The {field} field must be unique.',
    
    // Success Messages
    'success_created' => '{item} created successfully.',
    'success_updated' => '{item} updated successfully.',
    'success_deleted' => '{item} deleted successfully.',
    'success_saved' => 'Changes saved successfully.',
    'success_sent' => '{item} sent successfully.',
    
    // Error Messages
    'error_general' => 'An error occurred. Please try again.',
    'error_not_found' => '{item} not found.',
    'error_permission' => 'You do not have permission to perform this action.',
    'error_validation' => 'Please correct the errors below.',
    'error_database' => 'Database error occurred.',
    'error_network' => 'Network error occurred.',
    
    // Confirmation Messages
    'confirm_delete_item' => 'Are you sure you want to delete this {item}?',
    'confirm_action' => 'Are you sure you want to perform this action?',
    'action_cannot_undone' => 'This action cannot be undone.',

    // SMS System
    'sms_system' => 'SMS System',
    'sms_analytics' => 'SMS Analytics',
    'sms_sent' => 'SMS Sent',
    'sms_delivered' => 'SMS Delivered',
    'sms_failed' => 'SMS Failed',
    'phone_numbers' => 'Phone Numbers',
    'external_contacts' => 'External Contacts',
    'church_members' => 'Church Members',
    'custom_numbers' => 'Custom Numbers',
    'select_recipients' => 'Select Recipients',
    'enter_phone_numbers' => 'Enter phone numbers (one per line)',
    'sms_message' => 'SMS Message',
    'character_count' => 'Character Count',
    'characters_remaining' => 'characters remaining',
    'send_sms' => 'Send SMS',
    'sms_sent_successfully' => 'SMS sent successfully',
    'sms_send_failed' => 'Failed to send SMS',

    // Email System
    'email_analytics' => 'Email Analytics',
    'emails_sent' => 'Emails Sent',
    'open_rate' => 'Open Rate',
    'click_rate' => 'Click Rate',
    'bounce_rate' => 'Bounce Rate',
    'unsubscribe_rate' => 'Unsubscribe Rate',
    'campaign_performance' => 'Campaign Performance',
    'recipient_analytics' => 'Recipient Analytics',
    'ab_testing' => 'A/B Testing',
    'test_variant' => 'Test Variant',
    'control_group' => 'Control Group',
    'winner' => 'Winner',
    'statistical_significance' => 'Statistical Significance',

    // Sidebar Navigation
    'dashboard' => 'Dashboard',
    'member_management' => 'Member Management',
    'events_management' => 'Events Management',
    'email_management' => 'Email Management',
    'sms_management' => 'SMS Management',
    'integrations' => 'Integrations',
    'account' => 'Account',

    // Member Management
    'members' => 'Members',
    'add_member' => 'Add Member',

    // Events Management
    'events' => 'Events',
    'event_attendance' => 'Event Attendance',
    'event_categories' => 'Event Categories',
    'event_reports' => 'Event Reports',

    // Email Management
    'email_scheduler' => 'Email Scheduler',
    'contacts' => 'Contacts',
    'contact_groups' => 'Contact Groups',
    'birthday_messages' => 'Birthday Messages',
    'send_birthday_emails' => 'Send Birthday Emails',
    'test_birthday_emails' => 'Test Birthday Emails',
    'debug_placeholders' => 'Debug Placeholders',
    'birthday_notifications' => 'Birthday Notifications',
    'automated_templates' => 'Automated Templates',
    'whatsapp_templates' => 'WhatsApp Templates',
    'whatsapp_messages' => 'WhatsApp Messages',
    'about_shortcodes' => 'About & Shortcodes',

    // SMS Management
    'sms_analytics' => 'SMS Analytics',

    // Integrations
    'calendar_integration' => 'Calendar Integration',
    'social_media' => 'Social Media',
    'sms_integration' => 'SMS Integration',
    'payment_integration' => 'Payment Integration',
    'payment_tables' => 'Payment Tables',
    'donations' => 'Donations',
    'check_payment_sdks' => 'Check Payment SDKs',

    // Account Settings
    'settings' => 'Settings',
    'appearance' => 'Appearance',
    'custom_fields' => 'Custom Fields',
    'logo_upload' => 'Logo Upload',
    'branding' => 'Branding',
    'logo_management' => 'Logo Management',
    'security_setup' => 'Security Setup',
    'security_audit' => 'Security Audit',
    'security_settings' => 'Security Settings',
    'my_profile' => 'My Profile',
    'logout' => 'Logout',
];
?>
