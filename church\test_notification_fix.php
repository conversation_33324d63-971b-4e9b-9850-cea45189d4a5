<?php
// Set error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Include configuration
require_once 'config.php';
require_once 'send_birthday_reminders.php';

// Initialize BirthdayReminder
$birthdayReminder = new BirthdayReminder($pdo);

// Create test data for the birthday member (person having the birthday)
$birthdayMember = [
    'id' => 123,
    'full_name' => '<PERSON>',
    'first_name' => '<PERSON>',
    'last_name' => '<PERSON>',
    'email' => '<EMAIL>',
    'birth_date' => '1990-03-22',
];

// Create test data for the recipient (person receiving the notification)
$recipientMember = [
    'id' => 456,
    'full_name' => '<PERSON>',
    'first_name' => '<PERSON>',
    'last_name' => '<PERSON>',
    'email' => '<EMAIL>',
    'birth_date' => '1985-06-15',
];

// Get a notification template
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE subject LIKE '%Celebrate with us%' OR subject LIKE '%Birthday%' LIMIT 1");
$stmt->execute();
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    die("Could not find a suitable template for testing");
}

echo "<h1>Testing Birthday Notification Fix</h1>";

echo "<h2>Template Information</h2>";
echo "<p><strong>Template ID:</strong> " . $template['id'] . "</p>";
echo "<p><strong>Template Name:</strong> " . $template['template_name'] . "</p>";
echo "<p><strong>Original Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";

// First, test the sanitizeSubjectLine method directly
$reflectionMethod = new ReflectionMethod('BirthdayReminder', 'sanitizeSubjectLine');
$reflectionMethod->setAccessible(true);

echo "<h2>Test 1: Direct sanitizeSubjectLine Call</h2>";

// Test 1A: With just recipient (old behavior)
$subject1A = $reflectionMethod->invoke($birthdayReminder, $template['subject'], $recipientMember);
echo "<p><strong>Result with just recipient:</strong> " . htmlspecialchars($subject1A) . "</p>";

// Test 1B: With recipient and birthday member (fixed behavior)
$subject1B = $reflectionMethod->invoke($birthdayReminder, $template['subject'], $recipientMember, $birthdayMember);
echo "<p><strong>Result with recipient and birthday member:</strong> " . htmlspecialchars($subject1B) . "</p>";

// Test 2: Use the sendEmail method through reflection to test the complete process
$reflectionSendEmail = new ReflectionMethod('BirthdayReminder', 'sendEmail');
$reflectionSendEmail->setAccessible(true);

echo "<h2>Test 2: Complete Email Processing</h2>";

// Use reflection to access private properties
$reflectionProp = new ReflectionProperty('BirthdayReminder', 'emailType');
$reflectionProp->setAccessible(true);
$reflectionProp->setValue($birthdayReminder, 'birthday_notification');

// Create a mock template
$mockTemplate = [
    'id' => $template['id'],
    'subject' => "Celebrate with us! {full_name}'s birthday is coming up soon!",
    'content' => "Dear {recipient_full_name}, We are delighted to announce that our cherished church family member {full_name} will be celebrating their birthday soon!",
    'template_name' => 'Test Birthday Notification'
];

// Capture the subject after processing without actually sending
$reflectionSendEmail->invokeArgs($birthdayReminder, [$recipientMember, $mockTemplate, 0, 'birthday_notification', $birthdayMember]);

echo "<p><strong>Template used:</strong> " . htmlspecialchars($mockTemplate['subject']) . "</p>";
echo "<p><strong>Expected result:</strong> Celebrate with us! Mike's birthday is coming up soon!</p>";

// Now test with the real sendMemberBirthdayNotifications method
echo "<h2>Test 3: Real Notification Method Test</h2>";
echo "<p>To test the full notification system, click below to send a test notification:</p>";
echo "<p><a href='?send_test=1'>Send Test Notification</a></p>";

// Handle test notification sending
if (isset($_GET['send_test'])) {
    // Create a temporary test template for notifications
    $stmt = $pdo->prepare("
        INSERT INTO email_templates 
        (template_name, subject, content, is_birthday_template, is_active) 
        VALUES 
        ('Test Notification Template', 'Celebrate with us! {full_name}\'s birthday is coming up soon!', 
        'Dear {recipient_full_name},<br><br>We are delighted to announce that our cherished church family member {full_name} will be celebrating their birthday soon!<br><br>With Blessings,<br>Church Team', 
        0, 1)
    ");
    $stmt->execute();
    $testTemplateId = $pdo->lastInsertId();
    
    // Insert a temporary test birthday member if needed
    $birthdayMemberId = $birthdayMember['id'];
    $recipientMemberId = $recipientMember['id'];
    
    // Check if we need to insert the test members
    $stmt = $pdo->prepare("SELECT id FROM members WHERE id = ?");
    $stmt->execute([$birthdayMember['id']]);
    if (!$stmt->fetch()) {
        $stmt = $pdo->prepare("
            INSERT INTO members 
            (id, full_name, first_name, last_name, email, birth_date, status) 
            VALUES 
            (?, ?, ?, ?, ?, ?, 'active')
        ");
        $stmt->execute([
            $birthdayMember['id'],
            $birthdayMember['full_name'],
            $birthdayMember['first_name'],
            $birthdayMember['last_name'],
            $birthdayMember['email'],
            $birthdayMember['birth_date']
        ]);
        $birthdayMemberId = $pdo->lastInsertId();
    }
    
    $stmt = $pdo->prepare("SELECT id FROM members WHERE id = ?");
    $stmt->execute([$recipientMember['id']]);
    if (!$stmt->fetch()) {
        $stmt = $pdo->prepare("
            INSERT INTO members 
            (id, full_name, first_name, last_name, email, birth_date, status) 
            VALUES 
            (?, ?, ?, ?, ?, ?, 'active')
        ");
        $stmt->execute([
            $recipientMember['id'],
            $recipientMember['full_name'],
            $recipientMember['first_name'],
            $recipientMember['last_name'],
            $recipientMember['email'],
            $recipientMember['birth_date']
        ]);
        $recipientMemberId = $pdo->lastInsertId();
    }
    
    // Send a test notification
    $result = $birthdayReminder->sendMemberBirthdayNotifications($birthdayMemberId, $testTemplateId);
    
    echo "<h3>Notification Test Result</h3>";
    echo "<pre>" . print_r($result, true) . "</pre>";
    
    // Clean up the test template
    $stmt = $pdo->prepare("DELETE FROM email_templates WHERE id = ?");
    $stmt->execute([$testTemplateId]);
    
    echo "<p><a href='admin/send_birthday_emails.php'>Go to Birthday Email Sender</a></p>";
}

echo "<p><a href='admin/send_birthday_emails.php'>Go to Birthday Email Sender</a></p>"; 